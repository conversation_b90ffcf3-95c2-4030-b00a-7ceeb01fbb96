/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { resolveForwardRef } from '../../di/forward_ref';
import { InjectionToken } from '../../di/injection_token';
import { throwError } from '../../util/assert';
let _injectorProfilerContext;
export function getInjectorProfilerContext() {
    !ngDevMode && throwError('getInjectorProfilerContext should never be called in production mode');
    return _injectorProfilerContext;
}
export function setInjectorProfilerContext(context) {
    !ngDevMode && throwError('setInjectorProfilerContext should never be called in production mode');
    const previous = _injectorProfilerContext;
    _injectorProfilerContext = context;
    return previous;
}
let injectorProfilerCallback = null;
/**
 * Sets the callback function which will be invoked during certain DI events within the
 * runtime (for example: injecting services, creating injectable instances, configuring providers)
 *
 * Warning: this function is *INTERNAL* and should not be relied upon in application's code.
 * The contract of the function might be changed in any release and/or the function can be removed
 * completely.
 *
 * @param profiler function provided by the caller or null value to disable profiling.
 */
export const setInjectorProfiler = (injectorProfiler) => {
    !ngDevMode && throwError('setInjectorProfiler should never be called in production mode');
    injectorProfilerCallback = injectorProfiler;
};
/**
 * Injector profiler function which emits on DI events executed by the runtime.
 *
 * @param event InjectorProfilerEvent corresponding to the DI event being emitted
 */
function injectorProfiler(event) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    if (injectorProfilerCallback != null /* both `null` and `undefined` */) {
        injectorProfilerCallback(event);
    }
}
/**
 * Emits an InjectorProfilerEventType.ProviderConfigured to the injector profiler. The data in the
 * emitted event includes the raw provider, as well as the token that provider is providing.
 *
 * @param eventProvider A provider object
 */
export function emitProviderConfiguredEvent(eventProvider, isViewProvider = false) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    let token;
    // if the provider is a TypeProvider (typeof provider is function) then the token is the
    // provider itself
    if (typeof eventProvider === 'function') {
        token = eventProvider;
    }
    // if the provider is an injection token, then the token is the injection token.
    else if (eventProvider instanceof InjectionToken) {
        token = eventProvider;
    }
    // in all other cases we can access the token via the `provide` property of the provider
    else {
        token = resolveForwardRef(eventProvider.provide);
    }
    let provider = eventProvider;
    // Injection tokens may define their own default provider which gets attached to the token itself
    // as `ɵprov`. In this case, we want to emit the provider that is attached to the token, not the
    // token itself.
    if (eventProvider instanceof InjectionToken) {
        provider = eventProvider.ɵprov || eventProvider;
    }
    injectorProfiler({
        type: 2 /* InjectorProfilerEventType.ProviderConfigured */,
        context: getInjectorProfilerContext(),
        providerRecord: { token, provider, isViewProvider }
    });
}
/**
 * Emits an event to the injector profiler with the instance that was created. Note that
 * the injector associated with this emission can be accessed by using getDebugInjectContext()
 *
 * @param instance an object created by an injector
 */
export function emitInstanceCreatedByInjectorEvent(instance) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    injectorProfiler({
        type: 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */,
        context: getInjectorProfilerContext(),
        instance: { value: instance }
    });
}
/**
 * @param token DI token associated with injected service
 * @param value the instance of the injected service (i.e the result of `inject(token)`)
 * @param flags the flags that the token was injected with
 */
export function emitInjectEvent(token, value, flags) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    injectorProfiler({
        type: 0 /* InjectorProfilerEventType.Inject */,
        context: getInjectorProfilerContext(),
        service: { token, value, flags }
    });
}
export function runInInjectorProfilerContext(injector, token, callback) {
    !ngDevMode &&
        throwError('runInInjectorProfilerContext should never be called in production mode');
    const prevInjectContext = setInjectorProfilerContext({ injector, token });
    try {
        callback();
    }
    finally {
        setInjectorProfilerContext(prevInjectContext);
    }
}
//# sourceMappingURL=data:application/json;base64,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