/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { getClosureSafeProperty } from '../../util/property';
/**
 * Construct an injectable definition which defines how a token will be constructed by the DI
 * system, and in which injectors (if any) it will be available.
 *
 * This should be assigned to a static `ɵprov` field on a type, which will then be an
 * `InjectableType`.
 *
 * Options:
 * * `providedIn` determines which injectors will include the injectable, by either associating it
 *   with an `@NgModule` or other `InjectorType`, or by specifying that this injectable should be
 *   provided in the `'root'` injector, which will be the application-level injector in most apps.
 * * `factory` gives the zero argument function which will create an instance of the injectable.
 *   The factory can call [`inject`](api/core/inject) to access the `Injector` and request injection
 * of dependencies.
 *
 * @codeGenApi
 * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.
 */
export function ɵɵdefineInjectable(opts) {
    return {
        token: opts.token,
        providedIn: opts.providedIn || null,
        factory: opts.factory,
        value: undefined,
    };
}
/**
 * @deprecated in v8, delete after v10. This API should be used only by generated code, and that
 * code should now use ɵɵdefineInjectable instead.
 * @publicApi
 */
export const defineInjectable = ɵɵdefineInjectable;
/**
 * Construct an `InjectorDef` which configures an injector.
 *
 * This should be assigned to a static injector def (`ɵinj`) field on a type, which will then be an
 * `InjectorType`.
 *
 * Options:
 *
 * * `providers`: an optional array of providers to add to the injector. Each provider must
 *   either have a factory or point to a type which has a `ɵprov` static property (the
 *   type must be an `InjectableType`).
 * * `imports`: an optional array of imports of other `InjectorType`s or `InjectorTypeWithModule`s
 *   whose providers will also be added to the injector. Locally provided types will override
 *   providers from imports.
 *
 * @codeGenApi
 */
export function ɵɵdefineInjector(options) {
    return { providers: options.providers || [], imports: options.imports || [] };
}
/**
 * Read the injectable def (`ɵprov`) for `type` in a way which is immune to accidentally reading
 * inherited value.
 *
 * @param type A type which may have its own (non-inherited) `ɵprov`.
 */
export function getInjectableDef(type) {
    return getOwnDefinition(type, NG_PROV_DEF) || getOwnDefinition(type, NG_INJECTABLE_DEF);
}
export function isInjectable(type) {
    return getInjectableDef(type) !== null;
}
/**
 * Return definition only if it is defined directly on `type` and is not inherited from a base
 * class of `type`.
 */
function getOwnDefinition(type, field) {
    return type.hasOwnProperty(field) ? type[field] : null;
}
/**
 * Read the injectable def (`ɵprov`) for `type` or read the `ɵprov` from one of its ancestors.
 *
 * @param type A type which may have `ɵprov`, via inheritance.
 *
 * @deprecated Will be removed in a future version of Angular, where an error will occur in the
 *     scenario if we find the `ɵprov` on an ancestor only.
 */
export function getInheritedInjectableDef(type) {
    const def = type && (type[NG_PROV_DEF] || type[NG_INJECTABLE_DEF]);
    if (def) {
        ngDevMode &&
            console.warn(`DEPRECATED: DI is instantiating a token "${type.name}" that inherits its @Injectable decorator but does not provide one itself.\n` +
                `This will become an error in a future version of Angular. Please add @Injectable() to the "${type.name}" class.`);
        return def;
    }
    else {
        return null;
    }
}
/**
 * Read the injector def type in a way which is immune to accidentally reading inherited value.
 *
 * @param type type which may have an injector def (`ɵinj`)
 */
export function getInjectorDef(type) {
    return type && (type.hasOwnProperty(NG_INJ_DEF) || type.hasOwnProperty(NG_INJECTOR_DEF)) ?
        type[NG_INJ_DEF] :
        null;
}
export const NG_PROV_DEF = getClosureSafeProperty({ ɵprov: getClosureSafeProperty });
export const NG_INJ_DEF = getClosureSafeProperty({ ɵinj: getClosureSafeProperty });
// We need to keep these around so we can read off old defs if new defs are unavailable
export const NG_INJECTABLE_DEF = getClosureSafeProperty({ ngInjectableDef: getClosureSafeProperty });
export const NG_INJECTOR_DEF = getClosureSafeProperty({ ngInjectorDef: getClosureSafeProperty });
//# sourceMappingURL=data:application/json;base64,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