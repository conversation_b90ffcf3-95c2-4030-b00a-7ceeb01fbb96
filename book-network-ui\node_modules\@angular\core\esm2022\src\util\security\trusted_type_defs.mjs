/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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