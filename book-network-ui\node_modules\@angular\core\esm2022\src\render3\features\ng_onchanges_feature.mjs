/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { SimpleChange } from '../../interface/simple_change';
import { assertString } from '../../util/assert';
import { EMPTY_OBJ } from '../../util/empty';
/**
 * The NgOnChangesFeature decorates a component with support for the ngOnChanges
 * lifecycle hook, so it should be included in any component that implements
 * that hook.
 *
 * If the component or directive uses inheritance, the NgOnChangesFeature MUST
 * be included as a feature AFTER {@link InheritDefinitionFeature}, otherwise
 * inherited properties will not be propagated to the ngOnChanges lifecycle
 * hook.
 *
 * Example usage:
 *
 * ```
 * static ɵcmp = defineComponent({
 *   ...
 *   inputs: {name: 'publicName'},
 *   features: [NgOnChangesFeature]
 * });
 * ```
 *
 * @codeGenApi
 */
export function ɵɵNgOnChangesFeature() {
    return NgOnChangesFeatureImpl;
}
export function NgOnChangesFeatureImpl(definition) {
    if (definition.type.prototype.ngOnChanges) {
        definition.setInput = ngOnChangesSetInput;
    }
    return rememberChangeHistoryAndInvokeOnChangesHook;
}
// This option ensures that the ngOnChanges lifecycle hook will be inherited
// from superclasses (in InheritDefinitionFeature).
/** @nocollapse */
// tslint:disable-next-line:no-toplevel-property-access
ɵɵNgOnChangesFeature.ngInherit = true;
/**
 * This is a synthetic lifecycle hook which gets inserted into `TView.preOrderHooks` to simulate
 * `ngOnChanges`.
 *
 * The hook reads the `NgSimpleChangesStore` data from the component instance and if changes are
 * found it invokes `ngOnChanges` on the component instance.
 *
 * @param this Component instance. Because this function gets inserted into `TView.preOrderHooks`,
 *     it is guaranteed to be called with component instance.
 */
function rememberChangeHistoryAndInvokeOnChangesHook() {
    const simpleChangesStore = getSimpleChangesStore(this);
    const current = simpleChangesStore?.current;
    if (current) {
        const previous = simpleChangesStore.previous;
        if (previous === EMPTY_OBJ) {
            simpleChangesStore.previous = current;
        }
        else {
            // New changes are copied to the previous store, so that we don't lose history for inputs
            // which were not changed this time
            for (let key in current) {
                previous[key] = current[key];
            }
        }
        simpleChangesStore.current = null;
        this.ngOnChanges(current);
    }
}
function ngOnChangesSetInput(instance, value, publicName, privateName) {
    const declaredName = this.declaredInputs[publicName];
    ngDevMode && assertString(declaredName, 'Name of input in ngOnChanges has to be a string');
    const simpleChangesStore = getSimpleChangesStore(instance) ||
        setSimpleChangesStore(instance, { previous: EMPTY_OBJ, current: null });
    const current = simpleChangesStore.current || (simpleChangesStore.current = {});
    const previous = simpleChangesStore.previous;
    const previousChange = previous[declaredName];
    current[declaredName] = new SimpleChange(previousChange && previousChange.currentValue, value, previous === EMPTY_OBJ);
    instance[privateName] = value;
}
const SIMPLE_CHANGES_STORE = '__ngSimpleChanges__';
function getSimpleChangesStore(instance) {
    return instance[SIMPLE_CHANGES_STORE] || null;
}
function setSimpleChangesStore(instance, store) {
    return instance[SIMPLE_CHANGES_STORE] = store;
}
//# sourceMappingURL=data:application/json;base64,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