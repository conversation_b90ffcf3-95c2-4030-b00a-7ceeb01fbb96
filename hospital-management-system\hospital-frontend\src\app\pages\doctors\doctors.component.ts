import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-doctors',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-md me-2"></i>Doctor Management</h2>
        <button class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add New Doctor
        </button>
      </div>
      
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Specialization</th>
                  <th>Department</th>
                  <th>Experience</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>D001</td>
                  <td>Dr. <PERSON></td>
                  <td>Cardiology</td>
                  <td>Cardiology</td>
                  <td>15 years</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <!-- More doctor rows would be loaded dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class DoctorsComponent {}
