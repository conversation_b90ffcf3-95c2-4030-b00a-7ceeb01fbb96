/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isEnvironmentProviders } from '../di/interface/provider';
import { RuntimeError } from '../errors';
import { stringify } from '../util/stringify';
import { stringifyForError } from './util/stringify_utils';
/** Called when directives inject each other (creating a circular dependency) */
export function throwCyclicDependencyError(token, path) {
    const depPath = path ? `. Dependency path: ${path.join(' > ')} > ${token}` : '';
    throw new RuntimeError(-200 /* RuntimeErrorCode.CYCLIC_DI_DEPENDENCY */, `Circular dependency in DI detected for ${token}${depPath}`);
}
export function throwMixedMultiProviderError() {
    throw new Error(`Cannot mix multi providers and regular providers`);
}
export function throwInvalidProviderError(ngModuleType, providers, provider) {
    if (ngModuleType && providers) {
        const providerDetail = providers.map(v => v == provider ? '?' + provider + '?' : '...');
        throw new Error(`Invalid provider for the NgModule '${stringify(ngModuleType)}' - only instances of Provider and Type are allowed, got: [${providerDetail.join(', ')}]`);
    }
    else if (isEnvironmentProviders(provider)) {
        if (provider.ɵfromNgModule) {
            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers from 'importProvidersFrom' present in a non-environment injector. 'importProvidersFrom' can't be used for component providers.`);
        }
        else {
            throw new RuntimeError(207 /* RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT */, `Invalid providers present in a non-environment injector. 'EnvironmentProviders' can't be used for component providers.`);
        }
    }
    else {
        throw new Error('Invalid provider');
    }
}
/** Throws an error when a token is not found in DI. */
export function throwProviderNotFoundError(token, injectorName) {
    const injectorDetails = injectorName ? ` in ${injectorName}` : '';
    throw new RuntimeError(-201 /* RuntimeErrorCode.PROVIDER_NOT_FOUND */, ngDevMode && `No provider for ${stringifyForError(token)} found${injectorDetails}`);
}
//# sourceMappingURL=data:application/json;base64,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