/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDefined, throwError } from '../util/assert';
import { toTNodeTypeAsString } from './interfaces/node';
export function assertTNodeType(tNode, expectedTypes, message) {
    assertDefined(tNode, 'should be called with a TNode');
    if ((tNode.type & expectedTypes) === 0) {
        throwError(message ||
            `Expected [${toTNodeTypeAsString(expectedTypes)}] but got ${toTNodeTypeAsString(tNode.type)}.`);
    }
}
export function assertPureTNodeType(type) {
    if (!(type === 2 /* TNodeType.Element */ || //
        type === 1 /* TNodeType.Text */ || //
        type === 4 /* TNodeType.Container */ || //
        type === 8 /* TNodeType.ElementContainer */ || //
        type === 32 /* TNodeType.Icu */ || //
        type === 16 /* TNodeType.Projection */ || //
        type === 64 /* TNodeType.Placeholder */)) {
        throwError(`Expected TNodeType to have only a single type selected, but got ${toTNodeTypeAsString(type)}.`);
    }
}
//# sourceMappingURL=data:application/json;base64,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