# Hospital Management System

A comprehensive hospital management system built with Spring Boot 3, Spring Security 6, Angular, and Key<PERSON>loak for authentication and authorization.

## Features

- **User Management**: Registration and authentication for doctors, nurses, and administrators
- **Role-Based Access Control**: Different permissions for doctors, nurses, and admin staff
- **Patient Management**: Create, update, and manage patient records
- **Appointment Scheduling**: Book and manage appointments
- **Medical Records**: Secure storage and access to medical histories
- **JWT Token Authentication**: Secure communication between client and server
- **Email Notifications**: Automated email notifications for appointments and updates

## Technology Stack

### Backend
- **Spring Boot 3**: Framework for building the REST API
- **Spring Security 6**: Authentication and authorization
- **Spring Data JPA**: Data persistence layer
- **PostgreSQL**: Primary database
- **Keycloak**: Identity and access management
- **JWT**: Token-based authentication
- **Maven**: Dependency management

### Frontend
- **Angular**: Frontend framework
- **Bootstrap**: UI styling
- **Keycloak Angular**: Keycloak integration for Angular

## Architecture

The system follows a microservices architecture with:
- Backend API (Spring Boot)
- Frontend Application (Angular)
- Authentication Server (Keycloak)
- Database (PostgreSQL)
- Email Service (MailDev for development)

## Setup Instructions

1. **Clone the repository**
2. **Start the infrastructure services**:
   ```bash
   docker-compose up -d
   ```
3. **Run the backend**:
   ```bash
   cd hospital-backend
   mvn clean install
   mvn spring-boot:run
   ```
4. **Run the frontend**:
   ```bash
   cd hospital-frontend
   npm install
   ng serve
   ```

## Default Access

- **Keycloak Admin Console**: http://localhost:9090 (admin/admin)
- **Backend API**: http://localhost:8088
- **Frontend Application**: http://localhost:4200
- **MailDev**: http://localhost:1080

## User Roles

- **ADMIN**: Full system access
- **DOCTOR**: Patient management, medical records, appointments
- **NURSE**: Patient care, basic record updates, appointment assistance
- **PATIENT**: View own records, book appointments

## API Documentation

Once the backend is running, access the API documentation at:
http://localhost:8088/api/v1/swagger-ui/index.html
