/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Symbol used to tell `Signal`s apart from other functions.
 *
 * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.
 */
export const SIGNAL = /* @__PURE__ */ Symbol('SIGNAL');
/**
 * Checks if the given `value` is a reactive `Signal`.
 *
 * @developerPreview
 */
export function isSignal(value) {
    return typeof value === 'function' && value[SIGNAL] !== undefined;
}
/**
 * The default equality function used for `signal` and `computed`, which treats objects and arrays
 * as never equal, and all other primitive values using identity semantics.
 *
 * This allows signals to hold non-primitive values (arrays, objects, other collections) and still
 * propagate change notification upon explicit mutation without identity change.
 *
 * @developerPreview
 */
export function defaultEquals(a, b) {
    // `Object.is` compares two values using identity semantics which is desired behavior for
    // primitive values. If `Object.is` determines two values to be equal we need to make sure that
    // those don't represent objects (we want to make sure that 2 objects are always considered
    // "unequal"). The null check is needed for the special case of JavaScript reporting null values
    // as objects (`typeof null === 'object'`).
    return (a === null || typeof a !== 'object') && Object.is(a, b);
}
//# sourceMappingURL=data:application/json;base64,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