package com.hospital.technician;

import com.hospital.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "technician")
public class Technician extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String licenseNumber;
    private String specialization; // Radiology Tech, Surgical Tech, EKG Tech, etc.
    private String department;
    private Integer yearsOfExperience;
    private String shift; // DAY, NIGHT, ROTATING
    
    @Enumerated(EnumType.STRING)
    private TechnicianStatus status;
    
    // Technician-specific fields
    private String certifications; // ARRT, NBSTSA, etc.
    private String equipmentCertifications; // Specific equipment they're certified on
    private String procedureTypes; // Types of procedures they can assist with
    private Boolean canWorkInSterileEnvironment;
    private Boolean canOperateAdvancedEquipment;
    private String supervisorId; // Reference to supervising staff

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum TechnicianStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    SUSPENDED,
    RETIRED
}
