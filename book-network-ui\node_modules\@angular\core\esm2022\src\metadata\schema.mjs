/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Defines a schema that allows an NgModule to contain the following:
 * - Non-Angular elements named with dash case (`-`).
 * - Element properties named with dash case (`-`).
 * Dash case is the naming convention for custom elements.
 *
 * @publicApi
 */
export const CUSTOM_ELEMENTS_SCHEMA = {
    name: 'custom-elements'
};
/**
 * Defines a schema that allows any property on any element.
 *
 * This schema allows you to ignore the errors related to any unknown elements or properties in a
 * template. The usage of this schema is generally discouraged because it prevents useful validation
 * and may hide real errors in your template. Consider using the `CUSTOM_ELEMENTS_SCHEMA` instead.
 *
 * @publicApi
 */
export const NO_ERRORS_SCHEMA = {
    name: 'no-errors-schema'
};
//# sourceMappingURL=data:application/json;base64,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