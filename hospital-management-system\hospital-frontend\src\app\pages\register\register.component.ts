import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid vh-100">
      <div class="row h-100">
        <div class="col-md-6 d-flex align-items-center justify-content-center bg-success">
          <div class="text-center text-white">
            <h1 class="display-4 mb-4">
              <i class="fas fa-user-md me-3"></i>
              Join Our Team
            </h1>
            <p class="lead">Register as a healthcare professional</p>
            <div class="mt-4">
              <div class="mb-3">
                <i class="fas fa-stethoscope me-2"></i>
                <span>Doctors</span>
              </div>
              <div class="mb-3">
                <i class="fas fa-user-nurse me-2"></i>
                <span>Nurses</span>
              </div>
              <div class="mb-3">
                <i class="fas fa-user-cog me-2"></i>
                <span>Administrators</span>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 d-flex align-items-center justify-content-center">
          <div class="card shadow-lg" style="width: 400px;">
            <div class="card-body p-5">
              <div class="text-center mb-4">
                <h3 class="card-title">Create Account</h3>
                <p class="text-muted">Join our healthcare system</p>
              </div>
              
              <div class="d-grid gap-2">
                <button 
                  class="btn btn-success btn-lg"
                  (click)="register()"
                  [disabled]="isLoading">
                  <i class="fas fa-user-plus me-2"></i>
                  <span *ngIf="!isLoading">Create Account</span>
                  <span *ngIf="isLoading">
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    Creating Account...
                  </span>
                </button>
                
                <button 
                  class="btn btn-outline-secondary"
                  (click)="goToLogin()"
                  [disabled]="isLoading">
                  <i class="fas fa-sign-in-alt me-2"></i>
                  Already have an account?
                </button>
              </div>
              
              <div class="mt-4 text-center">
                <small class="text-muted">
                  By registering, you agree to our terms of service
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .bg-success {
      background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
    }
    
    .card {
      border: none;
      border-radius: 15px;
    }
    
    .btn {
      border-radius: 10px;
      padding: 12px 20px;
    }
    
    .fas {
      width: 20px;
    }
  `]
})
export class RegisterComponent implements OnInit {
  isLoading = false;

  constructor(
    private keycloakService: KeycloakService,
    private router: Router
  ) {}

  async ngOnInit() {
    const isLoggedIn = await this.keycloakService.isLoggedIn();
    if (isLoggedIn) {
      this.router.navigate(['/dashboard']);
    }
  }

  async register() {
    this.isLoading = true;
    try {
      await this.keycloakService.register({
        redirectUri: window.location.origin + '/dashboard'
      });
    } catch (error) {
      console.error('Registration failed:', error);
      this.isLoading = false;
    }
  }

  goToLogin() {
    this.router.navigate(['/login']);
  }
}
