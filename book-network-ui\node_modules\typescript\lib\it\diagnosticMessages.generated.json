{"ALL_COMPILER_OPTIONS_6917": "TUTTE LE OPZIONI DEL COMPILATORE", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Non è possibile usare un modificatore '{0}' con una dichiarazione di importazione.", "A_0_parameter_must_be_the_first_parameter_2680": "Il primo parametro deve essere '{0}'.", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "Un commento '@typedef' di JSDoc non può contenere più tag '@type'.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Un valore letterale bigint non può usare la notazione esponenziale.", "A_bigint_literal_must_be_an_integer_1353": "Un valore letterale bigint deve essere un numero intero.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Un parametro del criterio di binding non può essere facoltativo in una firma di implementazione.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Un'istruzione 'break' può essere usata solo all'interno di un'iterazione di inclusione o di un'istruzione switch.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Un'istruzione 'break' può solo passare a un'etichetta di un'istruzione di inclusione.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "Una classe può implementare solo un identificatore/nome qualificato con argomenti tipo facoltativi.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "Una classe può implementare solo un tipo di oggetto o un'intersezione di tipi di oggetto con membri noti in modo statico.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "È necessario assegnare un nome a una dichiarazione di classe senza modificatore 'default'.", "A_class_member_cannot_have_the_0_keyword_1248": "Un membro di classe non può contenere la parola chiave '{0}'.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "Non sono consentite espressioni con virgole in un nome di proprietà calcolato.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Un nome di proprietà calcolato non può fare riferimento a un parametro di tipo dal tipo che lo contiene.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Un nome di proprietà calcolato in una dichiarazione di proprietà di classe deve avere un tipo di valore letterale semplice o un tipo 'unique symbol'.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Un nome di proprietà calcolato in un overload di metodo deve fare riferimento a un'espressione il cui tipo è un tipo di valore letterale o un tipo 'unique symbol'.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Un nome di proprietà calcolato in un valore letterale di tipo deve fare riferimento a un'espressione il cui tipo è un tipo di valore letterale o un tipo 'unique symbol'.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Un nome di proprietà calcolato in un contesto di ambiente deve fare riferimento a un'espressione il cui tipo è un tipo di valore letterale o un tipo 'unique symbol'.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Un nome di proprietà calcolato in un'interfaccia deve fare riferimento a un'espressione il cui tipo è un tipo di valore letterale o un tipo 'unique symbol'.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Un nome di proprietà calcolato deve essere di tipo 'string', 'number', 'symbol' o 'any'.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "Le asserzioni 'const' possono essere applicate solo a riferimenti a membri di enumerazione oppure a valori letterali stringa, numerico, booleano, di oggetto o matrice.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "È possibile accedere a un membro di enumerazione const solo tramite un valore letterale stringa.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Un inizializzatore 'const' in un contesto di ambiente deve essere un valore letterale numerico o stringa oppure un riferimento a un'enumerazione di valori letterali.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Un costruttore non può contenere una chiamata 'super' quando la relativa classe estende 'null'.", "A_constructor_cannot_have_a_this_parameter_2681": "Un costruttore non può contenere un parametro 'this'.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Un'istruzione 'continue' può essere usata solo all'interno di un'istruzione di iterazione di inclusione.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Un'istruzione 'continue' può solo passare a un'etichetta di un'istruzione di iterazione di inclusione.", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Non è possibile usare un modificatore 'declare' in un contesto già di ambiente.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Un elemento Decorator può solo decorare un'implementazione del metodo e non un overload.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "Una clausola 'default' non può essere specificata più volte in un'istruzione 'switch'.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "È possibile usare un'esportazione predefinita solo in un modulo di tipo ECMAScript.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Un'esportazione predefinita deve essere al livello principale di una dichiarazione di file o di modulo.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "In questo contesto non sono consentite asserzioni di assegnazione definite '!'.", "A_destructuring_declaration_must_have_an_initializer_1182": "Una dichiarazione di destrutturazione deve includere un inizializzatore.", "A_dynamic_import_call_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_declarat_2712": "Con una chiamata di importazione dinamica in ES5/ES3 è necessario il costruttore 'Promise'. Assicurarsi che sia presente una dichiarazione per il costruttore 'Promise' oppure includere 'ES2015' nell'opzione '--lib'.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Una chiamata di importazione dinamica restituisce un costruttore 'Promise'. Assicurarsi che sia presente una dichiarazione per 'Promise' oppure includere 'ES2015' nell'opzione '--lib'.", "A_file_cannot_have_a_reference_to_itself_1006": "Un file non può contenere un riferimento a se stesso.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "Una funzione che restituisce 'never' non può includere un punto finale raggiungibile.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "Una funzione chiamata con la parola chiave 'new' non può contenere un tipo 'this' con valore 'void'.", "A_function_whose_declared_type_is_neither_void_nor_any_must_return_a_value_2355": "Una funzione il cui tipo dichiarato non è 'void' né 'any' deve restituire un valore.", "A_generator_cannot_have_a_void_type_annotation_2505": "Un generatore non può contenere un'annotazione di tipo 'void'.", "A_get_accessor_cannot_have_parameters_1054": "Una funzione di accesso 'get' non può contenere parametri.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Una funzione di accesso get deve essere accessibile almeno come setter", "A_get_accessor_must_return_a_value_2378": "Una funzione di accesso 'get' deve restituire un valore.", "A_label_is_not_allowed_here_1344": "In questo punto non sono consentite etichette.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Un elemento tupla con etichetta è dichiarato come facoltativo con un punto interrogativo dopo il nome e prima dei due punti, anziché dopo il tipo.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Un elemento tupla con etichetta è dichiarato come inattivo con '...' prima del nome, anziché prima del tipo.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Un tipo di cui è stato eseguito il mapping non può dichiarare proprietà o metodi.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Un inizializzatore di membro in una dichiarazione di enumerazione non può fare riferimento a membri dichiarati successivamente, inclusi quelli definiti in altre enumerazioni.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Una classe mixin deve includere un costruttore con un unico parametro REST di tipo 'any[]'.", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "Una classe mixin estesa da una variabile di tipo contenente una firma del costrutto astratta deve essere dichiarata anche come 'abstract'.", "A_module_cannot_have_multiple_default_exports_2528": "Un modulo non può includere più esportazioni predefinite.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Una dichiarazione di spazio dei nomi non può essere presente in un file diverso rispetto a una classe o funzione con cui è stato eseguito il merge.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Una dichiarazione di spazio dei nomi non può essere specificata prima di una classe o funzione con cui è stato eseguito il merge.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Una dichiarazione di spazio dei nomi è consentita solo al livello superiore di uno spazio dei nomi o di un modulo.", "A_non_dry_build_would_build_project_0_6357": "Se si esegue una compilazione senza flag -dry, verrà compilato il progetto '{0}'", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "Se si esegue una compilazione senza flag -dry, i file seguenti verranno eliminati: {0}", "A_non_dry_build_would_update_output_of_project_0_6375": "Se si esegue una compilazione non di prova, l'output del progetto '{0}' verrà aggiornato", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Se si esegue una compilazione non di prova, i timestamp dell'output del progetto '{0}' verranno aggiornati", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Un inizializzatore di parametro è consentito solo in un'implementazione di funzione o costruttore.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Non è possibile dichiarare una proprietà di parametro usando un parametro REST.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Una proprietà di parametro è consentita solo in un'implementazione di costruttore.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Non è possibile dichiarare una proprietà di parametro con un modello di associazione.", "A_promise_must_have_a_then_method_1059": "Una promessa deve contenere un metodo 'then'.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Una proprietà di una classe il cui tipo è un tipo 'unique symbol' deve essere sia 'static' che 'readonly'.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Una proprietà di un'interfaccia o di un valore letterale di tipo il cui tipo è un tipo 'unique symbol' deve essere 'readonly'.", "A_required_element_cannot_follow_an_optional_element_1257": "Non è possibile specificare un elemento obbligatorio dopo un elemento facoltativo.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Un parametro obbligatorio non può seguire un parametro facoltativo.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Un elemento rest non può contenere un criterio di binding.", "A_rest_element_cannot_follow_another_rest_element_1265": "Non è possibile specificare un elemento REST dopo un altro elemento REST.", "A_rest_element_cannot_have_a_property_name_2566": "Un elemento rest non può contenere un nome proprietà.", "A_rest_element_cannot_have_an_initializer_1186": "Un elemento rest non può includere un inizializzatore.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Un elemento rest deve essere l'ultimo di un criterio di destrutturazione.", "A_rest_element_type_must_be_an_array_type_2574": "Un tipo di elemento rest deve essere un tipo di matrice.", "A_rest_parameter_cannot_be_optional_1047": "Un parametro rest non può essere facoltativo.", "A_rest_parameter_cannot_have_an_initializer_1048": "Un parametro rest non può contenere un inizializzatore.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Un parametro rest deve essere l'ultimo di un elenco di parametri.", "A_rest_parameter_must_be_of_an_array_type_2370": "Un parametro rest deve essere di un tipo di matrice.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Un modello di associazione o un parametro REST non può contenere una virgola finale.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Un'istruzione 'return' può essere usata solo all'interno di un corpo di funzione.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Non è possibile usare un'istruzione 'return' all'interno di un blocco statico di classe.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "Serie di voci che ripetono il mapping delle importazioni a percorsi di ricerca relativi al valore di 'baseUrl'.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Una funzione di accesso 'set' non può contenere un'annotazione di tipo restituito.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Una funzione di accesso 'set' non può contenere un parametro facoltativo.", "A_set_accessor_cannot_have_rest_parameter_1053": "Una funzione di accesso 'set' non può contenere il parametro rest.", "A_set_accessor_must_have_exactly_one_parameter_1049": "Una funzione di accesso 'set' deve contenere un solo parametro.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Un parametro della funzione di accesso 'set' non può contenere un inizializzatore.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Un argomento spread deve avere un tipo di tupla o essere passato a un parametro rest.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Una chiamata 'super' deve essere un'istruzione a livello radice all'interno di un costruttore di una classe derivata che contiene proprietà inizializzate, proprietà dei parametri o identificatori privati.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Una chiamata 'super' deve essere la prima istruzione del costruttore a fare riferimento a 'super' o 'this' quando una classe derivata contiene proprietà inizializzate, proprietà di parametri o identificatori privati.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Un guard di tipo basato su 'this' non è compatibile con uno basato su parametri.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "Un tipo 'this' è disponibile solo in un membro non statico di una classe o di interfaccia.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "Un file 'tsconfig.json' è già definito in: '{0}'.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Un membro di tupla non può essere sia facoltativo che inattivo.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Un tipo di tupla non può essere indicizzato con un valore negativo.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "Nella parte sinistra di un'espressione di elevamento a potenza non è consentita un'espressione di asserzione tipi. Provare a racchiudere l'espressione tra parentesi.", "A_type_literal_property_cannot_have_an_initializer_1247": "Una proprietà di valore letterale di tipo non può contenere un inizializzatore.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Un'importazione solo di tipi può specificare un'importazione predefinita o binding denominati, ma non entrambi.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "Un predicato di tipo non può fare riferimento a un parametro rest.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "Un predicato di tipo non può fare riferimento all'elemento '{0}' in un criterio di binding.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "Un predicato di tipo è consentito solo nella posizione del tipo restituito per le funzioni e i metodi.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "Il tipo di un predicato di tipo deve essere assegnabile al tipo del relativo parametro.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "Un tipo a cui viene fatto riferimento in una firma decorata deve essere importato con 'import type' o un'importazione dello spazio dei nomi quando sono abilitati 'isolatedModules' e 'emitDecoratorMetadata'.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "Una variabile il cui tipo è un tipo 'unique symbol' deve essere 'const'.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Un'espressione 'yield' è consentita solo nel corpo di un generatore.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "Non è possibile accedere al metodo astratto '{0}' nella classe '{1}' tramite l'espressione super.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "I metodi astratti possono essere inclusi solo in una classe astratta.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "Non è possibile accedere alla proprietà astratta '{0}' nella classe '{1}' nel costruttore.", "Accessibility_modifier_already_seen_1028": "Il modificatore di accessibilità è già presente.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Le funzioni di accesso sono disponibili solo se destinate a ECMAScript 5 e versioni successive.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Le funzioni di accesso devono essere tutte astratte o tutte non astratte.", "Add_0_to_unresolved_variable_90008": "Aggiungere '{0}.' alla variabile non risolta", "Add_a_return_statement_95111": "Aggiungere un'istruzione return", "Add_all_missing_async_modifiers_95041": "Aggiungere tutti i modificatori 'async' mancanti", "Add_all_missing_attributes_95168": "Aggiungi tutti gli attributi mancanti", "Add_all_missing_call_parentheses_95068": "Aggiungere tutte le parentesi mancanti nelle chiamate", "Add_all_missing_function_declarations_95157": "Aggiungere tutte le dichiarazioni di funzione mancanti", "Add_all_missing_imports_95064": "Aggiungere tutte le importazioni mancanti", "Add_all_missing_members_95022": "Aggiungere tutti i membri mancanti", "Add_all_missing_override_modifiers_95162": "Aggiungere tutti i modificatori 'override' mancanti", "Add_all_missing_properties_95166": "Aggiunge tutte le proprietà mancanti", "Add_all_missing_return_statement_95114": "Aggiungere tutte le istruzioni return mancanti", "Add_all_missing_super_calls_95039": "Aggiungere tutte le chiamate a super mancanti", "Add_async_modifier_to_containing_function_90029": "Aggiungere il modificatore async alla funzione contenitore", "Add_await_95083": "Aggiungere 'await'", "Add_await_to_initializer_for_0_95084": "Aggiungere 'await' all'inizializzatore per '{0}'", "Add_await_to_initializers_95089": "Aggiungere 'await' agli inizializzatori", "Add_braces_to_arrow_function_95059": "Aggiungere le parentesi graffe alla funzione arrow", "Add_const_to_all_unresolved_variables_95082": "Aggiungere 'const' a tutte le variabili non risolte", "Add_const_to_unresolved_variable_95081": "Aggiungere 'const' alla variabile non risolta", "Add_definite_assignment_assertion_to_property_0_95020": "Aggiungere l'asserzione di assegnazione definita alla proprietà '{0}'", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Aggiungere le asserzioni di assegnazione definite a tutte le proprietà non inizializzate", "Add_export_to_make_this_file_into_a_module_95097": "Aggiungere 'export {}' per trasformare questo file in un modulo", "Add_extends_constraint_2211": "Aggiungere il vincolo 'extends'.", "Add_extends_constraint_to_all_type_parameters_2212": "Aggiungere il vincolo `extends` a tutti i parametri di tipo", "Add_import_from_0_90057": "Aggiungere l'importazione da \"{0}\"", "Add_index_signature_for_property_0_90017": "Aggiungere la firma dell'indice per la proprietà '{0}'", "Add_initializer_to_property_0_95019": "Aggiungere l'inizializzatore alla proprietà '{0}'", "Add_initializers_to_all_uninitialized_properties_95027": "Aggiungere gli inizializzatori a tutte le proprietà non inizializzate", "Add_missing_attributes_95167": "Aggiungi attributi mancanti", "Add_missing_call_parentheses_95067": "Aggiu<PERSON><PERSON> le parentesi mancanti nelle chiamate", "Add_missing_enum_member_0_95063": "Aggiungere il membro di enumerazione mancante '{0}'", "Add_missing_function_declaration_0_95156": "Aggiungere la dichiarazione di funzione mancante '{0}'", "Add_missing_new_operator_to_all_calls_95072": "Aggiungere l'operatore mancante 'new' a tutte le chiamate", "Add_missing_new_operator_to_call_95071": "Aggiungere l'operatore mancante 'new' alla chiamata", "Add_missing_properties_95165": "Aggiunge le proprietà mancanti", "Add_missing_super_call_90001": "Aggiungere la chiamata mancante a 'super()'", "Add_missing_typeof_95052": "Aggiungere l'elemento 'typeof' mancante", "Add_names_to_all_parameters_without_names_95073": "Aggiu<PERSON><PERSON> i nomi a tutti i parametri senza nomi", "Add_or_remove_braces_in_an_arrow_function_95058": "Aggiungere o rimuovere le parentesi graffe in una funzione arrow", "Add_override_modifier_95160": "Aggiungere il modificatore 'override'", "Add_parameter_name_90034": "Aggiungere il nome del parametro", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Aggiungere il qualificatore a tutte le variabili non risolte corrispondenti a un nome di membro", "Add_to_all_uncalled_decorators_95044": "Aggiungere '()' a tutti gli elementi Decorator non chiamati", "Add_ts_ignore_to_all_error_messages_95042": "Aggiungere '@ts-ignore' a tutti i messaggi di errore", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "Aggiunge 'undefined' a un tipo quando l'accesso viene eseguito tramite un indice.", "Add_undefined_to_optional_property_type_95169": "Aggiungi 'undefined' al tipo di proprietà facoltativo", "Add_undefined_type_to_all_uninitialized_properties_95029": "Aggiungere il tipo non definito a tutte le proprietà non inizializzate", "Add_undefined_type_to_property_0_95018": "Aggiungere il tipo 'undefined' alla proprietà '{0}'", "Add_unknown_conversion_for_non_overlapping_types_95069": "Aggiungere la conversione 'unknown' per i tipi non sovrapposti", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Aggiungere 'unknown' a tutte le conversioni di tipi non sovrapposti", "Add_void_to_Promise_resolved_without_a_value_95143": "Aggiungere 'void' all'elemento Promise risolto senza un valore", "Add_void_to_all_Promises_resolved_without_a_value_95144": "Aggiu<PERSON><PERSON> 'void' a tutti gli elementi Promise risolti senza un valore", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Aggiungere un file tsconfig.json per organizzare più facilmente progetti che contengono sia file TypeScript che JavaScript. Per altre informazioni, vedere https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Tutte le dichiarazioni di '{0}' devono avere vincoli identici.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Tutte le dichiarazioni di '{0}' devono contenere modificatori identici.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Tutte le dichiarazioni di '{0}' devono contenere parametri di tipo identici.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Tutte le dichiarazioni di un metodo astratto devono essere consecutive.", "All_destructured_elements_are_unused_6198": "<PERSON><PERSON> gli elementi destrutturati sono inutilizzati.", "All_imports_in_import_declaration_are_unused_6192": "Tutte le importazioni nella dichiarazione di importazione sono inutilizzate.", "All_type_parameters_are_unused_6205": "Tutti i parametri di tipo sono inutilizzati.", "All_variables_are_unused_6199": "Tutte le variabili sono inutilizzate.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "Consente l'uso di file JavaScript nel programma. Usare l'opzione 'checkJS' per ottenere gli errori da questi file.", "Allow_accessing_UMD_globals_from_modules_6602": "Consentire l'accesso alle istruzioni globali UMD dai moduli.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Consente di eseguire importazioni predefinite da moduli senza esportazione predefinita. Non influisce sulla creazione del codice ma solo sul controllo dei tipi.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Consente 'import x from y' quando un modulo non contiene un'esportazione predefinita.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Consente di eseguire una volta per progetto l'importazione di funzioni helper da tslib, invece di includerle per ogni file.", "Allow_javascript_files_to_be_compiled_6102": "Consente la compilazione di file JavaScript.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Consente che più cartelle vengano considerate come una sola durante la risoluzione dei moduli.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "Il nome file già incluso '{0}' differisce da quello '{1}' solo per l'uso di maiuscole/minuscole.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "Non è possibile specificare il nome di modulo relativo nella dichiarazione di modulo di ambiente.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "I moduli di ambiente non possono essere annidati in altri moduli o spazi dei nomi.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Un modulo AMD non può includere più assegnazioni di nome.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Una funzione di accesso astratta non può contenere un'implementazione.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "Non è possibile usare un modificatore di accessibilità con un identificatore privato.", "An_accessor_cannot_have_type_parameters_1094": "Una funzione di accesso non può contenere parametri di tipo.", "An_accessor_property_cannot_be_declared_optional_1276": "Una proprietà 'accessor' non può essere dichiarata facoltativa.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Una dichiarazione di modulo di ambiente è consentita solo al primo livello in un file.", "An_argument_for_0_was_not_provided_6210": "Non è stato specificato alcun argomento per '{0}'.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "Non è stato specificato alcun argomento corrispondente a questo modello di associazione.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Un operando aritmetico deve essere di tipo 'any', 'number', 'bigint' o un tipo enumerazione.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Una funzione arrow non può contenere un parametro 'this'.", "An_async_function_or_method_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_de_2705": "Con una funzione o un metodo asincrono in ES5/ES3 è necessario il costruttore 'Promise'.  Assicurarsi che sia presente una dichiarazione per il costruttore 'Promise' oppure includere 'ES2015' nell'opzione '--lib'.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Un metodo o una funzione asincrona deve restituire un costruttore 'Promise'. Assicurarsi che sia presente una dichiarazione per 'Promise' oppure includere 'ES2015' nell'opzione '--lib'.", "An_async_iterator_must_have_a_next_method_2519": "Un iteratore asincrono deve contenere un metodo 'next()'.", "An_element_access_expression_should_take_an_argument_1011": "Un'espressione di accesso a elementi deve accettare un argomento.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "Non è possibile assegnare un nome con un identificatore privato a un membro di enumerazione.", "An_enum_member_cannot_have_a_numeric_name_2452": "Il nome di un membro di enumerazione non può essere numerico.", "An_enum_member_name_must_be_followed_by_a_or_1357": "Il nome di un membro di enumerazione deve essere seguito da ',', '=' o '}'.", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Versione espansa di queste informazioni, che mostra tutte le opzioni possibili del compilatore", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Non è possibile usare un'assegnazione di esportazione in un modulo con altri elementi esportati.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Non è possibile usare un'assegnazione di esportazione in uno spazio dei nomi.", "An_export_assignment_cannot_have_modifiers_1120": "Un'assegnazione di esportazione non può contenere modificatori.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Un'assegnazione di esportazione deve essere al primo livello di una dichiarazione di file o di modulo.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Una dichiarazione di esportazione può essere usata solo al livello superiore di un modulo.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Una dichiarazione di esportazione può essere usata solo al livello superiore di uno spazio dei nomi o di un modulo.", "An_export_declaration_cannot_have_modifiers_1193": "Una dichiarazione di esportazione non può contenere modificatori.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "Non è possibile testare la veridicità di un'espressione di tipo 'void'.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Un valore di escape Unicode avanzato deve essere compreso tra 0x0 e 0x10FFFF inclusi.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Non è possibile specificare un identificatore o una parola chiave subito dopo un valore letterale numerico.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Non è possibile dichiarare un'implementazione in contesti di ambiente.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Un alias di importazione non può fare riferimento a una dichiarazione esportata con 'export type'.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Un alias di importazione non può fare riferimento a una dichiarazione importata con 'import type'.", "An_import_alias_cannot_use_import_type_1392": "Un alias di importazione non può usare 'import type'", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Una dichiarazione di importazione può essere usata solo al livello superiore di un modulo.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Una dichiarazione di importazione può essere usata solo al livello superiore di uno spazio dei nomi o di un modulo.", "An_import_declaration_cannot_have_modifiers_1191": "Una dichiarazione di importazione non può contenere modificatori.", "An_import_path_cannot_end_with_a_0_extension_Consider_importing_1_instead_2691": "Un percorso di importazione non può terminare con l'estensione '{0}'. In alternativa, provare a importare '{1}'.", "An_index_signature_cannot_have_a_rest_parameter_1017": "Una firma dell'indice non può contenere un parametro rest.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Una firma dell'indice non può contenere una virgola finale.", "An_index_signature_must_have_a_type_annotation_1021": "Una firma dell'indice deve contenere un'annotazione di tipo.", "An_index_signature_must_have_exactly_one_parameter_1096": "Una firma dell'indice deve contenere un solo parametro.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Un parametro della firma dell'indice non può contenere un punto interrogativo.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Un parametro della firma dell'indice non può contenere un modificatore di accessibilità.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Un parametro della firma dell'indice non può contenere un inizializzatore.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "Un parametro della firma dell'indice deve contenere un'annotazione di tipo.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Un tipo di parametro della firma dell'indice non può essere un tipo di valore letterale o un tipo generico. Considerare l'utilizzo di un tipo di oggetto con mapping.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "Un tipo di parametro della firma dell'indice deve essere 'stringa', 'numero', 'simbolo' o un tipo di valore letterale del modello.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Un'espressione di creazione di un'istanza non può essere seguita da un accesso a proprietà.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "Un'interfaccia può estendere solo un identificatore/nome qualificato con argomenti tipo facoltativi.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Un'interfaccia può estendere solo un tipo di oggetto o un'intersezione di tipi di oggetto con membri noti in modo statico.", "An_interface_cannot_extend_a_primitive_type_like_0_an_interface_can_only_extend_named_types_and_clas_2840": "Un'interfaccia non può estendere un tipo primitivo come '{0}'; un'interfaccia può estendere solo tipi e classi denominati", "An_interface_property_cannot_have_an_initializer_1246": "Una proprietà di interfaccia non può contenere un inizializzatore.", "An_iterator_must_have_a_next_method_2489": "Un iteratore deve contenere un metodo 'next()'.", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "Quando si usa un'istruzione @jsx con frammenti JSX, è necessaria un'istruzione pragma @jsxFrag.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Un valore letterale di oggetto non può contenere più funzioni di accesso get/set con lo stesso nome.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Un valore letterale di oggetto non può contenere più proprietà con lo stesso nome.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Un valore letterale di oggetto non può contenere proprietà e funzioni di accesso con lo stesso nome.", "An_object_member_cannot_be_declared_optional_1162": "Un membro di oggetto non può essere dichiarato come facoltativo.", "An_optional_chain_cannot_contain_private_identifiers_18030": "Una catena facoltativa non può contenere identificatori privati.", "An_optional_element_cannot_follow_a_rest_element_1266": "Non è possibile specificare un elemento facoltativo dopo un elemento REST.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Un valore esterno di 'this' è nascosto da questo contenitore.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Non è possibile dichiarare come generatore una firma di overload.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "Nella parte sinistra di un'espressione di elevamento a potenza non è consentita un'espressione unaria con l'operatore '{0}'. Provare a racchiudere l'espressione tra parentesi.", "Annotate_everything_with_types_from_JSDoc_95043": "Ann<PERSON>re tutto con tipi di JSDoc", "Annotate_with_type_from_JSDoc_95009": "Annotare con tipo di JSDoc", "Another_export_default_is_here_2753": "In questo punto è presente un'altra impostazione predefinita per l'esportazione.", "Are_you_missing_a_semicolon_2734": "Manca un punto e virgola?", "Argument_expression_expected_1135": "È prevista l'espressione di argomento.", "Argument_for_0_option_must_be_Colon_1_6046": "L'argomento per l'opzione '{0}' deve essere {1}.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "L'argomento dell'importazione dinamica non può essere l'elemento spread.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "L'argomento di tipo '{0}' non è assegnabile al parametro di tipo '{1}'.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "L'argomento di tipo '{0}' non può essere assegnato al parametro di tipo '{1}' con 'exactOptionalPropertyTypes: true'. Provare ad aggiungere 'undefined' ai tipi di proprietà di destinazione.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "Gli argomenti per il parametro REST '{0}' non sono stati specificati.", "Array_element_destructuring_pattern_expected_1181": "È previsto il criterio di destrutturazione dell'elemento della matrice.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "Con le asserzioni ogni nome nella destinazione di chiamata deve essere dichiarato con un'annotazione di tipo esplicita.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "Con le asserzioni la destinazione di chiamata deve essere un identificatore o un nome completo.", "Asterisk_Slash_expected_1010": "È previsto '*/'.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Gli aumenti per l'ambito globale possono solo essere direttamente annidati in dichiarazioni di modulo di ambiente o moduli esterni.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Gli aumenti per l'ambito globale devono contenere il modificatore 'declare', a meno che non siano già presenti in un contesto di ambiente.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "Il rilevamento automatico per le defizioni di tipi è abilitato nel progetto '{0}'. Verrà eseguito il passaggio di risoluzione aggiuntivo per il modulo '{1}' usando il percorso della cache '{2}'.", "Await_expression_cannot_be_used_inside_a_class_static_block_18037": "Non è possibile usare l'espressione await all'interno di un blocco statico di classe.", "BUILD_OPTIONS_6919": "OPZIONI DI COMPILAZIONE", "Backwards_Compatibility_6253": "Compatibilità con le versioni precedenti", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Le espressioni di classi di base non possono fare riferimento a parametri di tipo classe.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "Il tipo restituito '{0}' del costruttore di base non è un tipo di oggetto o un'intersezione di tipi di oggetto con membri noti in modo statico.", "Base_constructors_must_all_have_the_same_return_type_2510": "Il tipo restituito deve essere identico per tutti i costruttori di base.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Directory di base per risolvere i nomi di modulo non assoluti.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "I valori letterali bigint non sono disponibili quando la destinazione è precedente a ES2020.", "Binary_digit_expected_1177": "È prevista una cifra binaria.", "Binding_element_0_implicitly_has_an_1_type_7031": "L'elemento di binding '{0}' contiene implicitamente un tipo '{1}'.", "Block_scoped_variable_0_used_before_its_declaration_2448": "La variabile con ambito blocco '{0}' è stata usata prima di essere stata dichiarata.", "Build_a_composite_project_in_the_working_directory_6925": "Compila un progetto composito nella directory di lavoro.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "Compilare tutti i progetti, anche quelli che sembrano aggiornati.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Compilare uno o più progetti e le relative dipendenze, se non aggiornate", "Build_option_0_requires_a_value_of_type_1_5073": "Con l'opzione di compilazione '{0}' è richiesto un valore di tipo {1}.", "Building_project_0_6358": "Compilazione del progetto '{0}'...", "COMMAND_LINE_FLAGS_6921": "FLAG DELLA RIGA DI COMANDO", "COMMON_COMMANDS_6916": "COMANDI COMUNI", "COMMON_COMPILER_OPTIONS_6920": "OPZIONI COMUNI DEL COMPILATORE", "Call_decorator_expression_90028": "Chiamare l'espressione Decorator", "Call_signature_return_types_0_and_1_are_incompatible_2202": "I tipi restituiti delle firme di chiamata '{0}' e '{1}' sono incompatibili.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "La firma di chiamata, in cui manca l'annotazione di tipo restituito, contiene implicitamente un tipo restituito 'any'.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Le firme di chiamata senza argomenti contengono i tipi restituiti incompatibili '{0}' e '{1}'.", "Call_target_does_not_contain_any_signatures_2346": "La destinazione della chiamata non contiene alcuna firma.", "Can_only_convert_logical_AND_access_chains_95142": "È possibile convertire solo catene di accesso AND logiche", "Can_only_convert_named_export_95164": "È possibile solo convertire l'esportazione denominata", "Can_only_convert_property_with_modifier_95137": "È possibile convertire solo la proprietà con il modificatore", "Can_only_convert_string_concatenation_95154": "È possibile convertire solo la concatenazione di stringhe", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "Non è possibile accedere a '{0}.{1}' perché '{0}' è un tipo ma non uno spazio dei nomi. Si intendeva recuperare il tipo della proprietà '{1}' in '{0}' con '{0}[\"{1}\"]'?", "Cannot_access_ambient_const_enums_when_the_isolatedModules_flag_is_provided_2748": "Quando si specifica il flag '--isolatedModules', non è possibile accedere a enumerazioni const di ambiente.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "Non è possibile assegnare un tipo di costruttore '{0}' a un tipo di costruttore '{1}'.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "Non è possibile assegnare un tipo di costruttore astratto a un tipo di costruttore non astratto.", "Cannot_assign_to_0_because_it_is_a_class_2629": "Non è possibile assegnare a '{0}' perché è una classe.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "Non è possibile assegnare a '{0}' perché è una costante.", "Cannot_assign_to_0_because_it_is_a_function_2630": "Non è possibile assegnare a '{0}' perché è una funzione.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "Non è possibile assegnare a '{0}' perché è uno spazio dei nomi.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "Non è possibile assegnare a '{0}' perché è una proprietà di sola lettura.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "Non è possibile assegnare a '{0}' perché è un'enumerazione.", "Cannot_assign_to_0_because_it_is_an_import_2632": "Non è possibile assegnare a '{0}' perché è una direttiva import.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "Non è possibile assegnare a '{0}' perché non è una variabile.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "Non è possibile assegnare al metodo privato '{0}'. I metodi privati non sono scrivibili.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "Non è possibile aumentare il modulo '{0}' perché viene risolto in un'entità non modulo.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "Non è possibile aumentare il modulo '{0}' con le esportazioni dei valori perché viene risolto in un'entità non modulo.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "Non è possibile compilare moduli con l'opzione '{0}' a meno che il flag '--module' non sia impostato su 'amd' o 'system'.", "Cannot_create_an_instance_of_an_abstract_class_2511": "Non è possibile creare un'istanza di una classe astratta.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "Non è possibile delegare l'iterazione al valore perché il metodo 'next' del relativo iteratore prevede il tipo '{1}', ma il generatore che la contiene invierà sempre '{0}'.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "Non è possibile esportare '{0}'. Da un modulo è possibile esportare solo dichiarazioni locali.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "Non è possibile estendere una classe '{0}'. Il costruttore di classe è contrassegnato come privato.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "Non è possibile estendere un'interfaccia '{0}'. Si intendeva usare 'implements'?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "Non è possibile trovare alcun file tsconfig.json nella directory corrente: {0}.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "Non è stato trovato alcun file tsconfig.json nella directory specificata '{0}'.", "Cannot_find_global_type_0_2318": "Il tipo globale '{0}' non è stato trovato.", "Cannot_find_global_value_0_2468": "Il valore globale '{0}' non è stato trovato.", "Cannot_find_lib_definition_for_0_2726": "La definizione della libreria per '{0}' non è stata trovata.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "La definizione della libreria per '{0}' non è stata trovata. Si intendeva '{1}'?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "Non è possibile trovare il modulo '{0}'. Provare a usare '--resolveJsonModule' per importare il modulo con estensione '.json'.", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_node_or_to_add_aliases_to_th_2792": "Non è possibile trovare il modulo '{0}'. Si intendeva impostare l'opzione 'moduleResolution' su 'node' o aggiungere alias all'opzione 'paths'?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "Non è possibile trovare il modulo '{0}' o le relative dichiarazioni di tipo corrispondenti.", "Cannot_find_name_0_2304": "Il nome '{0}' non è stato trovato.", "Cannot_find_name_0_Did_you_mean_1_2552": "Il nome '{0}' non è stato trovato. Si intendeva '{1}'?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "Il nome '{0}' non è stato trovato. Si intendeva il membro di istanza 'this.{0}'?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "Il nome '{0}' non è stato trovato. Si intendeva il membro statico '{1}.{0}'?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "Impossibile trovare il nome '{0}'. Si intendeva scrivere questo elemento in una funzione asincrona?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "Non è possibile trovare il nome '{0}'. È necessario modificare la libreria di destinazione? Provare a impostare l'opzione 'lib' del compilatore su '{1}' o versioni successive.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "Non è possibile trovare il nome '{0}'. È necessario modificare la libreria di destinazione? Provare a modificare l'opzione 'lib' del compilatore in modo che includa 'dom'.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "Non è possibile trovare il nome '{0}'. È necessario installare le definizioni di tipo per un test runner? Provare con `npm i --save-dev @types/jest` o `npm i --save-dev @types/mocha`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "Non è possibile trovare il nome '{0}'. È necessario installare le definizioni di tipo per un test runner? Provare con `npm i --save-dev @types/jest` o `npm i --save-dev @types/mocha` e quindi aggiungere 'jest' o 'mocha' al campo types in tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "Non è possibile trovare il nome '{0}'. È necessario installare le definizioni di tipo per jQuery? Provare con `npm i --save-dev @types/jquery`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "Non è possibile trovare il nome '{0}'. È necessario installare le definizioni di tipo per jQuery? Provare con `npm i --save-dev @types/jquery` e quindi aggiungere 'jquery' al campo types in tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "Non è possibile trovare il nome '{0}'. È necessario installare le definizioni di tipo per il nodo? Provare con `npm i --save-dev @types/node`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "Non è possibile trovare il nome '{0}'. È necessario installare le definizioni di tipo per il nodo? Provare con `npm i --save-dev @types/node` e quindi aggiungere 'node' al campo types in tsconfig.", "Cannot_find_namespace_0_2503": "Lo spazio dei nomi '{0}' non è stato trovato.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "Il nome '{0}' non è stato trovato. Intendevi '{1}'?", "Cannot_find_parameter_0_1225": "Il parametro '{0}' non è stato trovato.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "Il percorso della sottodirectory comune per i file di input non è stato trovato.", "Cannot_find_type_definition_file_for_0_2688": "Il file di definizione del tipo per '{0}' non è stato trovato.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "Non è possibile importare file di dichiarazione di tipo. Provare a importare '{0}' invece di '{1}'.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "Non è possibile inizializzare la variabile con ambito esterna '{0}' nello stesso ambito della dichiarazione con ambito del blocco '{1}'.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "Non è possibile richiamare un oggetto che è probabilmente 'null'.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "Non è possibile richiamare un oggetto che è probabilmente 'null' o 'undefined'.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "Non è possibile richiamare un oggetto che è probabilmente 'undefined'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "Non è possibile eseguire l'iterazione del valore perché il metodo 'next' del relativo iteratore prevede il tipo '{1}', ma la destrutturazione della matrice invierà sempre '{0}'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "Non è possibile eseguire l'iterazione del valore perché il metodo 'next' del relativo iteratore prevede il tipo '{1}', ma l'estensione della matrice invierà sempre '{0}'.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "Non è possibile eseguire l'iterazione del valore perché il metodo 'next' del relativo iteratore prevede il tipo '{1}', ma for-of invierà sempre '{0}'.", "Cannot_prepend_project_0_because_it_does_not_have_outFile_set_6308": "Non è possibile anteporre il progetto '{0}' perché 'outFile' non è impostato", "Cannot_read_file_0_5083": "Non è possibile leggere il file '{0}'.", "Cannot_read_file_0_Colon_1_5012": "Non è possibile leggere il file '{0}': {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "Non è possibile dichiarare di nuovo la variabile con ambito blocco '{0}'.", "Cannot_redeclare_exported_variable_0_2323": "Non è possibile dichiarare di nuovo la variabile esportata '{0}'.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "Non è possibile dichiarare di nuovo l'identificatore '{0}' nella clausola catch.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "Non è possibile avviare una chiamata di funzione in un'annotazione di tipo.", "Cannot_update_output_of_project_0_because_there_was_error_reading_file_1_6376": "Non è possibile aggiornare l'output del progetto '{0}' perché si è verificato un errore durante la lettura del file '{1}'", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "Non è possibile usare JSX a meno che non sia specificato il flag '--jsx'.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_the_isolatedModules_flag_is_provided_1269": "Non è possibile usare 'export import' in un tipo o in uno spazio dei nomi solo di tipo quando si specifica il flag '--isolatedModules'.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "Non è possibile usare importazioni, esportazioni o aumenti del modulo quando il valore di '--module' è 'none'.", "Cannot_use_namespace_0_as_a_type_2709": "Non è possibile usare lo spazio dei nomi '{0}' come tipo.", "Cannot_use_namespace_0_as_a_value_2708": "Non è possibile usare lo spazio dei nomi '{0}' come valore.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "Non è possibile usare 'this' in un inizializzatore di proprietà statica di una classe decorata.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "Non è possibile scrivere il file '{0}' perché sovrascriverà il file '.tsbuildinfo' generato dal progetto di riferimento '{1}'", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "Non è possibile scrivere il file '{0}' perché verrebbe sovrascritto da più file di input.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "Non è possibile scrivere il file '{0}' perché sovrascriverebbe il file di input.", "Catch_clause_variable_cannot_have_an_initializer_1197": "La variabile della clausola catch non può contenere un inizializzatore.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Se specificata, l'annotazione del tipo di variabile della clausola catch deve essere 'any' o 'unknown'.", "Change_0_to_1_90014": "Modificare '{0}' in '{1}'", "Change_all_extended_interfaces_to_implements_95038": "<PERSON><PERSON><PERSON> tutte le interfacce estese in 'implements'", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Cambiare tutti i tipi in stile jsdoc in TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Cambiare tutti i tipi in stile jsdoc in TypeScript (e aggiungere '| undefined' ai tipi nullable)", "Change_extends_to_implements_90003": "Cambiare 'extends' in 'implements'", "Change_spelling_to_0_90022": "Modificare l'ortografia in '{0}'", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "Verifica la presenza di proprietà di classe dichiarate ma non impostate nel costruttore.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "Verifica che gli argomenti per i metodi 'bind', 'call', and 'apply' corrispondano alla funzione originale.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "V<PERSON>r<PERSON> verificato se '{0}' è il prefisso di corrispondenza più lungo per '{1}' - '{2}'.", "Circular_definition_of_import_alias_0_2303": "Definizione circolare dell'alias di importazione '{0}'.", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "È stata rilevata una circolarità durante la risoluzione della configurazione: {0}", "Circularity_originates_in_type_at_this_location_2751": "La circolarità ha origine nel tipo in questa posizione.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "La classe '{0}' definisce '{1}' come funzione di accesso di membro di istanza, mentre la classe estesa '{2}' la definisce come funzione di membro di istanza.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "La classe '{0}' definisce '{1}' come funzione di membro di istanza, mentre la classe estesa '{2}' la definisce come funzione di accesso di membro di istanza.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "La classe '{0}' definisce '{1}' come proprietà di membro di istanza, mentre la classe estesa '{2}' la definisce come funzione di membro di istanza.", "Class_0_incorrectly_extends_base_class_1_2415": "La classe '{0}' estende in modo errato la classe di base '{1}'.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "La classe '{0}' implementa in modo errato la classe '{1}'. Si intendeva estendere '{1}' ed ereditarne i membri come sottoclasse?", "Class_0_incorrectly_implements_interface_1_2420": "La classe '{0}' implementa in modo errato l'interfaccia '{1}'.", "Class_0_used_before_its_declaration_2449": "La classe '{0}' è stata usata prima di essere stata dichiarata.", "Class_constructor_may_not_be_a_generator_1368": "Il costruttore di classe non può essere un generatore.", "Class_constructor_may_not_be_an_accessor_1341": "Il costruttore di classe non può essere una funzione di accesso.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "La dichiarazione classe non può implementare l'elenco di overload per '{0}'.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "Le dichiarazioni di classe non possono contenere più di un tag '@augments' o '@extends'.", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Non è possibile elementi Decorator di classe con l'identificatore privato statico. Provare a rimuovere l'elemento Decorator sperimentale.", "Class_name_cannot_be_0_2414": "Il nome della classe non può essere '{0}'.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "Il nome della classe non può essere 'Object' quando la destinazione è ES5 con il modulo {0}.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "Il lato statico '{0}' della classe estende in modo errato il lato statico '{1}' della classe di base.", "Classes_can_only_extend_a_single_class_1174": "Le classi possono estendere solo un'unica classe.", "Classes_may_not_have_a_field_named_constructor_18006": "Le classi non possono includere un campo denominato 'constructor'.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "Il codice contenuto in una classe viene valutato in modalità strict JavaScript, che non consente l'uso di '{0}'. Per altre informazioni, vedere https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "Opzioni della riga di comando", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Compila il progetto in base al percorso del file di configurazione o della cartella contenente un file 'tsconfig.json'.", "Compiler_Diagnostics_6251": "Diagnostica compilatore", "Compiler_option_0_expects_an_argument_6044": "Con l'opzione '{0}' del compilatore è previsto un argomento.", "Compiler_option_0_may_not_be_used_with_build_5094": "L'opzione del compilatore '--{0}' potrebbe non essere usata con '--build'.", "Compiler_option_0_may_only_be_used_with_build_5093": "L'opzione del compilatore '--{0}' potrebbe essere usata solo con '--build'.", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "L'opzione del compilatore '{0}' del valore '{1}' è instabile. Usare TypeScript notturno per disattivare l'errore. Provare ad eseguire l'aggiornamento con 'npm install -D typescript@next'.", "Compiler_option_0_requires_a_value_of_type_1_5024": "Con l'opzione '{0}' del compilatore è richiesto un valore di tipo {1}.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "Il compilatore riserva il nome '{0}' quando si crea l'identificatore privato per browser meno recenti.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Compila il progetto TypeScript presente nel percorso specificato.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Compila il progetto corrente (tsconfig.json nella directory di lavoro).", "Compiles_the_current_project_with_additional_settings_6929": "Compila il progetto corrente con impostazioni aggiuntive.", "Completeness_6257": "Completezza", "Composite_projects_may_not_disable_declaration_emit_6304": "I progetti compositi non possono disabilitare la creazione di dichiarazioni.", "Composite_projects_may_not_disable_incremental_compilation_6379": "I progetti compositi non possono disabilitare la compilazione incrementale.", "Computed_from_the_list_of_input_files_6911": "Calcolato dall'elenco dei file di input", "Computed_property_names_are_not_allowed_in_enums_1164": "I nomi di proprietà calcolati non sono consentiti nelle enumerazioni.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "In un'enumerazione con membri con valore stringa non sono consentiti valori calcolati.", "Concatenate_and_emit_output_to_single_file_6001": "Concatena e crea l'output in un singolo file.", "Conflicting_definitions_for_0_found_at_1_and_2_Consider_installing_a_specific_version_of_this_librar_4090": "In '{1}' e '{2}' sono state trovate definizioni in conflitto per '{0}'. Per risolvere il conflitto, provare a installare una versione specifica di questa libreria.", "Conflicts_are_in_this_file_6201": "I conflitti si trovano in questo file.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Provare ad aggiungere un modificatore \"declare\" a questa classe.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "I tipi restituiti delle firme del costrutto '{0}' e '{1}' sono incompatibili.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "La firma del costrutto, in cui manca l'annotazione di tipo restituito, contiene implicitamente un tipo restituito 'any'.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Le firme di costrutto senza argomenti contengono i tipi restituiti incompatibili '{0}' e '{1}'.", "Constructor_implementation_is_missing_2390": "Manca l'implementazione di costruttore.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "Il costruttore della classe '{0}' è privato e accessibile solo all'interno della dichiarazione di classe.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "Il costruttore della classe '{0}' è protetto e accessibile solo all'interno della dichiarazione di classe.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "La notazione del tipo di costruttore deve essere racchiusa tra parentesi quando viene usata in un tipo di unione.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "La notazione del tipo di costruttore deve essere racchiusa tra parentesi quando viene usata in un tipo di intersezione.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "I costruttori di classi derivate devono contenere una chiamata 'super'.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "Il file contenitore non è specificato e non è possibile determinare la directory radice. La ricerca nella cartella 'node_modules' verrà ignorata.", "Containing_function_is_not_an_arrow_function_95128": "La funzione contenitore non è una funzione arrow", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "Controllare il metodo usato per rilevare i file JS in formato modulo.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "La conversione del tipo '{0}' nel tipo '{1}' può essere un errore perché nessuno dei due tipi si sovrappone sufficientemente all'altro. Se questa opzione è intenzionale, convertire prima l'espressione in 'unknown'.", "Convert_0_to_1_in_0_95003": "Convertire '{0}' in '{1} in {0}'", "Convert_0_to_mapped_object_type_95055": "Convertire '{0}' nel tipo di oggetto con mapping", "Convert_all_const_to_let_95102": "Convertire ogni 'const' in 'let'", "Convert_all_constructor_functions_to_classes_95045": "Convertire tutte le funzioni di costruttore in classi", "Convert_all_imports_not_used_as_a_value_to_type_only_imports_1374": "Convertire tutte le importazioni non usate come valore in importazioni solo di tipi", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Convertire tutti i caratteri non validi nel codice entità HTML", "Convert_all_re_exported_types_to_type_only_exports_1365": "Convertire tutti i tipi riesportati in esportazioni solo di tipi", "Convert_all_require_to_import_95048": "Convertire tutte le occorrenze di 'require' in 'import'", "Convert_all_to_async_functions_95066": "Convertire tutto in funzioni asincrone", "Convert_all_to_bigint_numeric_literals_95092": "Convertire tutto in valori letterali numerici bigint", "Convert_all_to_default_imports_95035": "Convertire tutte le impostazioni predefinite", "Convert_all_type_literals_to_mapped_type_95021": "Convertire tutti i valori letterali di tipo nel tipo di cui è stato eseguito il mapping", "Convert_arrow_function_or_function_expression_95122": "Convertire la funzione arrow o l'espressione di funzione", "Convert_const_to_let_95093": "Convertire 'const' in 'let'", "Convert_default_export_to_named_export_95061": "Convertire l'esportazione predefinita nell'esportazione denominata", "Convert_function_declaration_0_to_arrow_function_95106": "Convertire la dichiarazione di funzione '{0}' nella funzione arrow", "Convert_function_expression_0_to_arrow_function_95105": "Convertire l'espressione di funzione '{0}' nella funzione arrow", "Convert_function_to_an_ES2015_class_95001": "Converti la funzione in una classe ES2015", "Convert_invalid_character_to_its_html_entity_code_95100": "Convertire il carattere non valido nel relativo codice entità HTML", "Convert_named_export_to_default_export_95062": "Convertire l'esportazione denominata nell'esportazione predefinita", "Convert_named_imports_to_default_import_95170": "Converti importazioni denominate nell'importazione predefinita", "Convert_named_imports_to_namespace_import_95057": "Convertire le importazioni denominate in importazione spazi dei nomi", "Convert_namespace_import_to_named_imports_95056": "Convertire l'importazione spazi dei nomi in importazioni denominate", "Convert_overload_list_to_single_signature_95118": "Convertire l'elenco di overload in una firma singola", "Convert_parameters_to_destructured_object_95075": "Convertire i parametri nell'oggetto destrutturato", "Convert_require_to_import_95047": "Convertire 'require' in 'import'", "Convert_to_ES_module_95017": "Converti nel modulo ES6", "Convert_to_a_bigint_numeric_literal_95091": "Convertire in un valore letterale numerico bigint", "Convert_to_anonymous_function_95123": "Convertire nella funzione anonima", "Convert_to_arrow_function_95125": "Convertire nella funzione arrow", "Convert_to_async_function_95065": "Convertire nella funzione asincrona", "Convert_to_default_import_95013": "Convertire nell'importazione predefinita", "Convert_to_named_function_95124": "Convertire nella funzione denominata", "Convert_to_optional_chain_expression_95139": "Convertire nell'espressione di catena facoltativa", "Convert_to_template_string_95096": "Convertire nella stringa di modello", "Convert_to_type_only_export_1364": "Convertire nell'esportazione solo di tipi", "Convert_to_type_only_import_1373": "Convertire nell'importazione solo di tipi", "Corrupted_locale_file_0_6051": "Il file delle impostazioni locali {0} è danneggiato.", "Could_not_convert_to_anonymous_function_95153": "Non è stato possibile convertire nella funzione anonima", "Could_not_convert_to_arrow_function_95151": "Non è stato possibile convertire nella funzione arrow", "Could_not_convert_to_named_function_95152": "Non è stato possibile convertire nella funzione denominata", "Could_not_determine_function_return_type_95150": "Non è stato possibile determinare il tipo restituito dalla funzione", "Could_not_find_a_containing_arrow_function_95127": "Non è stato possibile trovare una funzione arrow contenitore", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "Non è stato trovato alcun file di dichiarazione per il modulo '{0}'. A '{1}' è assegnato implicitamente un tipo 'any'.", "Could_not_find_convertible_access_expression_95140": "Non è stato possibile trovare l'espressione di accesso convertibile", "Could_not_find_export_statement_95129": "Non è stato possibile trovare l'istruzione di esportazione", "Could_not_find_import_clause_95131": "Non è stato possibile trovare la clausola di importazione", "Could_not_find_matching_access_expressions_95141": "Non è stato possibile trovare espressioni di accesso corrispondenti", "Could_not_find_name_0_Did_you_mean_1_2570": "Non è stato possibile trovare il nome '{0}'. Si intendeva '{1}'?", "Could_not_find_namespace_import_or_named_imports_95132": "Non è stato possibile trovare l'importazione spazi dei nomi o importazioni denominate", "Could_not_find_property_for_which_to_generate_accessor_95135": "Non è stato possibile trovare la proprietà per cui generare la funzione di accesso", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "Non è stato possibile risolvere il percorso '{0}' con le estensioni: {1}.", "Could_not_write_file_0_Colon_1_5033": "Non è stato possibile scrivere il file '{0}': {1}.", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Crea file di mapping di origine per i file JavaScript creati.", "Create_sourcemaps_for_d_ts_files_6614": "Crea mapping di origine per i file d.ts.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Crea un file tsconfig.jscon le impostazioni consigliate nella directory di lavoro.", "DIRECTORY_6038": "DIRECTORY", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "La dichiarazione causa un aumento di una dichiarazione in un altro file. Questa operazione non è serializzabile.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "Per la creazione della dichiarazione per questo file è necessario usare il nome privato '{0}'. Un'annotazione di tipo esplicita può sbloccare la creazione della dichiarazione.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "Per la creazione della dichiarazione per questo file è necessario usare il nome privato '{0}' dal modulo '{1}'. Un'annotazione di tipo esplicita può sbloccare la creazione della dichiarazione.", "Declaration_expected_1146": "È prevista la dichiarazione.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Il nome della dichiarazione è in conflitto con l'identificatore globale predefinito '{0}'.", "Declaration_or_statement_expected_1128": "È prevista la dichiarazione o l'istruzione.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Dichiarazione o istruzione prevista. Questo carattere '=' segue un blocco di istruzioni, di conseguenza se si intende scrivere un'assegnazione di destrutturazione, potrebbe essere necessario racchiudere l'intera assegnazione tra parentesi.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "Le dichiarazioni con asserzioni di assegnazione definite devono includere anche annotazioni di tipo.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Le dichiarazioni con inizializzatori non possono includere anche asserzioni di assegnazione definite.", "Declare_a_private_field_named_0_90053": "Dichiarare un campo privato denominato '{0}'.", "Declare_method_0_90023": "Dichiarare il metodo '{0}'", "Declare_private_method_0_90038": "Dichiarare il metodo privato '{0}'", "Declare_private_property_0_90035": "Dichiarare la proprietà privata '{0}'", "Declare_property_0_90016": "Dichiarare la proprietà '{0}'", "Declare_static_method_0_90024": "Dichiarare il metodo statico '{0}'", "Declare_static_property_0_90027": "Dichiarare la proprietà statica '{0}'", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "Il tipo restituito della funzione Decorator '{0}' non è assegnabile al tipo '{1}'.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "Il tipo restituito della funzione Decorator è '{0}' ma è previsto 'void' o 'any'.", "Decorators_are_not_valid_here_1206": "In questo punto le espressioni Decorator non sono valide.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "Non è possibile applicare le espressioni Decorator a più funzioni di accesso get/set con lo stesso nome.", "Decorators_may_not_be_applied_to_this_parameters_1433": "Gli elementi Decorator potrebbero non essere applicati ai parametri 'this'.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Gli elementi Decorator devono precedere il nome e tutte le parole chiave delle dichiarazioni di proprietà.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "Le variabili della clausola catch predefinite sono 'unknown' anziché 'any'.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "L'esportazione predefinita del modulo contiene o usa il nome privato '{0}'.", "Default_library_1424": "Libreria predefinita", "Default_library_for_target_0_1425": "Libreria predefinita per la destinazione '{0}'", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "Le definizioni degli identificatori seguenti sono in conflitto con quelle di un altro file: {0}", "Delete_all_unused_declarations_95024": "Eliminare tutte le dichiarazioni non usate", "Delete_all_unused_imports_95147": "Eliminare tutte le direttive import non usate", "Delete_all_unused_param_tags_95172": "Eliminare tutti i tag '@param' inutilizzati", "Delete_the_outputs_of_all_projects_6365": "Eliminare gli output di tutti i progetti.", "Delete_unused_param_tag_0_95171": "Eliminare il tag '@param' '{0}' inutilizzato", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[Deprecata] In alternativa, usare '--jsxFactory'. Specifica l'oggetto richiamato per createElement quando la destinazione è la creazione JSX 'react'", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[Deprecata] In alternativa, usare '--outFile'. Concatena e crea l'output in un singolo file", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[Deprecata] In alternativa, usare '--skipLibCheck'. Ignora il controllo del tipo dei file di dichiarazione delle librerie predefinite.", "Deprecated_setting_Use_outFile_instead_6677": "Impostazione deprecata. In alternativa, usare 'outFile'.", "Did_you_forget_to_use_await_2773": "Si è omesso di usare 'await'?", "Did_you_mean_0_1369": "Si intendeva '{0}'?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "Si intendeva che '{0}' fosse vincolato al tipo 'new (...args: any[]) => {1}'?", "Did_you_mean_to_call_this_expression_6212": "Si intendeva chiamare questa espressione?", "Did_you_mean_to_mark_this_function_as_async_1356": "Si intendeva contrassegnare questa funzione come 'async'?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "Si intendeva usare i due punti (':')? È possibile usare il carattere '=' dopo un nome di proprietà, solo quando il valore letterale di oggetto che lo contiene fa parte di un criterio di destrutturazione.", "Did_you_mean_to_use_new_with_this_expression_6213": "Si intende usare 'new' con questa espressione?", "Digit_expected_1124": "È prevista la cifra.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "La directory '{0}' non esiste. Tutte le ricerche che la interessano verranno ignorate.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "La directory ' {0}' non contiene alcun ambito package.json. Non sarà possibile risolvere le importazioni.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Disabilita l'aggiunta di direttive `use strict` nei file JavaScript generati.", "Disable_checking_for_this_file_90018": "Disabilitare la verifica per questo file", "Disable_emitting_comments_6688": "Disabilita la creazione di commenti.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Disabilita la creazione di dichiarazioni che contengono '@internal' nei commenti JSDoc.", "Disable_emitting_files_from_a_compilation_6660": "Disabilita la creazione di file da una compilazione.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Disabilita la creazione di file se vengono restituiti errori di controllo del tipo.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Disabilita la cancellazione delle dichiarazioni 'const enum' nel codice generato.", "Disable_error_reporting_for_unreachable_code_6603": "Disabilita la segnalazione errori per il codice non raggiungibile.", "Disable_error_reporting_for_unused_labels_6604": "Disabilita la segnalazione errori per le etichette non usate.", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Disabilita la generazione di funzioni helper personalizzate come '__extends' nell'output compilato.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Disabilita l'inclusione di tutti i file di libreria, incluso il file lib.d.ts predefinito.", "Disable_loading_referenced_projects_6235": "Disabilitare il caricamento dei progetti cui viene fatto riferimento.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Disabilita la preferenza per i file di origine invece dei file di dichiarazione quando si fa riferimento a progetti compositi.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Disabilita la segnalazione errori di proprietà in eccesso durante la creazione di valori letterali dell'oggetto.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Disabilita la risoluzione dei collegamenti simbolici nei relativi realpath. È correlato allo stesso flag presente nel nodo.", "Disable_size_limitations_on_JavaScript_projects_6162": "Disabilita le dimensioni relative alle dimensioni per i progetti JavaScript.", "Disable_solution_searching_for_this_project_6224": "Disabilitare la ricerca della soluzione per questo progetto.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Disabilitare il controllo tassativo delle firme generiche nei tipi funzione.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Disabilita l'acquisizione del tipo per i progetti JavaScript", "Disable_truncating_types_in_error_messages_6663": "Disabilita il troncamento dei tipi nei messaggi di errore.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Disabilitare l'uso di file di origine invece dei file di dichiarazione dai progetti a cui si fa riferimento.", "Disable_wiping_the_console_in_watch_mode_6684": "Disabilita la cancellazione della console in modalità espressione di controllo.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Disabilita l'inferenza per l'acquisizione del tipo esaminando i nomi di file in un progetto.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "Non consente a direttive 'import's, 'require's o '<reference>' di espandere il numero di file che TypeScript deve aggiungere a un progetto.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "Non consente riferimenti allo stesso file in cui le maiuscole/minuscole vengono usate in modo incoerente.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "Non aggiunge riferimenti con tripla barra (////) o moduli importati all'elenco di file compilati.", "Do_not_emit_comments_to_output_6009": "Non crea commenti nell'output.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "Non crea dichiarazioni per codice che contiene un'annotazione '@internal'.", "Do_not_emit_outputs_6010": "Non crea output.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "Non crea output se sono stati restituiti errori.", "Do_not_emit_use_strict_directives_in_module_output_6112": "Non crea direttive 'use strict' nell'output del modulo.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "Non cancella le dichiarazioni di enumerazione const nel codice generato.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "Non genera funzioni di supporto personalizzate, come '__extends', nell'output compilato.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "Non include il file di libreria predefinito (lib.d.ts).", "Do_not_report_errors_on_unreachable_code_6077": "Non segnala gli errori in caso di codice non raggiungibile.", "Do_not_report_errors_on_unused_labels_6074": "Non segnala gli errori in caso di etichette non usate.", "Do_not_resolve_the_real_path_of_symlinks_6013": "Non risolvere il percorso reale di collegamenti simbolici.", "Do_not_truncate_error_messages_6165": "Non tronca i messaggi di errore.", "Duplicate_function_implementation_2393": "Implementazione di funzione duplicata.", "Duplicate_identifier_0_2300": "Identificatore '{0}' duplicato.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Identificatore '{0}' duplicato. Il compilatore riserva il nome '{1}' nell'ambito di primo livello di un modulo.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "L'identificatore '{0}' è duplicato. Il compilatore riserva il nome '{1}' nell'ambito di primo livello di un modulo che contiene funzioni asincrone.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "L'identificatore '{0}' è duplicato. Il compilatore riserva il nome '{1}' durante la creazione dei riferimenti 'super' negli inizializzatori statici.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Identificatore '{0}' duplicato. Il compilatore usa la dichiarazione '{1}' per supportare le funzioni asincrone.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "L'identificatore '{0}' è duplicato. Gli elementi statici e di istanza non possono condividere lo stesso nome privato.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Identificatore 'arguments' duplicato. Il compilatore usa 'arguments' per inizializzare i parametri rest.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Identificatore '_newTarget' duplicato. Il compilatore usa la dichiarazione di variabile '_newTarget' per acquisire il riferimento alla metaproprietà 'new.target'.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Identificatore '_this' duplicato. Il compilatore usa la dichiarazione di variabile '_this' per acquisire il riferimento 'this'.", "Duplicate_index_signature_for_type_0_2374": "Firma dell'indice duplicata per il tipo '{0}'.", "Duplicate_label_0_1114": "Etiche<PERSON> '{0}' duplicata.", "Duplicate_property_0_2718": "La proprietà '{0}' è duplicata.", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "L'identificatore dell'importazione dinamica deve essere di tipo 'string', ma il tipo specificato qui è '{0}'.", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Le importazioni dinamiche sono supportate solo quando il flag '--module' è impostato su 'es2020', 'es2022', 'esnext', 'commonjs', 'amd', 'system', 'umd', 'node16' o 'nodenext'.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_assertion_as_arguments_1450": "Le importazioni dinamiche possono accettare come argomenti solo un identificatore di modulo e un'asserzione facoltativa", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_or_nod_1324": "Le importazioni dinamiche supportano un secondo argomento solo quando l'opzione '--module' è impostata su ''esnext', 'node16', o 'nodenext'.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Ogni membro del tipo di unione '{0}' contiene firme di costrutto, ma nessuna di tali firme è compatibile con le altre.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Ogni membro del tipo di unione '{0}' contiene firme, ma nessuna di tali firme è compatibile con le altre.", "Editor_Support_6249": "Supporto Editor", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "L'elemento contiene implicitamente un tipo 'any' perché non è possibile usare l'espressione di tipo '{0}' per indicizzare il tipo '{1}'.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "L'elemento contiene implicitamente un tipo 'any' perché l'espressione di indice non è di tipo 'number'.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "L'elemento contiene implicitamente un tipo 'any' perché al tipo '{0}' non è assegnata alcuna firma dell'indice.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "L'elemento contiene implicitamente un tipo 'any' perché al tipo '{0}' non è assegnata alcuna firma dell'indice. Si intendeva chiamare '{1}'?", "Emit_6246": "<PERSON><PERSON>", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Crea campi di classe conformi allo standard ECMAScript.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Crea un BOM (Byte Order Mark) UTF-8 all'inizio dei file di output.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Crea un unico file con i mapping di origine invece di file separati.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Crea un profilo CPU v8 dell'esecuzione del compilatore per il debug.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Crea codice JavaScript aggiuntivo per semplificare il supporto per l'importazione di moduli CommonJS. Abilita 'allowSyntheticDefaultImports' per la compatibilità dei tipi.", "Emit_class_fields_with_Define_instead_of_Set_6222": "<PERSON><PERSON><PERSON> i campi della classe con Define invece di Set.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Crea metadati di tipo progettazione per le dichiarazioni decorate nei file di origine.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "Crea codice JavaScript più conforme, ma dettagliato e meno efficiente per l'iterazione.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Crea l'origine unitamente ai mapping di origine all'interno di un unico file. Richiede l'impostazione di '--inlineSourceMap' o '--sourceMap'.", "Enable_all_strict_type_checking_options_6180": "Abilita tutte le opzioni per i controlli del tipo strict.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Abilita il colore e la formattazione nell'output TypeScript per agevolare la lettura degli errori del compilatore.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Abilita i vincoli che consentono l'uso di un progetto TypeScript con riferimenti al progetto.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Abilita la segnalazione errori per i percorsi di codice che non vengono restituiti in modo esplicito in una funzione.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Abilita la segnalazione errori per espressioni e dichiarazioni con un tipo implicito 'any'.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Abilita la segnalazione errori per i casi di fallthrough nelle istruzioni switch.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Abilita la segnalazione errori nei file JavaScript con controllo del tipo.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Abilita la segnalazione errori quando variabili locali non vengono lette.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Abilita la segnalazione errori quando a 'this' viene assegnato il tipo 'any'.", "Enable_experimental_support_for_TC39_stage_2_draft_decorators_6630": "Abilita il supporto sperimentale per gli elementi Decorator della bozza fase 2 di TC39.", "Enable_importing_json_files_6689": "Abilita l'importazione di file .json.", "Enable_project_compilation_6302": "Abilitare la compilazione dei progetti", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "Abilitare i metodi strict 'bind', 'call' e 'apply' nelle funzioni.", "Enable_strict_checking_of_function_types_6186": "Abilita il controllo tassativo dei tipi funzione.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Abilitare il controllo tassativo dell'inizializzazione delle proprietà nelle classi.", "Enable_strict_null_checks_6113": "Abilita i controlli strict Null.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Abilitare l'opzione 'experimentalDecorators' nel file di configurazione", "Enable_the_jsx_flag_in_your_configuration_file_95088": "Abilitare il flag '--jsx' nel file di configurazione", "Enable_tracing_of_the_name_resolution_process_6085": "Abilita la traccia del processo di risoluzione dei nomi.", "Enable_verbose_logging_6713": "Abilitare la registrazione dettagliata.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Abilita l'interoperabilità di creazione tra moduli ES e CommonJS tramite la creazione di oggetti spazio dei nomi per tutte le importazioni. Implica 'allowSyntheticDefaultImports'.", "Enables_experimental_support_for_ES7_decorators_6065": "Abilita il supporto sperimentale per le espressioni Decorator di ES7.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Abilita il supporto sperimentale per la creazione dei metadati dei tipi per le espressioni Decorator.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Impone l'uso di funzioni di accesso indicizzate per le chiavi dichiarate con un tipo indicizzato.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Si assicura che i membri di override nelle classi derivate siano contrassegnati con un modificatore override.", "Ensure_that_casing_is_correct_in_imports_6637": "Si assicura che l'uso di maiuscole e minuscole sia corretto nelle direttive import.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Si assicura che sia possibile eseguire il transpile di ogni file in modo sicuro senza basarsi su altre direttive import.", "Ensure_use_strict_is_always_emitted_6605": "Si assicura che la direttiva 'use strict' venga sempre creata.", "Entry_point_for_implicit_type_library_0_1420": "Punto di ingresso per la libreria dei tipi impliciti '{0}'", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Punto di ingresso per la libreria dei tipi impliciti '{0}' con packageId '{1}'", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Punto di ingresso della libreria dei tipi '{0}' specificata in compilerOptions", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Punto di ingresso della libreria dei tipi '{0}' specificata in compilerOptions con packageId '{1}'", "Enum_0_used_before_its_declaration_2450": "L'enumerazione '{0}' è stata usata prima di essere stata dichiarata.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "È possibile unire dichiarazioni di enumerazione solo con lo spazio dei nomi o altre dichiarazioni di enumerazione.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Le dichiarazioni di enumerazione devono essere tutte const o tutte non const.", "Enum_member_expected_1132": "È previsto il membro di enumerazione.", "Enum_member_must_have_initializer_1061": "Il membro di enumerazione deve contenere l'inizializzatore.", "Enum_name_cannot_be_0_2431": "Il nome dell'enumerazione non può essere '{0}'.", "Enum_type_0_has_members_with_initializers_that_are_not_literals_2535": "Il tipo di enumerazione '{0}' contiene membri i cui inizializzatori non sono valori letterali.", "Errors_Files_6041": "File di errori", "Examples_Colon_0_6026": "Esempi: {0}", "Excessive_stack_depth_comparing_types_0_and_1_2321": "La profondità dello stack per il confronto dei tipi '{0}' e '{1}' è eccessiva.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Sono previsti argomento tipo {0}-{1}. Per <PERSON><PERSON>, usare un tag '@extends'.", "Expected_0_arguments_but_got_1_2554": "Sono previsti {0} argo<PERSON>i, ma ne sono stati ottenuti {1}.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "Sono previsti {0} argomenti, ma ne sono stati ottenuti {1}. Si è dimenticato di includere 'void' nell'argomento di tipo per 'Promise'?", "Expected_0_type_arguments_but_got_1_2558": "Sono previsti {0} argomenti tipo, ma ne sono stati ottenuti {1}.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Sono previsti {0} argomenti tipo. Per specificarli, usare un tag '@extends'.", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "Previsto 1 argomento, ma ottenuto 0. 'new Promise()' richiede un hint JSDoc per produrre un elemento 'resolve' che possa essere chiamato senza argomenti.", "Expected_at_least_0_arguments_but_got_1_2555": "Sono previsti almeno {0} argo<PERSON>i, ma ne sono stati ottenuti {1}.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "È previsto il tag di chiusura JSX corrispondente per '{0}'.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "È previsto il tag di chiusura corrispondente per il frammento JSX.", "Expected_for_property_initializer_1442": "È previsto '=' per l'inizializzatore di proprietà.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "Il tipo previsto del campo '{0}' in 'package.json' è '{1}', ma è stato ottenuto '{2}'.", "Experimental_support_for_decorators_is_a_feature_that_is_subject_to_change_in_a_future_release_Set_t_1219": "Il supporto sperimentale per gli elementi Decorator è una funzionalità soggetta a modifica nelle prossime versioni. Per rimuovere questo avviso, impostare l'opzione 'experimentalDecorators' nel file 'tsconfig' o 'jsconfig'.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Il tipo di risoluzione del modulo '{0}' è stato specificato in modo esplicito.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "Non è possibile usare l'elevamento a potenza su valori 'bigint' a meno che l'opzione 'target' non sia impostata su 'es2016' o versioni successive.", "Export_0_from_module_1_90059": "Esporta '{0}' dal modulo '{1}'", "Export_all_referenced_locals_90060": "Esporta tutte le variabili locali di riferimento", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "Non è possibile usare l'assegnazione di esportazione se destinata a moduli ECMAScript. Provare a usare 'export default' o un altro formato di modulo.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "L'assegnazione dell'esportazione non è supportata quando il valore del flag '--module' è 'system'.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "La dichiarazione di esportazione è in conflitto con la dichiarazione esportata di '{0}'.", "Export_declarations_are_not_permitted_in_a_namespace_1194": "Le dichiarazioni di esportazione non sono consentite in uno spazio dei nomi.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "L'identificatore di esportazione '{0}' non esiste nell'ambito package.json al percorso '{1}'.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "L'alias di tipo esportato '{0}' contiene o usa il nome privato '{1}'.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "L'alias di tipo esportato '{0}' contiene o usa il nome privato '{1}' del modulo {2}.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "La variabile esportata '{0}' contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominata.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "La variabile esportata '{0}' contiene o usa il nome '{1}' del modulo privato '{2}'.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "La variabile esportata '{0}' contiene o usa il nome privato '{1}'.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "Le esportazioni e le assegnazioni di esportazioni non sono consentite negli aumenti del modulo.", "Expression_expected_1109": "È prevista l'espressione.", "Expression_or_comma_expected_1137": "È prevista l'espressione o la virgola.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "L'espressione produce un tipo di tupla troppo grande da rappresentare.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "L'espressione produce un tipo di unione troppo complesso da rappresentare.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "L'espressione viene risolta in '_super', che è usato dal compilatore per acquisire il riferimento della classe di base.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "L'espressione viene risolta nella dichiarazione di variabile '_newTarget', che è usata dal compilatore per acquisire il riferimento alla metaproprietà 'new.target'.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "L'espressione viene risolta nella dichiarazione di variabile '_this', che è usata dal compilatore per acquisire il riferimento 'this'.", "Extract_constant_95006": "Estrarre la costante", "Extract_function_95005": "Estrarre la funzione", "Extract_to_0_in_1_95004": "Estrarre in {0} in {1}", "Extract_to_0_in_1_scope_95008": "Estrarre in {0} nell'ambito {1}", "Extract_to_0_in_enclosing_scope_95007": "E<PERSON>rre in {0} nell'ambito che lo contiene", "Extract_to_interface_95090": "E<PERSON>rre nell'interfaccia", "Extract_to_type_alias_95078": "<PERSON><PERSON><PERSON> nell'alias di tipo", "Extract_to_typedef_95079": "Estrarre in typedef", "Extract_type_95077": "Estrarre il tipo", "FILE_6035": "FILE", "FILE_OR_DIRECTORY_6040": "FILE O DIRECTORY", "Failed_to_parse_file_0_Colon_1_5014": "Non è stato possibile analizzare il file '{0}': {1}.", "Fallthrough_case_in_switch_7029": "<PERSON>aso di fallthrough in switch.", "File_0_does_not_exist_6096": "Il file '{0}' non esiste.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "Il file '{0}' non esiste in base alle ricerche precedenti memorizzate nella cache.", "File_0_exist_use_it_as_a_name_resolution_result_6097": "Il file '{0}' esiste. Usarlo come risultato per la risoluzione dei nomi.", "File_0_exists_according_to_earlier_cached_lookups_6239": "Il file '{0}' esiste già in base alle ricerche precedenti memorizzate nella cache.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "L'estensione del file '{0}' non è supportata. Le uniche estensioni supportate sono {1}.", "File_0_has_an_unsupported_extension_so_skipping_it_6081": "L'estensione del file '{0}' non è supportata. Il file verrà ignorato.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "Il file '{0}' è un file JavaScript. Si intendeva abilitare l'opzione 'allowJs'?", "File_0_is_not_a_module_2306": "Il file '{0}' non è un modulo.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "Il file '{0}' non è incluso nell'elenco file del progetto '{1}'. I progetti devono elencare tutti i file o usare un criterio 'include'.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "Il file '{0}' non si trova in 'rootDir' '{1}'. 'rootDir' deve contenere tutti i file di origine.", "File_0_not_found_6053": "Il file '{0}' non è stato trovato.", "File_Management_6245": "Gestione dei file", "File_change_detected_Starting_incremental_compilation_6032": "È stata rilevata una modifica ai file. Verrà avviata la compilazione incrementale...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "Il file è un modulo CommonJS perché '{0}' non contiene il campo \"type\"", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "Il file è un modulo CommonJS perché '{0}' contiene il campo \"type\" il cui valore non è \"module\"", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "Il file è un modulo CommonJS perché 'package.json' non è stato trovato", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "Il file è un modulo ECMAScript perché '{0}' contiene il campo \"type\" con valore \"module\"", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "Il file è un modulo CommonJS e può essere convertito in un modulo ES6.", "File_is_default_library_for_target_specified_here_1426": "Il file è la libreria predefinita per la destinazione specificata in questo punto.", "File_is_entry_point_of_type_library_specified_here_1419": "Il file è il punto di ingresso della libreria dei tipi specificata in questo punto.", "File_is_included_via_import_here_1399": "Il file viene incluso tramite importazione in questo punto.", "File_is_included_via_library_reference_here_1406": "Il file viene incluso tramite il riferimento alla libreria in questo punto.", "File_is_included_via_reference_here_1401": "Il file viene incluso tramite riferimento in questo punto.", "File_is_included_via_type_library_reference_here_1404": "Il file viene incluso tramite il riferimento alla libreria dei tipi in questo punto.", "File_is_library_specified_here_1423": "Il file è la libreria specificata in questo punto.", "File_is_matched_by_files_list_specified_here_1410": "Per la corrispondenza del file viene usato l'elenco 'files' specificato in questo punto.", "File_is_matched_by_include_pattern_specified_here_1408": "Per la corrispondenza del file viene usato il criterio di inclusione specificato in questo punto.", "File_is_output_from_referenced_project_specified_here_1413": "Il file corrisponde all'output del progetto di riferimento specificato in questo punto.", "File_is_output_of_project_reference_source_0_1428": "Il file corrisponde all'origine '{0}' del riferimento al progetto", "File_is_source_from_referenced_project_specified_here_1416": "Il file è l'origine del progetto di riferimento specificato in questo punto.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "Il nome file '{0}' differisce da quello già incluso '{1}' solo per l'uso di maiuscole/minuscole.", "File_name_0_has_a_1_extension_stripping_it_6132": "L'estensione del nome file '{0}' è '{1}' e verrà rimossa.", "File_redirects_to_file_0_1429": "Il file viene reindirizzato al file '{0}'", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "La specifica del file non può contenere una directory padre ('..') inserita dopo un carattere jolly ('**') di directory ricorsiva: '{0}'.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "La specifica del file non può terminare con caratteri jolly ('**') di directory ricorsiva: '{0}'.", "Filters_results_from_the_include_option_6627": "Filtra i risultati dall'opzione `include`.", "Fix_all_detected_spelling_errors_95026": "Correggere tutti gli errori di ortografia rilevati", "Fix_all_expressions_possibly_missing_await_95085": "Correggere tutte le espressioni in cui potrebbe mancare 'await'", "Fix_all_implicit_this_errors_95107": "Correggere tutti gli errori relativi a 'this' implicito", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Correggere tutti i tipi restituiti non corretti di una funzione asincrona", "For_await_loops_cannot_be_used_inside_a_class_static_block_18038": "Non è possibile usare i cicli 'for await' all'interno di un blocco statico di classe.", "Found_0_errors_6217": "Sono stati trovati {0} errori.", "Found_0_errors_Watching_for_file_changes_6194": "Sono stati trovati {0} errori. Verranno individuate le modifiche ai file.", "Found_0_errors_in_1_files_6261": "Sono stati trovati {0} errori nei file {1}.", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "Sono stati trovati {0} errori nello stesso file, a partire da: {1}", "Found_1_error_6216": "È stato trovato 1 errore.", "Found_1_error_Watching_for_file_changes_6193": "È stato trovato 1 errore. Verranno individuate le modifiche ai file.", "Found_1_error_in_1_6259": "È stato trovato 1 errore in {1}", "Found_package_json_at_0_6099": "Il file 'package.json' è stato trovato in '{0}'.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_1250": "Le dichiarazioni di funzione non sono consentite all'interno di blocchi in modalità strict quando la destinazione è 'ES3' o 'ES5'.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Class_d_1251": "Le dichiarazioni di funzione non sono consentite all'interno di blocchi in modalità strict quando la destinazione è 'ES3' o 'ES5'. Le definizioni di classe sono impostate automaticamente nella modalità strict.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Modules_1252": "Le dichiarazioni di funzione non sono consentite all'interno di blocchi in modalità strict quando la destinazione è 'ES3' o 'ES5'. I moduli sono impostati automaticamente nella modalità strict.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "L'espressione di funzione, in cui manca l'annotazione di tipo restituito, contiene implicitamente un tipo restituito '{0}'.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "L'implementazione di funzione manca o non segue immediatamente la dichiarazione.", "Function_implementation_name_must_be_0_2389": "Il nome dell'implementazione di funzione deve essere '{0}'.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "La funzione contiene implicitamente il tipo restituito 'any', perché non contiene un'annotazione di tipo restituito e viene usata come riferimento diretto o indiretto in una delle relative espressioni restituite.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "Nella funzione manca l'istruzione return finale e il tipo restituito non include 'undefined'.", "Function_not_implemented_95159": "Funzione non implementata.", "Function_overload_must_be_static_2387": "L'overload della funzione deve essere statico.", "Function_overload_must_not_be_static_2388": "L'overload della funzione non deve essere statico.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "La notazione del tipo di funzione deve essere racchiusa tra parentesi quando viene usata in un tipo di unione.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "La notazione del tipo di funzione deve essere racchiusa tra parentesi quando viene usata in un tipo di intersezione.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "Il tipo di funzione, in cui manca l'annotazione di tipo restituito, contiene implicitamente un tipo restituito '{0}'.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "La funzione con corpi può essere unita solo a classi di tipo ambient.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Genera file .d.ts da file TypeScript e JavaScript nel progetto.", "Generate_get_and_set_accessors_95046": "Generare le funzioni di accesso 'get' e 'set'", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "Generare le funzioni di accesso 'get' e 'set' per tutte le proprietà di sostituzione", "Generates_a_CPU_profile_6223": "Genera un profilo CPU.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Genera un mapping di origine per ogni file '.d.ts' corrispondente.", "Generates_an_event_trace_and_a_list_of_types_6237": "Genera una traccia eventi e un elenco di tipi.", "Generates_corresponding_d_ts_file_6002": "Genera il file '.d.ts' corrispondente.", "Generates_corresponding_map_file_6043": "Genera il file '.map' corrispondente.", "Generator_implicitly_has_yield_type_0_because_it_does_not_yield_any_values_Consider_supplying_a_retu_7025": "Al generatore è assegnato in modo implicito il tipo yield '{0}' perché non contiene alcun valore. Provare a specificare un'annotazione di tipo restituito.", "Generators_are_not_allowed_in_an_ambient_context_1221": "I generatori non sono consentiti in un contesto di ambiente.", "Generic_type_0_requires_1_type_argument_s_2314": "Il tipo generico '{0}' richiede {1} argomento/i di tipo.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "Il tipo generico '{0}' richiede tra {1} e {2} argomenti tipo.", "Global_module_exports_may_only_appear_at_top_level_1316": "Le esportazioni di moduli globali possono essere usate solo al primo livello.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Le esportazioni di moduli globali possono essere usate solo in file di dichiarazione.", "Global_module_exports_may_only_appear_in_module_files_1314": "Le esportazioni di moduli globali possono essere usate solo in file di modulo.", "Global_type_0_must_be_a_class_or_interface_type_2316": "Il tipo globale '{0}' deve un tipo di classe o di interfaccia.", "Global_type_0_must_have_1_type_parameter_s_2317": "Il tipo globale '{0}' deve contenere {1} parametro/i di tipo.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "Impostare le ricompilazioni in '--incremental' e '--watch' in modo che le modifiche all'interno di un file interessino solo i file che dipendono direttamente da esso.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "Imposta le ricompilazioni in progetti che usano la modalità 'incremental' e 'watch' in modo che le modifiche all'interno di un file interessino solo i file che dipendono direttamente da esso.", "Hexadecimal_digit_expected_1125": "È prevista la cifra esadecimale.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "È previsto un identificatore. '{0}' è una parola riservata al livello principale di un modulo.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "È previsto un identificatore. '{0}' è una parola riservata in modalità strict.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "È previsto un identificatore. '{0}' è una parola riservata in modalità strict. Le definizioni di classe sono automaticamente impostate sulla modalità strict.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "È previsto un identificatore. '{0}' è una parola riservata in modalità strict. I moduli vengono impostati automaticamente in modalità strict.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "È previsto un identificatore. '{0}' è una parola riservata che non può essere usata in questo punto.", "Identifier_expected_1003": "È previsto l'identificatore.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "È previsto un identificatore. '__esModule' è riservato come marcatore esportato durante la trasformazione di moduli ECMAScript.", "Identifier_or_string_literal_expected_1478": "Previsto identificatore o valore letterale stringa.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "Se il pacchetto '{0}' espone effettivamente questo modulo, provare a inviare una richiesta pull per modificare 'https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}'", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "Se il pacchetto ' {0}' espone effettivamente il modulo, provare ad aggiungere un nuovo file di dichiarazione (.d.ts) contenente ' Dichiara modulo' {1}';'", "Ignore_this_error_message_90019": "Ignorare questo messaggio di errore", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Il file tsconfig.json verrà ignorato. I file specificati verranno compilati con le opzioni predefinite del compilatore.", "Implement_all_inherited_abstract_classes_95040": "Implementare tutte le classi astratte ereditate", "Implement_all_unimplemented_interfaces_95032": "Implementare tutte le interfacce non implementate", "Implement_inherited_abstract_class_90007": "Implementare la classe astratta ereditata", "Implement_interface_0_90006": "Implementare l'interfaccia '{0}'", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "La clausola implements della classe esportata '{0}' contiene o usa il nome privato '{1}'.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "La conversione implicita di un valore 'symbol' in 'string' non riuscirà in fase di esecuzione. Provare a eseguire il wrapping di questa espressione in 'String(...)'.", "Import_0_from_1_90013": "Importare '{0}' da \"{1}\".", "Import_assertion_values_must_be_string_literal_expressions_2837": "I valori di asserzione di importazione devono essere espressioni letterali delle stringhe.", "Import_assertions_are_not_allowed_on_statements_that_transpile_to_commonjs_require_calls_2836": "Le asserzioni di importazione non sono consentite nelle istruzioni che eseguono il transpile nelle chiamate 'require' di commonjs.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_or_nodenext_2821": "Le asserzioni di importazione sono supportate solo quando l'opzione “--module” è impostata su “esnext” o “nodenext”.", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "Non è possibile usare asserzioni di importazione con importazioni o esportazioni di solo tipo.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "Non è possibile usare l'assegnazione di importazione se destinata a moduli ECMAScript. Provare a usare 'import * as ns from \"mod\"', 'import {a} from \"mod\"', 'import d from \"mod\"' o un altro formato di modulo.", "Import_declaration_0_is_using_private_name_1_4000": "La dichiarazione di importazione '{0}' usa il nome privato '{1}'.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "La dichiarazione di importazione è in conflitto con la dichiarazione locale di '{0}'.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Le dichiarazioni di importazione in uno spazio dei nomi non possono far riferimento a un modulo.", "Import_emit_helpers_from_tslib_6139": "Importa gli helper di creazione da 'tslib'.", "Import_may_be_converted_to_a_default_import_80003": "L'importazione può essere convertita in un'importazione predefinita.", "Import_name_cannot_be_0_2438": "Il nome dell'importazione non può essere '{0}'.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "La dichiarazione di importazione o esportazione in una dichiarazione di modulo di ambiente non può fare riferimento al modulo tramite il nome di modulo relativo.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "L'identificatore di importazione ' {0}' non esiste nell’ambito package.json al percorso ' {1}'.", "Imported_via_0_from_file_1_1393": "Importato tramite {0} dal file '{1}'", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Importato tramite {0} dal file '{1}' per importare 'importHelpers' come specificato in compilerOptions", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Importato tramite {0} dal file '{1}' per importare le funzioni di factory 'jsx' e 'jsxs'", "Imported_via_0_from_file_1_with_packageId_2_1394": "Importato tramite {0} dal file '{1}' con packageId '{2}'", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Importato tramite {0} dal file '{1}' con packageId '{2}' per importare 'importHelpers' come specificato in compilerOptions", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Importato tramite {0} dal file '{1}' con packageId '{2}' per importare le funzioni di factory 'jsx' e 'jsxs'", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "Le importazioni non sono consentite negli aumenti di modulo. Provare a spostarle nel modulo esterno di inclusione.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "Nelle dichiarazioni di enumerazione dell'ambiente l'inizializzatore di membro deve essere un'espressione costante.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "In un'enumerazione con più dichiarazioni solo una di queste può omettere un inizializzatore per il primo elemento dell'enumerazione.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Include un elenco di file. A differenza di `include`, i criteri GLOB non sono supportati.", "Include_modules_imported_with_json_extension_6197": "Includere i moduli importati con estensione '.json'", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Include il codice sorgente nei mapping di origine all'interno del codice JavaScript creato.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Include i file dei mapping di origine all'interno del codice JavaScript creato.", "Includes_imports_of_types_referenced_by_0_90054": "Include importazioni di tipi a cui fa riferimento '{0}'", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "Se si include --watch, l'opzione -w consentirà di iniziare a controllare il progetto corrente per individuare modifiche ai file. Dopo l'impostazione, è possibile configurare la modalità espressione di controllo con:", "Index_signature_for_type_0_is_missing_in_type_1_2329": "La firma dell'indice per il tipo '{0}' manca nel tipo '{1}'.", "Index_signature_in_type_0_only_permits_reading_2542": "La firma dell'indice nel tipo '{0}' consente solo la lettura.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Le singole dichiarazioni della dichiarazione sottoposta a merge '{0}' devono essere tutte esportate o tutte locali.", "Infer_all_types_from_usage_95023": "Derivare tutti i tipi dall'utilizzo", "Infer_function_return_type_95148": "Dedurre il tipo restituito della funzione", "Infer_parameter_types_from_usage_95012": "Derivare i tipi di parametro dall'utilizzo", "Infer_this_type_of_0_from_usage_95080": "Derivare il tipo 'this' di '{0}' dall'utilizzo", "Infer_type_of_0_from_usage_95011": "Derivare il tipo di '{0}' dall'utilizzo", "Initialize_property_0_in_the_constructor_90020": "Inizializzare la proprietà '{0}' nel costruttore", "Initialize_static_property_0_90021": "Inizializzare la proprietà statica '{0}'", "Initializer_for_property_0_2811": "Inizializzatore per la proprietà '{0}'", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "L'inizializzatore della variabile del membro di istanza '{0}' non può fare riferimento all'identificatore '{1}' dichiarato nel costruttore.", "Initializer_provides_no_value_for_this_binding_element_and_the_binding_element_has_no_default_value_2525": "L'inizializzatore non fornisce alcun valore per questo elemento di binding e per quest'ultimo non è disponibile un valore predefinito.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "Gli inizializzatori non sono consentiti in contesti di ambiente.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Inizializza un progetto TypeScript e crea un file tsconfig.json.", "Insert_command_line_options_and_files_from_a_file_6030": "Inserisce i file e le opzioni della riga di comando da un file.", "Install_0_95014": "Installare '{0}'", "Install_all_missing_types_packages_95033": "Installare tutti i pacchetti di tipi mancanti", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "L'interfaccia '{0}' non può estendere simultaneamente i tipi '{1}' e '{2}'.", "Interface_0_incorrectly_extends_interface_1_2430": "L'interfaccia '{0}' estende in modo errato l'interfaccia '{1}'.", "Interface_declaration_cannot_have_implements_clause_1176": "La dichiarazione di interfaccia non può avere una clausola 'implements'.", "Interface_must_be_given_a_name_1438": "È necessario assegnare un nome all'interfaccia.", "Interface_name_cannot_be_0_2427": "Il nome dell'interfaccia non può essere '{0}'.", "Interop_Constraints_6252": "Vincoli interoperabilità", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Interpreta i tipi di proprietà facoltativi scritti, invece di aggiungere 'undefined'.", "Invalid_character_1127": "Carattere non valido.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "L'identificatore di importazione non è valido ' {0}' non contiene risoluzioni possibili.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Il nome di modulo nell'aumento non è valido. Il modulo '{0}' viene risolto in un modulo non tipizzato in '{1}', che non può essere aumentato.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Il nome di modulo nell'aumento non è valido. Il modulo '{0}' non è stato trovato.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Catena facoltativa non valida dalla nuova espressione. Si intendeva chiamare '{0}()'?", "Invalid_reference_directive_syntax_1084": "La sintassi della direttiva 'reference' non è valida.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Uso non valido di '{0}'. Non può essere usato all'interno di un blocco statico di classe.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Uso non valido di '{0}'. I moduli vengono impostati automaticamente in modalità strict.", "Invalid_use_of_0_in_strict_mode_1100": "Uso non valido di '{0}' in modalità strict.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Il valore non è valido per 'jsxFactory'. '{0}' non è un identificatore o un nome qualificato valido.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Il valore non è valido per 'jsxFragmentFactory'. '{0}' non è un identificatore o un nome qualificato valido.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Il valore di '--reactNamespace' non è valido. '{0}' non è un identificatore valido", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "È probabile che manchi una virgola per separare queste due espressioni di modello. Costituiscono un'espressione di modello con tag che non può essere richiamata.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "Il relativo tipo di elemento '{0}' non è un elemento JSX valido.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "Il relativo tipo di istanza '{0}' non è un elemento JSX valido.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "Il relativo tipo restituito '{0}' non è un elemento JSX valido.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "Il tag '@{0} {1}' di J<PERSON>oc non corrisponde alla clausola 'extends {2}'.", "JSDoc_0_is_not_attached_to_a_class_8022": "Il tag '@{0}' di JSDoc non è collegato a una classe.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...' può essere presente solo nell'ultimo parametro di una firma.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "Il nome del tag '@param' di JSDoc è '{0}', ma non esiste alcun parametro con questo nome.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "Il nome del tag '@param' di JSDoc è '{0}', ma non esiste alcun parametro con questo nome. Se contenesse un tipo matrice, corrisponderebbe ad 'arguments'.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "Il tag '@typedef' di JSDoc deve contenere un'annotazione di tipo o essere seguito dal tag '@property' o '@member'.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "I tipi JSDoc possono essere usati solo nei commenti della documentazione.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "I tipi JSDoc possono essere convertiti in tipi TypeScript.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "Agli attributi JSX deve essere assegnato solo un elemento 'expression' non vuoto.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "Per l'elemento JSX '{0}' non esiste alcun tag di chiusura corrispondente.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "La classe dell'elemento JSX non supporta gli attributi perché non contiene una proprietà '{0}'.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "L'elemento JSX contiene implicitamente il tipo 'any' perché non esiste alcuna interfaccia 'JSX.{0}'.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "L'elemento JSX contiene implicitamente il tipo 'any' perché il tipo globale 'JSX.Element' non esiste.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "Il tipo '{0}' dell'elemento JSX non contiene firme di costrutto o chiamata.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "Gli elementi JSX non possono contenere più attributi con lo stesso nome.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "Nelle espressioni JSX non si può usare l'operatore virgola. Si intendeva scrivere una matrice?", "JSX_expressions_must_have_one_parent_element_2657": "Le espressioni JSX devono contenere un solo elemento padre.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "Per il frammento JSX non esiste alcun tag di chiusura corrispondente.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "Le espressioni di accesso alla proprietà JSX non possono includere nomi dello spazio dei nomi JSX", "JSX_spread_child_must_be_an_array_type_2609": "L'elemento figlio dell'attributo spread JSX deve essere un tipo di matrice.", "JavaScript_Support_6247": "Supporto JavaScript", "Jump_target_cannot_cross_function_boundary_1107": "La destinazione di collegamento non può oltrepassare il limite della funzione.", "KIND_6034": "TIPOLOGIA", "Keywords_cannot_contain_escape_characters_1260": "Le parole chiave non possono contenere caratteri di escape.", "LOCATION_6037": "PERCORSO", "Language_and_Environment_6254": "Linguaggio e ambiente", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "Il lato sinistro dell'operatore virgola non è usato e non ha effetti collaterali.", "Library_0_specified_in_compilerOptions_1422": "Libreria '{0}' specificata in compilerOptions", "Library_referenced_via_0_from_file_1_1405": "Libreria a cui viene fatto riferimento tramite '{0}' dal file '{1}'", "Line_break_not_permitted_here_1142": "L'interruzione di riga non è consentita in questo punto.", "Line_terminator_not_permitted_before_arrow_1200": "Il terminatore di riga non è consentito prima di arrow.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Elenco dei suffissi dei nomi di file da cercare durante la risoluzione di un modulo.", "List_of_folders_to_include_type_definitions_from_6161": "Elenco di cartelle da cui includere le definizioni di tipo.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Elenco delle cartelle radice il cui contenuto combinato rappresenta la struttura del progetto in fase di esecuzione.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Verrà eseguito il caricamento di '{0}' dalla directory radice '{1}'. Percorso candidato: '{2}'.", "Loading_module_0_from_node_modules_folder_target_file_type_1_6098": "Verrà eseguito il caricamento del modulo '{0}' dalla cartella 'node_modules'. Tipo di file di destinazione: '{1}'.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_type_1_6095": "Verrà eseguito il caricamento del modulo come file/cartella. Percorso candidato del modulo: '{0}'. Tipo di file di destinazione: '{1}'.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "Le impostazioni locali devono essere nel formato <lingua> o <lingua>-<territorio>, ad esempio, '{0}' o '{1}'.", "Log_paths_used_during_the_moduleResolution_process_6706": "Registra i percorsi usati durante il processo 'moduleResolution'.", "Longest_matching_prefix_for_0_is_1_6108": "Il prefisso di corrispondenza più lungo per '{0}' è '{1}'.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Verrà eseguita la ricerca nella cartella 'node_modules'. Percorso iniziale: '{0}'.", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Impostare tutte le chiamate a 'super()' come prima istruzione nel costruttore", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "<PERSON><PERSON><PERSON> keyof in modo che restituisca solo stringhe invece di stringhe, numeri o simboli. Opzione legacy.", "Make_super_call_the_first_statement_in_the_constructor_90002": "Impostare la chiamata a 'super()' come prima istruzione nel costruttore", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "Il tipo di oggetto con mapping contiene implicitamente un tipo di modello 'any'.", "Matched_0_condition_1_6403": "Corrispondenza tra '{0}' condizione '{1}'.", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "Corrispondente per impostazione predefinita al criterio di inclusione '**/*'", "Matched_by_include_pattern_0_in_1_1407": "Corrispondenza tramite criterio di inclusione '{0}' in '{1}'", "Member_0_implicitly_has_an_1_type_7008": "Il membro '{0}' contiene implicitamente un tipo '{1}'.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "Il membro '{0}' include implicitamente un tipo '{1}', ma è possibile dedurre un tipo migliore dall'utilizzo.", "Merge_conflict_marker_encountered_1185": "È stato rilevato un indicatore di conflitti di merge.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "La dichiarazione '{0}' sottoposta a merge non può includere una dichiarazione di esportazione predefinita. Provare ad aggiungere una dichiarazione 'export default {0}' distinta.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "La metaproprietà '{0}' è consentita solo nel corpo di una dichiarazione di funzione, di un'espressione di funzione o di un costruttore.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "Il metodo '{0}' non può includere un'implementazione perché è contrassegnato come astratto.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "Il metodo '{0}' dell'interfaccia esportata ha o usa il nome '{1}' del modulo privato '{2}'.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "Il metodo '{0}' dell'interfaccia esportata ha o usa il nome privato '{1}'.", "Method_not_implemented_95158": "Metodo non implementato.", "Modifiers_cannot_appear_here_1184": "In questo punto non è possibile usare modificatori.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "Il modulo '{0}' può essere importato come predefinito solo con il flag '{1}'", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "Non è possibile importare il modulo '{0}' utilizzando questo costrutto. L'identificatore può essere solo risolto in un modulo ES, che non può essere importato con 'require'. Usare invece un'importazione ECMAScript.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "Il modulo '{0}' dichiara '{1}' in locale, ma viene esportato come '{2}'.", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "Il modulo '{0}' dichiara '{1}' in locale, ma non viene esportato.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "Il modulo '{0}' non fa riferimento a un tipo, ma viene usato come tipo in questo punto. Si intendeva 'typeof import('{0}')'?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "Il modulo '{0}' non fa riferimento a un valore, ma qui viene usato come valore.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "Il modulo {0} ha già esportato un membro denominato '{1}'. Per risolvere l'ambiguità, provare a esportarlo di nuovo in modo esplicito.", "Module_0_has_no_default_export_1192": "Per il modulo '{0}' non esistono esportazioni predefinite.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "Non esiste alcuna esportazione predefinita per il modulo '{0}'. Si intendeva usare 'import { {1} } from {0}'?", "Module_0_has_no_exported_member_1_2305": "Il modulo '{0}' non contiene un membro esportato '{1}'.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "Non esiste alcun membro esportato '{1}' per il modulo '{0}'. Si intendeva usare 'import {1} from {0}'?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "Il modulo '{0}' è nascosto da una dichiarazione locale con lo stesso nome.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "Il modulo '{0}' usa 'export =' e non può essere usato con 'export *'.", "Module_0_was_resolved_as_ambient_module_declared_in_1_since_this_file_was_not_modified_6145": "Il modulo '{0}' è stato risolto come modulo di ambiente dichiarato in '{1}' dal momento che questo file non è stato modificato.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "Il modulo '{0}' è stato risolto come modulo di ambiente dichiarato in locale nel file '{1}'.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "Il modulo '{0}' è stato risolto in '{1}', ma '--jsx' non è impostato.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "Il modulo '{0}' è stato risolto in '{1}', ma '--resolveJsonModule' non viene usato.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "I nomi delle dichiarazioni di modulo possono usare solo stringhe racchiuse tra virgolette.", "Module_name_0_matched_pattern_1_6092": "Nome del modulo: '{0}'. Criterio corrispondente: '{1}'.", "Module_name_0_was_not_resolved_6090": "======== Il nome del modulo '{0}' non è stato risolto. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== Il nome del modulo '{0}' è stato risolto in '{1}'. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== Il nome del modulo '{0}' è stato risolto in '{1}' con ID pacchetto '{2}'. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "Il tipo di risoluzione del modulo non è specificato. Verrà usato '{0}'.", "Module_resolution_using_rootDirs_has_failed_6111": "La risoluzione del modulo con 'rootDirs' non è riuscita.", "Modules_6244": "<PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Spostare i modificatori di elemento tupla con etichetta nelle etichette", "Move_to_a_new_file_95049": "Passare a un nuovo file", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "Non sono consentiti più separatori numerici consecutivi.", "Multiple_constructor_implementations_are_not_allowed_2392": "Non è possibile usare più implementazioni di costruttore.", "NEWLINE_6061": "NUOVA RIGA", "Name_is_not_valid_95136": "Nome non valido.", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "Le proprietà denominate '{0}' dei tipi '{1}' e '{2}' non sono identiche.", "Namespace_0_has_no_exported_member_1_2694": "Lo spazio dei nomi '{0}' non contiene un membro esportato '{1}'.", "Namespace_must_be_given_a_name_1437": "È necessario assegnare un nome allo spazio dei nomi.", "Namespace_name_cannot_be_0_2819": "Lo spazio dei nomi non può essere '{0}'.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "Nessun costruttore di base contiene il numero specificato di argomenti tipo.", "No_constituent_of_type_0_is_callable_2755": "Non è possibile chiamare nessun costituente di tipo '{0}'.", "No_constituent_of_type_0_is_constructable_2759": "Non è possibile costruire nessun costituente di tipo '{0}'.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "Non è stata trovata alcuna firma dell'indice con un parametro di tipo '{0}' nel tipo '{1}'.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "Non sono stati trovati input nel file config '{0}'. Percorsi 'include' specificati: '{1}'. Percorsi 'exclude' specificati: '{2}'.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Non più supportato. Nelle versioni precedenti imposta manualmente la codifica del testo per la lettura dei file.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "<PERSON><PERSON><PERSON> overload prevede {0} argomenti, ma esistono overload che prevedono {1} o {2} argomenti.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "Nessun overload prevede {0} argomenti di tipo, ma esistono overload che prevedono {1} o {2} argomenti di tipo.", "No_overload_matches_this_call_2769": "<PERSON><PERSON><PERSON> overload corrisponde a questa chiamata.", "No_type_could_be_extracted_from_this_type_node_95134": "Non è stato possibile estrarre il tipo da questo nodo di tipo", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "Non esiste alcun valore nell'ambito per la proprietà a sintassi abbreviata '{0}'. Dichiararne uno o specificare un inizializzatore.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "La classe non astratta '{0}' non implementa il membro astratto ereditato '{1}' della classe '{2}'.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "L'espressione di classe non astratta non implementa il membro astratto ereditato '{0}' dalla classe '{1}'.", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "Le asserzioni non Null possono essere usate solo in file TypeScript.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "I percorsi non relativi non sono consentiti quando 'baseUrl' non è impostato. Si è dimenticato di aggiungere './' all'inizio?", "Non_simple_parameter_declared_here_1348": "In questo punto è dichiarato un parametro non semplice.", "Not_all_code_paths_return_a_value_7030": "Non tutti i percorsi del codice restituiscono un valore.", "Not_all_constituents_of_type_0_are_callable_2756": "Non tutti i costituenti di tipo '{0}' possono essere chiamati.", "Not_all_constituents_of_type_0_are_constructable_2760": "Non tutti i costituenti di tipo '{0}' possono essere costruiti.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "I valori letterali numerici con valori assoluti uguali o maggiori di 2^53 sono troppo grandi per essere rappresentati in modo corretto come numeri interi.", "Numeric_separators_are_not_allowed_here_6188": "I separatori numerici non sono consentiti in questa posizione.", "Object_is_of_type_unknown_2571": "L'oggetto è di tipo 'unknown'.", "Object_is_possibly_null_2531": "L'oggetto è probabilmente 'null'.", "Object_is_possibly_null_or_undefined_2533": "L'oggetto è probabilmente 'null' o 'undefined'.", "Object_is_possibly_undefined_2532": "L'oggetto è probabilmente 'undefined'.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "Il valore letterale di oggetto può specificare solo proprietà note e '{0}' non esiste nel tipo '{1}'.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "Il valore letterale dell'oggetto può specificare solo proprietà note, ma '{0}' non esiste nel tipo '{1}'. Si intendeva scrivere '{2}'?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "La proprietà '{0}' del valore letterale di oggetto contiene implicitamente un tipo '{1}'.", "Octal_digit_expected_1178": "È prevista la cifra ottale.", "Octal_literal_types_must_use_ES2015_syntax_Use_the_syntax_0_8017": "I tipi di valori letterali ottali devono usare la sintassi ES2015. Usare la sintassi '{0}'.", "Octal_literals_are_not_allowed_in_enums_members_initializer_Use_the_syntax_0_8018": "I valori letterali ottali non sono consentiti nell'inizializzatore di membri di enumerazioni. Usare la sintassi '{0}'.", "Octal_literals_are_not_allowed_in_strict_mode_1121": "I valori letterali ottali non sono consentiti in modalità strict.", "Octal_literals_are_not_available_when_targeting_ECMAScript_5_and_higher_Use_the_syntax_0_1085": "I valori letterali ottali non sono disponibili quando la destinazione è ECMAScript 5 e versioni successive. Usare la sintassi '{0}'.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "In un'istruzione 'for...in' è consentita solo una singola dichiarazione di variabile.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "In un'istruzione 'for...of' è consentita solo una singola dichiarazione di variabile.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Con la parola chiave 'new' può essere chiamata solo una funzione void.", "Only_ambient_modules_can_use_quoted_names_1035": "I nomi delimitati si possono usare solo nei moduli di ambiente.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Unitamente a --{0} sono supportati solo i moduli 'amd' e 'system'.", "Only_emit_d_ts_declaration_files_6014": "<PERSON><PERSON><PERSON> solo i file di dichiarazione '.d.ts'.", "Only_named_exports_may_use_export_type_1383": "Solo le esportazioni denominate possono usare 'export type'.", "Only_numeric_enums_can_have_computed_members_but_this_expression_has_type_0_If_you_do_not_need_exhau_18033": "Solo le enumerazioni numeriche possono includere membri calcolati, ma il tipo di questa espressione è '{0}'. Se non sono necessari controlli di esaustività, provare a usare un valore letterale di oggetto.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Restituisce solo file d.ts e non file JavaScript.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Con la parola chiave 'super' è possibile accedere solo ai metodi pubblico e protetto della classe di base.", "Operator_0_cannot_be_applied_to_type_1_2736": "Non è possibile applicare l'operatore '{0}' al tipo '{1}'.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "Non è possibile applicare l'operatore '{0}' ai tipi '{1}' e '{2}'.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Esclude un progetto dal controllo dei riferimenti a più progetti durante la modifica.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "L'opzione '{0}' può essere specificata solo nel file 'tsconfig.json' oppure impostata su 'false' o 'null' sulla riga di comando.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "L'opzione '{0}' può essere specificata solo nel file 'tsconfig.json' oppure impostata su 'null' sulla riga di comando.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "L'opzione '{0}' può essere usata solo quando si specifica l'opzione '--inlineSourceMap' o '--sourceMap'.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "Non è possibile specificare l'opzione '{0}' quando l'opzione 'jsx' è '{1}'.", "Option_0_cannot_be_specified_when_option_target_is_ES3_5048": "Non è possibile specificare l'opzione '{0}' quando l'opzione 'target' è 'ES3'.", "Option_0_cannot_be_specified_with_option_1_5053": "Non è possibile specificare l'opzione '{0}' insieme all'opzione '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "Non è possibile specificare l'opzione '{0}' senza l'opzione '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "Non è possibile specificare l'opzione '{0}' senza l'opzione'{1}' o '{2}'.", "Option_build_must_be_the_first_command_line_argument_6369": "L'opzione '--build' deve essere il primo argomento della riga di comando.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "È possibile specificare l'opzione '--incremental' solo se si usa tsconfig, si crea un singolo file o si specifica l'opzione '--tsBuildInfoFile'.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "L'opzione 'isolatedModules' può essere usata solo quando si specifica l'opzione '--module' oppure il valore dell'opzione 'target' è 'ES2015' o maggiore.", "Option_preserveConstEnums_cannot_be_disabled_when_isolatedModules_is_enabled_5091": "Non è possibile disabilitare l'opzione 'preserveConstEnums' quando l'opzione 'isolatedModules' è abilitata.", "Option_preserveValueImports_can_only_be_used_when_module_is_set_to_es2015_or_later_5095": "L'opzione 'preserveValueImports' può essere usata solo quando 'module' è impostato su 'es2015' o versione successiva.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "Non è possibile combinare l'opzione 'project' con file di origine in una riga di comando.", "Option_resolveJsonModule_can_only_be_specified_when_module_code_generation_is_commonjs_amd_es2015_or_5071": "È possibile specificare l'opzione '--resolveJsonModule' solo quando la generazione del codice del modulo è impostata su 'commonjs', 'amd', 'es2015' o 'esNext'.", "Option_resolveJsonModule_cannot_be_specified_without_node_module_resolution_strategy_5070": "Non è possibile specificare l'opzione '--resolveJsonModule' senza la strategia di risoluzione del modulo 'node'.", "Options_0_and_1_cannot_be_combined_6370": "Non è possibile combinare le opzioni '{0}' e '{1}'.", "Options_Colon_6027": "Opzioni:", "Output_Formatting_6256": "Formattazione dell'output", "Output_compiler_performance_information_after_building_6615": "Restituisce informazioni sulle prestazioni del compilatore dopo la compilazione.", "Output_directory_for_generated_declaration_files_6166": "Directory di output per i file di dichiarazione generati.", "Output_file_0_from_project_1_does_not_exist_6309": "Il file di output '{0}' del progetto '{1}' non esiste", "Output_file_0_has_not_been_built_from_source_file_1_6305": "Il file di output '{0}' non è stato compilato dal file di origine '{1}'.", "Output_from_referenced_project_0_included_because_1_specified_1411": "L'output del progetto di riferimento '{0}' è incluso perché è stato specificato '{1}'", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "L'output del progetto di riferimento '{0}' è incluso perché il valore specificato per '--module' è 'none'", "Output_more_detailed_compiler_performance_information_after_building_6632": "Restituisce informazioni più dettagliate sulle prestazioni del compilatore dopo la compilazione.", "Overload_0_of_1_2_gave_the_following_error_2772": "L'overload {0} di {1},'{2}', ha restituito l'errore seguente.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Le firme di overload devono essere tutte astratte o tutte non astratte.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Le firme di overload devono essere tutte di ambiente o non di ambiente.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Le firme di overload devono essere tutte esportate o tutte non esportate.", "Overload_signatures_must_all_be_optional_or_required_2386": "Le firme di overload devono essere tutte facoltative o obbligatorie.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Le firme di overload devono essere tutte pubbliche, private o protette.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "Il parametro '{0}' non può fare riferimento all'identificatore '{1}' dichiarato dopo di esso.", "Parameter_0_cannot_reference_itself_2372": "Il parametro '{0}' non può fare riferimento a se stesso.", "Parameter_0_implicitly_has_an_1_type_7006": "Il parametro '{0}' contiene implicitamente un tipo '{1}'.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "Il parametro '{0}' include implicitamente un tipo '{1}', ma è possibile dedurre un tipo migliore dall'utilizzo.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "Il parametro '{0}' non si trova nella stessa posizione del parametro '{1}'.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "Il parametro '{0}' della funzione di accesso contiene o usa il nome '{1}' del modulo esterno {2}, ma non può essere rinominato.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "Il parametro '{0}' della funzione di accesso contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "Il parametro '{0}' della funzione di accesso contiene o usa il nome privato '{1}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "Il parametro '{0}' della firma di chiamata dell'interfaccia esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "Il parametro '{0}' della firma di chiamata dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "Il parametro '{0}' del costruttore della classe esportata contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominato.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "Il parametro '{0}' del costruttore della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "Il parametro '{0}' del costruttore della classe esportata contiene o usa il nome privato '{1}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "Il parametro '{0}' della firma del costruttore dell'interfaccia esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "Il parametro '{0}' della firma del costruttore dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "Il parametro '{0}' della funzione esportata contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominato.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "Il parametro '{0}' della funzione esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "Il parametro '{0}' della funzione esportata contiene o usa il nome privato '{1}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "Il parametro '{0}' della firma dell'indice dell'interfaccia esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "Il parametro '{0}' della firma dell'indice dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "Il parametro '{0}' del metodo dell'interfaccia esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "Il parametro '{0}' del metodo dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "Il parametro '{0}' del metodo pubblico della classe esportata contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominato.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "Il parametro '{0}' del metodo pubblico della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "Il parametro '{0}' del metodo pubblico della classe esportata contiene o usa il nome privato '{1}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "Il parametro '{0}' del metodo statico pubblico della classe esportata contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominato.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "Il parametro '{0}' del metodo statico pubblico della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "Il parametro '{0}' del metodo statico pubblico della classe esportata contiene o usa il nome privato '{1}'.", "Parameter_cannot_have_question_mark_and_initializer_1015": "Il parametro non può contenere il punto interrogativo e l'inizializzatore.", "Parameter_declaration_expected_1138": "È prevista la dichiarazione di parametro.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "Il parametro include un nome ma non un tipo. Si intendeva '{0}: {1}'?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "I modificatori di parametro possono esere usati solo in file TypeScript.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "Il tipo di parametro del setter pubblico '{0}' della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "Il tipo di parametro del setter pubblico '{0}' della classe esportata contiene o usa il nome privato '{1}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "Il tipo di parametro del setter statico pubblico '{0}' della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "Il tipo di parametro del setter statico pubblico '{0}' della classe esportata contiene o usa il nome privato '{1}'.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Esegue l'analisi in modalità strict e crea la direttiva \"use strict\" per ogni file di origine.", "Part_of_files_list_in_tsconfig_json_1409": "Parte dell'elenco 'files' in tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "Il criterio '{0}' deve contenere al massimo un carattere '*'.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Gli intervalli delle prestazioni per '--Diagnostics' o '--extendedDiagnostics' non sono disponibili in questa sessione. Non è stato possibile trovare un'implementazione nativa dell'API Prestazioni Web.", "Platform_specific_6912": "Specifico della piattaforma", "Prefix_0_with_an_underscore_90025": "Anteporre un carattere di sottolineatura a '{0}'", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Aggiungere 'declare' come prefisso a tutte le dichiarazioni di proprietà non corrette", "Prefix_all_unused_declarations_with_where_possible_95025": "Aggiungere a tutte le dichiarazioni non usate il prefisso '_', laddove possibile", "Prefix_with_declare_95094": "Aggiungere il prefisso 'declare'", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Conserva i valori importati non usati nell'output JavaScript che altrimenti verrebbe rimosso.", "Print_all_of_the_files_read_during_the_compilation_6653": "Stampa tutti i file letti durante la compilazione.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Stampa i file letti durante la compilazione e indica il motivo per cui sono stati inclusi.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Stampa i nomi dei file e il motivo per cui fanno parte della compilazione.", "Print_names_of_files_part_of_the_compilation_6155": "Stampa i nomi dei file che fanno parte della compilazione.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Stampa i nomi dei file che fanno parte della compilazione, quindi arresta l'elaborazione.", "Print_names_of_generated_files_part_of_the_compilation_6154": "Stampa i nomi dei file generati che fanno parte della compilazione.", "Print_the_compiler_s_version_6019": "Stampa la versione del compilatore.", "Print_the_final_configuration_instead_of_building_1350": "Stampa la configurazione finale invece di eseguire la compilazione.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Stampa i nomi dei file creati al termine di una compilazione.", "Print_this_message_6017": "Stampa questo messaggio.", "Private_accessor_was_defined_without_a_getter_2806": "La funzione di accesso privata è stata definita senza un getter.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "Gli identificatori privati non sono consentiti nelle dichiarazioni di variabili.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "Gli identificatori privati non sono consentiti all'esterno del corpo della classe.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Gli identificatori privati sono consentiti solo nei corpi di classe e possono essere usati solo come parte di una dichiarazione di un membro della classe, dell'accesso alle proprietà o sulla parte sinistra di un'espressione 'in'.", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Gli identificatori privati sono disponibili solo se destinati a ECMAScript 2015 e versioni successive.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Non è possibile usare gli identificatori privati come parametri.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "Non è possibile accedere al membro privato o protetto '{0}' in un parametro di tipo.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Non è possibile compilare il progetto '{0}' perché la dipendenza '{1}' contiene errori", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Non è possibile compilare il progetto '{0}' perché la relativa dipendenza '{1}' non è stata compilata", "Project_0_is_being_forcibly_rebuilt_6388": "Il progetto '{0}' è stato ricompilato forzatamente", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "Il '{0}' del progetto non è aggiornato perché il file buildinfo '{1}' indica che alcune modifiche non sono state generate", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "Il progetto '{0}' non è aggiornato perché la dipendenza '{1}' non è aggiornata", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "Il progetto '{0}' non è aggiornato perché l'output '{1}' è meno recente dell'input '{2}'", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "Il progetto '{0}' non è aggiornato perché il file di output '{1}' non esiste", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "Il progetto '{0}' non è aggiornato perché l'output per il progetto è stato generato con la versione '{1}' che non corrisponde alla versione corrente '{2}'", "Project_0_is_out_of_date_because_output_of_its_dependency_1_has_changed_6372": "Il progetto '{0}' non è aggiornato perché l'output della relativa dipendenza '{1}' è cambiato", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "Il progetto '{0}' non è aggiornato perché si è verificato un errore durante la lettura del file '{1}'", "Project_0_is_up_to_date_6361": "Il progetto '{0}' è aggiornato", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "Il progetto '{0}' è aggiornato perché l'input più recente '{1}' è meno recente dell'output '{2}'", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "Project '{0}' è aggiornato ma deve aggiornare i timestamp dei file di output precedenti ai file di input", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "Il progetto '{0}' è aggiornato con i file con estensione d.ts delle relative dipendenze", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "I riferimenti al progetto non possono formare un grafico circolare. Ciclo rilevato: {0}", "Projects_6255": "<PERSON><PERSON><PERSON>", "Projects_in_this_build_Colon_0_6355": "<PERSON><PERSON><PERSON> in questa compilazione: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Le proprietà con il modificatore 'funzione di accesso' sono disponibili solo quando la destinazione è ECMAScript 2015 e versioni successive.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "La proprietà '{0}' non può includere un inizializzatore perché è contrassegnata come astratta.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "La proprietà '{0}' deriva da una firma dell'indice, quindi è necessario accedervi con ['{0}'].", "Property_0_does_not_exist_on_type_1_2339": "La proprietà '{0}' non esiste nel tipo '{1}'.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "La proprietà '{0}' non esiste nel tipo '{1}'. Si intendeva '{2}'?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "La proprietà '{0}' non esiste nel tipo '{1}'. Si intendeva accedere al membro statico '{2}'?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "La proprietà '{0}' non esiste nel tipo '{1}'. È necessario modificare la libreria di destinazione? Provare a impostare l'opzione 'lib' del compilatore su '{2}' o versioni successive.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "La proprietà '{0}' non esiste nel tipo '{1}'. Provare a modificare l'opzione del compilatore 'lib' per includere 'dom'.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "La proprietà '{0}' non include alcun inizializzatore e non viene assolutamente assegnata in un blocco statico di classe.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "La proprietà '{0}' non include alcun inizializzatore e non viene assolutamente assegnata nel costruttore.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "La proprietà '{0}' contiene implicitamente il tipo 'any', perché nella relativa funzione di accesso get manca un'annotazione di tipo restituito.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "La proprietà '{0}' contiene implicitamente il tipo 'any', perché nella relativa funzione di accesso set manca un'annotazione di tipo di parametro.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "La proprietà '{0}' contiene implicitamente il tipo 'any', ma è possibile dedurre un tipo migliore per la funzione di accesso get dall'utilizzo.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "La proprietà '{0}' contiene implicitamente il tipo 'any', ma è possibile dedurre un tipo migliore per la funzione di accesso set dall'utilizzo.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "La proprietà '{0}' nel tipo '{1}' non è assegnabile alla stessa proprietà nel tipo di base '{2}'.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "La proprietà '{0}' nel tipo '{1}' non è assegnabile al tipo '{2}'.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "La proprietà '{0}' nel tipo '{1}' fa riferimento a un membro diverso a cui non è possibile accedere dall'interno del tipo '{2}'.", "Property_0_is_declared_but_its_value_is_never_read_6138": "La proprietà '{0}' è dichiarata, ma il suo valore non viene mai letto.", "Property_0_is_incompatible_with_index_signature_2530": "La proprietà '{0}' non è compatibile con la firma dell'indice.", "Property_0_is_missing_in_type_1_2324": "Nel tipo '{1}' manca la proprietà '{0}'.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "La proprietà '{0}' manca nel tipo '{1}', ma è obbligatoria nel tipo '{2}'.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "La proprietà '{0}' non è accessibile all'esterno della classe '{1}' perché contiene un identificatore privato.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "La proprietà '{0}' è facoltativa nel tipo '{1}', ma obbligatoria nel tipo '{2}'.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "La proprietà '{0}' è privata e accessibile solo all'interno della classe '{1}'.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "La proprietà '{0}' è privata nel tipo '{1}', ma non nel tipo '{2}'.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "La proprietà '{0}' è protetta e accessibile solo tramite un'istanza della classe '{1}'. Si tratta di un'istanza della classe '{2}'.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "La proprietà '{0}' è protetta e accessibile solo all'interno della classe '{1}' e delle relative sottoclassi.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "La proprietà '{0}' è protetta, ma il tipo '{1}' non è una classe derivata da '{2}'.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "La proprietà '{0}' è protetta nel tipo '{1}', ma è pubblica non nel tipo '{2}'.", "Property_0_is_used_before_being_assigned_2565": "La proprietà '{0}' viene usata prima dell'assegnazione.", "Property_0_is_used_before_its_initialization_2729": "La proprietà '{0}' viene usata prima della relativa inizializzazione.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "La proprietà '{0}' potrebbe non esistere nel tipo '{1}'. Si intendeva '{2}'?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "La proprietà '{0}' dell'attributo spread JSX non è assegnabile alla proprietà di destinazione.", "Property_0_of_exported_class_expression_may_not_be_private_or_protected_4094": "La proprietà '{0}' dell'espressione di classe esportata potrebbe essere non privata o protetta.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "La proprietà '{0}' dell'interfaccia esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "La proprietà '{0}' dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "La proprietà '{0}' del tipo '{1}' non è assegnabile al tipo di indice '{2}' '{3}'.", "Property_0_was_also_declared_here_2733": "In questo punto è dichiarata anche la proprietà '{0}'.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "La proprietà '{0}' sovrascriverà la proprietà di base in '{1}'. Se questo comportamento è intenzionale, aggiungere un inizializzatore; in caso contrario, aggiungere un modificatore 'declare' o rimuovere la dichiarazione ridondante.", "Property_assignment_expected_1136": "È prevista l'assegnazione di proprietà.", "Property_destructuring_pattern_expected_1180": "È previsto il criterio di destrutturazione della proprietà.", "Property_or_signature_expected_1131": "È prevista la proprietà o la firma.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "Il valore della proprietà può essere solo un valore letterale stringa, un valore letterale numerico, 'true', 'false', 'null', un valore letterale di oggetto o un valore letterale di matrice.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_or_ES3_6179": "Fornisce supporto completo per elementi iterabili in 'for-of', spread e destrutturazione quando la destinazione è 'ES5' o 'ES3'.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "Il metodo pubblico '{0}' della classe esportata ha o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominato.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "Il metodo pubblico '{0}' della classe esportata ha o usa il nome '{1}' del modulo privato '{2}'.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "Il metodo pubblico '{0}' della classe esportata ha o usa il nome privato '{1}'.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "La proprietà pubblica '{0}' della classe esportata contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominata.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "La proprietà pubblica '{0}' della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "La proprietà pubblica '{0}' della classe esportata contiene o usa il nome privato '{1}'.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "Il metodo statico pubblico '{0}' della classe esportata contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominato.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "Il metodo statico pubblico '{0}' della classe esportata ha o usa il nome '{1}' del modulo privato '{2}'.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "Il metodo statico pubblico '{0}' della classe esportata ha o usa il nome privato '{1}'.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "La proprietà statica pubblica '{0}' della classe esportata contiene o usa il nome '{1}' del modulo esterno {2} ma non può essere rinominata.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "La proprietà statica pubblica '{0}' della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "La proprietà statica pubblica '{0}' della classe esportata contiene o usa il nome privato '{1}'.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "Il nome completo '{0}' non è consentito se non si specifica un parametro '@param {object} {1}' iniziale.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "Genera un errore quando un parametro di funzione non viene letto.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Genera un errore in caso di espressioni o dichiarazioni con tipo 'any' implicito.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Genera un errore in caso di espressioni 'this con un tipo 'any' implicito.", "Re_exporting_a_type_when_the_isolatedModules_flag_is_provided_requires_using_export_type_1205": "Per riesportare un tipo quando è specificato il flag '--isolatedModules', è necessario usare 'export type'.", "Redirect_output_structure_to_the_directory_6006": "Reindirizza la struttura di output alla directory.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "Riduce il numero di progetti caricati automaticamente da TypeScript.", "Referenced_project_0_may_not_disable_emit_6310": "Il progetto di riferimento '{0}' non può disabilitare la creazione.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "Il progetto di riferimento '{0}' deve includere l'impostazione \"composite\": true.", "Referenced_via_0_from_file_1_1400": "Riferimento tramite '{0}' dal file '{1}'", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2834": "I percorsi di importazione relativi necessitano di estensioni di file esplicite nelle importazioni EcmaScript quando '--moduleResolution' è 'node16' o 'nodenext'. Provare ad aggiungere un'estensione al percorso di importazione.", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2835": "I percorsi di importazione relativi necessitano di estensioni di file esplicite nelle importazioni EcmaScript quando '--moduleResolution' è 'node16' o 'nodenext'. Si intendeva \"{0}\"?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Rimuove un elenco di directory dal processo dell'espressione di controllo.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Rimuove un elenco di file dall'elaborazione della modalità espressione di controllo.", "Remove_all_unnecessary_override_modifiers_95163": "Rimuovere tutti i modificatori 'override' non necessari", "Remove_all_unnecessary_uses_of_await_95087": "Rimuovere tutti gli utilizzi non necessari di 'await'", "Remove_all_unreachable_code_95051": "Rimuovere tutto il codice non eseguibile", "Remove_all_unused_labels_95054": "Rimuovere tutte le etichette inutilizzate", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Rimuovere le parentesi graffe da tutti i corpi della funzione arrow con problemi specifici", "Remove_braces_from_arrow_function_95060": "Rimuovere le parentesi graffe dalla funzione arrow", "Remove_braces_from_arrow_function_body_95112": "Rimuovere le parentesi graffe dal corpo della funzione arrow", "Remove_import_from_0_90005": "Rimuovere l'importazione da '{0}'", "Remove_override_modifier_95161": "Rimuovere il modificatore 'override'", "Remove_parentheses_95126": "R<PERSON><PERSON><PERSON> le parentesi", "Remove_template_tag_90011": "Rimuovere il tag template", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Rimuove il limite di 20 MB per le dimensioni totali del codice sorgente relativo ai file JavaScript nel server di linguaggio TypeScript.", "Remove_type_from_import_declaration_from_0_90055": "Rimuovi 'type' dalla dichiarazione di importazione da \"{0}\"", "Remove_type_from_import_of_0_from_1_90056": "Rimuovi 'type' dall'importazione di '{0}' da \"{1}\"", "Remove_type_parameters_90012": "Rimuovere i parametri di tipo", "Remove_unnecessary_await_95086": "Rimuovere l'elemento 'await' non necessario", "Remove_unreachable_code_95050": "Rimuovere il codice non eseguibile", "Remove_unused_declaration_for_Colon_0_90004": "Rimuovere la dichiarazione inutilizzata per: '{0}'", "Remove_unused_declarations_for_Colon_0_90041": "Rimuovere le dichiarazioni inutilizzate per: '{0}'", "Remove_unused_destructuring_declaration_90039": "Rimuovere la dichiarazione di destrutturazione inutilizzata", "Remove_unused_label_95053": "Rimuovere l'etichetta inutilizzata", "Remove_variable_statement_90010": "Rimuovere l'istruzione di variabile", "Rename_param_tag_name_0_to_1_95173": "Cambiane il nome '{0}' del tag '@param' in '{1}'", "Replace_0_with_Promise_1_90036": "Sostituire '{0}' con 'Promise<{1}>'", "Replace_all_unused_infer_with_unknown_90031": "Sostituire tutti gli elementi 'infer' inutilizzati con 'unknown'", "Replace_import_with_0_95015": "Sostituire l'importazione con '{0}'.", "Replace_infer_0_with_unknown_90030": "Sostituire 'infer {0}' con 'unknown'", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "Segnala l'errore quando non tutti i percorsi del codice nella funzione restituiscono un valore.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "Segnala errori per i casi di fallthrough nell'istruzione switch.", "Report_errors_in_js_files_8019": "Segnala gli errori presenti nei file con estensione js.", "Report_errors_on_unused_locals_6134": "Segnala errori relativi a variabili locali non usate.", "Report_errors_on_unused_parameters_6135": "Segnala errori relativi a parametri non usati.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "Richiedere alle proprietà non dichiarate da firme dell'indice di usare gli accessi agli elementi.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "I parametri di tipo obbligatori potrebbero non seguire i parametri di tipo facoltativi.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "La risoluzione per il modulo '{0}' è stata trovata nella cache dal percorso '{1}'.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "La risoluzione per la direttiva '{0}' del riferimento al tipo è stata trovata nella cache dal percorso '{1}'.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "<PERSON><PERSON><PERSON><PERSON> 'keyof' solo in nomi di proprietà con valori stringa (senza numeri o simboli).", "Resolving_in_0_mode_with_conditions_1_6402": "Risoluzione in modalità {0} con condizioni {1}.", "Resolving_module_0_from_1_6086": "======== Risoluzione del modulo '{0}' da '{1}'. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Verrà eseguita la risoluzione del nome del modulo '{0}' relativo all'URL di base '{1}' - '{2}'.", "Resolving_real_path_for_0_result_1_6130": "Risoluzione del percorso reale per '{0}'. Risultato: '{1}'.", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Risoluzione della direttiva '{0}' del riferimento al tipo contenente il file '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Risoluzione della direttiva '{0}' del riferimento al tipo contenente il file '{1}' con directory radice '{2}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Risoluzione della direttiva '{0}' del riferimento al tipo contenente il file '{1}' e directory radice non impostata. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Risoluzione della direttiva '{0}' del riferimento al tipo contenente il file non impostato con directory radice '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Risoluzione della direttiva '{0}' del riferimento al tipo contenente il file non impostato con directory radice non impostata. ========", "Resolving_with_primary_search_path_0_6121": "La risoluzione verrà eseguita con il percorso di ricerca primaria '{0}'.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Il parametro rest '{0}' contiene implicitamente un tipo 'any[]'.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Il parametro rest '{0}' contiene implicitamente un tipo 'any[]', ma è possibile dedurre un tipo migliore dall'utilizzo.", "Rest_types_may_only_be_created_from_object_types_2700": "È possibile creare tipi rest solo da tipi di oggetto.", "Return_type_annotation_circularly_references_itself_2577": "L'annotazione di tipo restituito contiene un riferimento circolare a se stessa.", "Return_type_must_be_inferred_from_a_function_95149": "Il tipo restituito deve essere dedotto da una funzione", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "Il tipo restituito della firma di chiamata dell'interfaccia esportata contiene o usa il nome '{0}' del modulo privato '{1}'.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "Il tipo restituito della firma di chiamata dell'interfaccia esportata contiene o usa il nome privato '{0}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "Il tipo restituito della firma del costruttore dell'interfaccia esportata contiene o usa il nome '{0}' del modulo privato '{1}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "Il tipo restituito della firma del costruttore dell'interfaccia esportata contiene o usa il nome privato '{0}'.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "Il tipo restituito della firma del costruttore deve essere assegnabile al tipo di istanza della classe.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "Il tipo restituito della funzione esportata contiene o usa il nome '{0}' del modulo esterno {1} ma non può essere rinominato.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "Il tipo restituito della funzione esportata contiene o usa il nome '{0}' del modulo privato '{1}'.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "Il tipo restituito della funzione esportata contiene o usa il nome privato '{0}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "Il tipo restituito della firma dell'indice dell'interfaccia esportata contiene o usa il nome '{0}' del modulo privato '{1}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "Il tipo restituito della firma dell'indice dell'interfaccia esportata contiene o usa il nome privato '{0}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "Il tipo restituito del metodo dell'interfaccia esportata contiene o usa il nome '{0}' del modulo privato '{1}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "Il tipo restituito del metodo dell'interfaccia esportata contiene o usa il nome privato '{0}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "Il tipo restituito del getter pubblico '{0}' della classe esportata contiene o usa il nome '{1}' del modulo esterno {2}, ma non può essere rinominato.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "Il tipo restituito del getter pubblico '{0}' della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "Il tipo restituito del getter pubblico '{0}' della classe esportata contiene o usa il nome privato '{1}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "Il tipo restituito del metodo pubblico della classe esportata contiene o usa il nome '{0}' del modulo esterno {1} ma non può essere rinominato.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "Il tipo restituito del metodo pubblico della classe esportata contiene o usa il nome '{0}' del modulo privato '{1}'.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "Il tipo restituito del metodo pubblico della classe esportata contiene o usa il nome privato '{0}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "Il tipo restituito del getter di proprietà pubblico '{0}' della classe esportata contiene o usa il nome '{1}' del modulo esterno '{2}', ma non può essere rinominato.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "Il tipo restituito del getter di proprietà pubblico '{0}' della classe esportata contiene o usa il nome '{1}' del modulo privato '{2}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "Il tipo restituito del getter statico pubblico '{0}' della classe esportata contiene o usa il nome privato '{1}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "Il tipo restituito del metodo statico pubblico della classe esportata contiene o usa il nome '{0}' del modulo esterno {1} ma non può essere rinominato.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "Il tipo restituito del metodo statico pubblico della classe esportata contiene o usa il nome '{0}' del modulo privato '{1}'.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "Il tipo restituito del metodo statico pubblico della classe esportata contiene o usa il nome privato '{0}'.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "Il riutilizzo della risoluzione del modulo '{0}' da '{1}' che è stato trovato nella cache dal percorso '{2}' non è stato risolto.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "Il riutilizzo della risoluzione del modulo '{0}' da '{1}' che è stato trovato nella cache dal percorso '{2}' è stato risolto in '{3}'.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "Il riutilizzo della risoluzione del modulo '{0}' da '{1}' è stato trovato nella cache dal percorso '{2}'. È stato risolto correttamente in '{3}' con ID pacchetto '{4}'.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "Il riutilizzo della risoluzione del modulo '{0}' da '{1}' del programma precedente non è stato risolto.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "Il riutilizzo della risoluzione del modulo '{0}' da '{1}' del programma precedente è stato risolto in '{2}'.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "Il riutilizzo della risoluzione del modulo '{0}' da '{1}' del programma precedente è stato risolto in '{2}' con l'ID pacchetto '{3}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "Il riutilizzo della risoluzione della direttiva per il tipo di riferimento '{0}' da '{1}' che è stato trovato nella cache dal percorso '{2}' non è stato risolto.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "Il riutilizzo della risoluzione della direttiva per il tipo di riferimento '{0}' da '{1}' che è stato trovato nella cache dal percorso '{2}' è stato risolto in '{3}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "Il riutilizzo della risoluzione della direttiva per il tipo di riferimento '{0}' da '{1}' che è stato trovato nella cache dal percorso '{2}' è stato risolto in '{3}' con ID pacchetto '{4}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "Il riutilizzo della risoluzione della direttiva riferimento di tipo '{0}' da '{1}' del programma precedente non è stato risolto.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "Il riutilizzo della risoluzione della direttiva riferimento di tipo '{0}' da '{1}' del programma precedente è stato risolto correttamente in '{2}'.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "Il riutilizzo della risoluzione della direttiva per il tipo di riferimento '{0}' da '{1}' del programma precedente è stato risolto in '{2}' con l'ID pacchetto '{3}'.", "Rewrite_all_as_indexed_access_types_95034": "Riscrivere tutti come tipi di accesso indicizzati", "Rewrite_as_the_indexed_access_type_0_90026": "Riscrivere come tipo di accesso indicizzato '{0}'", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "Non è possibile determinare la directory radice. I percorsi di ricerca primaria verranno ignorati.", "Root_file_specified_for_compilation_1427": "File radice specificato per la compilazione", "STRATEGY_6039": "STRATEGIA", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Salva i file .tsbuildinfo per consentire la compilazione incrementale dei progetti.", "Saw_non_matching_condition_0_6405": "Visualizzata la condizione di corrispondenza '{0}'.", "Scoped_package_detected_looking_in_0_6182": "Il pacchetto con ambito è stato rilevato. Verrà eseguita una ricerca in '{0}'", "Selection_is_not_a_valid_statement_or_statements_95155": "La selezione non corrisponde a una o più istruzioni valide", "Selection_is_not_a_valid_type_node_95133": "La selezione non corrisponde a un nodo di tipo valido", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Imposta la versione del linguaggio JavaScript per il codice JavaScript creato e include le dichiarazioni di libreria compatibili.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Imposta la lingua della messaggistica da TypeScript. Non influisce sulla creazione.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Impostare l'opzione 'module' nel file di configurazione su '{0}'", "Set_the_newline_character_for_emitting_files_6659": "Imposta il carattere di nuova riga per la creazione di file.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Impostare l'opzione 'target' nel file di configurazione su '{0}'", "Setters_cannot_return_a_value_2408": "I setter non possono restituire un valore.", "Show_all_compiler_options_6169": "Mostra tutte le opzioni del compilatore.", "Show_diagnostic_information_6149": "Mostra le informazioni di diagnostica.", "Show_verbose_diagnostic_information_6150": "Mostra le informazioni di diagnostica dettagliate.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "Mostra gli elementi che vengono compilati (o eliminati, se specificati con l'opzione '--clean')", "Signature_0_must_be_a_type_predicate_1224": "La firma '{0}' deve essere un predicato di tipo.", "Skip_type_checking_all_d_ts_files_6693": "Ignora il controllo del tipo di tutti i file .d.ts.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Ignora il controllo dei tipi dei file .d.ts inclusi con TypeScript.", "Skip_type_checking_of_declaration_files_6012": "Ignora il controllo del tipo dei file di dichiarazione.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "La compilazione del progetto '{0}' verrà ignorata perché la dipendenza '{1}' contiene errori", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "La compilazione del progetto '{0}' verrà ignorata perché la dipendenza '{1}' non è stata compilata", "Source_from_referenced_project_0_included_because_1_specified_1414": "L'origine del progetto di riferimento '{0}' è inclusa perché è stato specificato '{1}'", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "L'origine del progetto di riferimento '{0}' è inclusa perché il valore specificato per '--module' è 'none'", "Source_has_0_element_s_but_target_allows_only_1_2619": "L'origine contiene {0} elemento/i ma la destinazione ne consente solo {1}.", "Source_has_0_element_s_but_target_requires_1_2618": "L'origine contiene {0} elemento/i ma la destinazione ne richiede {1}.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "L'origine non fornisce alcuna corrispondenza per l'elemento obbligatorio alla posizione {0} nella destinazione.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "L'origine non fornisce alcuna corrispondenza per l'elemento variadic alla posizione {0} nella destinazione.", "Specify_ECMAScript_target_version_6015": "Specifica la versione di destinazione di ECMAScript.", "Specify_JSX_code_generation_6080": "Specifica la generazione del codice JSX.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Consente di specificare un file che aggrega tutti gli output in un unico file JavaScript. Se 'declaration' è true, designa anche un file che aggrega tutto l'output dei file .d.ts.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Consente di specificare un elenco di criteri GLOB che corrispondono ai file da includere nella compilazione.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Consente di specificare un elenco di plug-in da includere del servizio di linguaggio.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Consente di specificare un set di file di dichiarazione della libreria aggregati che descrivono l'ambiente di runtime di destinazione.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "Consente di specificare un set di voci che eseguono di nuovo il mapping delle direttive import nei percorsi di ricerca aggiuntivi.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "Consente di specificare un array di oggetti che indicano percorsi per i progetti. Usato nei riferimenti dei progetti.", "Specify_an_output_folder_for_all_emitted_files_6678": "Consente di specificare una cartella di output per tutti i file creati.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Specificare il comportamento di creazione/controllo per le importazioni usate solo per i tipi.", "Specify_file_to_store_incremental_compilation_information_6380": "Specificare il file per l'archiviazione delle informazioni di compilazione incrementale", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "Consente di specificare in che modo TypeScript cerca un file da un identificatore di modulo specifico.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Consente di specifica la modalità di controllo delle directory nei sistemi in cui non sono presenti funzionalità ricorsive di controllo dei file.", "Specify_how_the_TypeScript_watch_mode_works_6715": "Consente di specificare il funzionamento della modalità espressione di controllo TypeScript.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Specificare i file di libreria da includere nella compilazione.", "Specify_module_code_generation_6016": "Specifica la generazione del codice del modulo.", "Specify_module_resolution_strategy_Colon_node_Node_js_or_classic_TypeScript_pre_1_6_6069": "Specifica la strategia di risoluzione del modulo: 'node' (Node.js) o 'classic' (TypeScript prima della versione 1.6).", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Specifica l'identificatore di modulo usato per importare funzioni factory JSX quando si usa 'jsx: react-jsx*'.", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Consente di specificare più cartelle che fungono da './node_modules/@types'.", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Consente di specificare uno o più percorsi o riferimenti al modulo del nodo ai file di configurazione di base da cui vengono ereditate le impostazioni.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Consente di specificare le opzioni per l'acquisizione automatica dei file di dichiarazione.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Specifica la strategia per la creazione di un'espressione di controllo di polling quando non viene creata con eventi del file system: 'FixedInterval' (impostazione predefinita), 'PriorityInterval', 'DynamicPriority', 'FixedChunkSize'.", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Specifica la strategia per il controllo della directory in piattaforme che non supportano il controllo ricorsivo in modo nativo: 'UseFsEvents' (impostazione predefinita), 'FixedPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling'.", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Specifica la strategia per il controllo del file: 'FixedPollingInterval' (impostazione predefinita), 'PriorityPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling', 'UseFsEvents', 'UseFsEventsOnParentDirectory'.", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Consente di specificare il riferimento al fragmento JSX usato per i frammenti quando la destinazione è la creazione JSX React, ad esempio 'React.Fragment' o 'Fragment'.", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Consente di specificare la funzione della factory JSX da usare quando la destinazione è la creazione JSX 'react', ad esempio 'React.createElement' o 'h'.", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Consente di specificare la funzione della factory JSX da usare quando la destinazione è la creazione JSX React, ad esempio 'React.createElement' o 'h'.", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Specificare la funzione della factory di frammenti JSX da usare quando la destinazione è la creazione JSX 'react' quando è specificata l'opzione del compilatore 'jsxFactory', ad esempio 'Fragment'.", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Specificare la directory di base per risolvere i nomi di modulo non relativi.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Specifica la sequenza di fine riga da usare per la creazione dei file, ovvero 'CRLF' (in DOS) o 'LF' (in UNIX).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Specifica il percorso in cui il debugger deve trovare i file TypeScript invece dei percorsi di origine.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Specifica il percorso in cui il debugger deve trovare i file map invece dei percorsi generati.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Consente di specificare la profondità massima della cartella utilizzata per il controllo dei file JavaScript da 'node_modules'. Applicabile solo con 'allowJs'.", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Specificare l'identificatore di modulo da usare da cui importare le funzioni di factory 'jsx' e 'jsxs', ad esempio react", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Consente di specificare l'oggetto richiamato per 'createElement'. Si applica quando la destinazione è la creazione JSX `react`.", "Specify_the_output_directory_for_generated_declaration_files_6613": "Consente di specificare la directory di output per i file di dichiarazione generati.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Consente di specificare il percorso per il file di compilazione incrementale .tsbuildinfo.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Specifica la directory radice dei file di input. Usare per controllare la struttura della directory di output con --outDir.", "Specify_the_root_folder_within_your_source_files_6690": "Consente di specificare la cartella radice nei file di origine.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Consente di specificare il percorso radice per consentire ai debugger di trovare il codice sorgente di riferimento.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Consente di specificare i tipi di nomi dei pacchetti da includere senza farvi riferimento in un file di origine.", "Specify_what_JSX_code_is_generated_6646": "Consente di specificare il codice JSX generato.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "Consente di specificare l'approccio che il watcher deve adottare se il sistema esaurisce i watcher di file nativi.", "Specify_what_module_code_is_generated_6657": "Consente di specificare il codice del modulo generato.", "Split_all_invalid_type_only_imports_1367": "Dividere tutte le importazioni solo di tipi non valide", "Split_into_two_separate_import_declarations_1366": "Dividere in due dichiarazioni di importazione separate", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "L'operatore Spread in espressioni 'new' è disponibile solo se destinato a ECMAScript 5 e versioni successive.", "Spread_types_may_only_be_created_from_object_types_2698": "È possibile creare tipi spread solo da tipi di oggetto.", "Starting_compilation_in_watch_mode_6031": "Avvio della compilazione in modalità espressione di controllo...", "Statement_expected_1129": "È prevista l'istruzione.", "Statements_are_not_allowed_in_ambient_contexts_1036": "Le istruzioni non sono consentite in contesti di ambiente.", "Static_members_cannot_reference_class_type_parameters_2302": "I membri statici non possono fare riferimento a parametri di tipo classe.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "La proprietà statica '{0}' è in conflitto con la proprietà predefinita 'Function.{0}' della funzione del costruttore '{1}'.", "String_literal_expected_1141": "È previsto un valore letterale stringa.", "String_literal_with_double_quotes_expected_1327": "È previsto un valore letterale stringa con virgolette doppie.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Applica stili a errori e messaggi usando colore e contesto (sperimentale).", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Le dichiarazioni di proprietà successive devono essere dello stesso tipo. La proprietà '{0}' deve essere di tipo '{1}', ma qui è di tipo '{2}'.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Le dichiarazioni di variabili successive devono essere dello stesso tipo. La variabile '{0}' deve essere di tipo '{1}', mentre è di tipo '{2}'.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "Il tipo della sostituzione '{0}' per il criterio '{1}' non è corretto. È previsto 'string', ma è stato ottenuto '{2}'.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "La sostituzione '{0}' nel criterio '{1}' può contenere al massimo un carattere '*'.", "Substitutions_for_pattern_0_should_be_an_array_5063": "Le sostituzioni per il criterio '{0}' devono essere una matrice.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Le sostituzioni per il criterio '{0}' non devono essere una matrice vuota.", "Successfully_created_a_tsconfig_json_file_6071": "La creazione di un file tsconfig.json è riuscita.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "Le chiamate super non sono consentite all'esterno di costruttori o nelle funzioni annidate all'interno di costruttori.", "Suppress_excess_property_checks_for_object_literals_6072": "Elimina i controlli delle proprietà in eccesso per i valori letterali di oggetto.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Non visualizza gli errori noImplicitAny per gli oggetti di indicizzazione in cui mancano le firme dell'indice.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Disabilita gli errori 'noImplicitAny' durante l'indicizzazione di oggetti in cui mancano le firme dell'indice.", "Switch_each_misused_0_to_1_95138": "<PERSON><PERSON><PERSON> ogni '{0}' non usato correttamente in '{1}'", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Chiama in modo sincrono i callback e aggiorna lo stato dei watcher di directory in piattaforme che non supportano il controllo ricorsivo in modo nativo.", "Syntax_Colon_0_6023": "Sintassi: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "Con il tag '{0}' sono previsti almeno '{1}' argomenti, ma la factory JSX '{2}' ne fornisce al massimo '{3}'.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "Le espressioni di modello con tag non sono consentite in una catena facoltativa.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "La destinazione consente solo {0} elemento/i ma l'origine potrebbe contenerne di più.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "La destinazione richiede {0} elemento/i ma l'origine potrebbe contenerne di meno.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "Il modificatore '{0}' può essere usato solo in file TypeScript.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "Non è possibile applicare l'operatore '{0}' al tipo 'symbol'.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "L'operatore '{0}' non è consentito per i tipi booleani. Provare a usare '{1}'.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "La proprietà '{0}' di un iteratore asincrono deve essere un metodo.", "The_0_property_of_an_iterator_must_be_a_method_2767": "La proprietà '{0}' di un iteratore deve essere un metodo.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "Il tipo 'Object' può essere assegnato a un numero molto limitato di altri tipi. Si intendeva usare il tipo 'any'?", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES3_and_ES5_Consider_using_a_stand_2496": "Non è possibile fare riferimento all'oggetto 'arguments' in una funzione arrow in ES3 e ES5. Provare a usare un'espressione di funzione standard.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES3_and_ES5_Consider_usi_2522": "Non è possibile fare riferimento all'oggetto 'arguments' in un metodo o una funzione asincrona in ES3 e ES5. Provare a usare un metodo o una funzione standard.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "Il corpo di un'istruzione 'if' non può essere l'istruzione vuota.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "La chiamata sarebbe riuscita rispetto a questa implementazione, ma le firme di implementazione degli overload non sono visibili esternamente.", "The_character_set_of_the_input_files_6163": "Set di caratteri dei file di input.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "La funzione arrow contenitore acquisisce il valore globale di 'this'.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "Il corpo del modulo o la funzione che contiene è troppo grande per l'analisi del flusso di controllo.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "Il file corrente è un modulo CommonJS e non può usare 'await' al livello principale.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "Il file corrente è un modulo CommonJS le cui importazioni genereranno chiamate 'require'. Tuttavia, il file a cui si fa riferimento è un modulo ECMAScript e non può essere importato con 'require'. Provare a scrivere una chiamata 'import(\"{0}\")' dinamica.", "The_current_host_does_not_support_the_0_option_5001": "L'host corrente non supporta l'opzione '{0}'.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "La dichiarazione di '{0}' che probabilmente si intende usare viene definita in questo punto", "The_declaration_was_marked_as_deprecated_here_2798": "La dichiarazione è stata contrassegnata come deprecata in questo punto.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "Il tipo previsto proviene dalla proprietà '{0}', dichiarata in questo punto nel tipo '{1}'", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "Il tipo previsto proviene dal tipo restituito di questa firma.", "The_expected_type_comes_from_this_index_signature_6501": "Il tipo previsto proviene da questa firma dell'indice.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "L'espressione di un'assegnazione di esportazione deve essere un identificatore o un nome completo in un contesto di ambiente.", "The_file_is_in_the_program_because_Colon_1430": "Motivo per cui il file è presente nel programma:", "The_files_list_in_config_file_0_is_empty_18002": "L'elenco 'files' nel file config '{0}' è vuoto.", "The_first_export_default_is_here_2752": "In questo punto è presente il valore predefinito per la prima esportazione.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "Il primo parametro del metodo 'then' di una promessa deve essere un callback.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "Il tipo globale 'JSX.{0}' non può contenere più di una proprietà.", "The_implementation_signature_is_declared_here_2750": "In questo punto viene dichiarata la firma di implementazione.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "La metaproprietà' Import. meta ' non è consentita per i file che vengono compilati nell'output di CommonJS.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "La metaproprietà 'import.meta' è consentita solo se l'opzione '--module' è impostata su 'es2020', 'es2022', 'esnext', 'system', 'node16' o 'nodenext'.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "Non è possibile assegnare un nome al tipo derivato di '{0}' senza un riferimento a '{1}'. È probabile che non sia portabile. È necessaria un'annotazione di tipo.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "Il tipo dedotto di '{0}' fa riferimento a un tipo con una struttura ciclica che non può essere facilmente serializzata. È necessaria un'annotazione di tipo.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "Il tipo dedotto di '{0}' fa riferimento a un tipo '{1}' non accessibile. È necessaria un'annotazione di tipo.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "Il tipo dedotto di questo nodo supera la lunghezza massima serializzata dal compilatore. È necessaria un'annotazione di tipo esplicita.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "L'intersezione '{0}' è stata ridotta a 'never' perché la proprietà '{1}' esiste in più costituenti ed è privata in alcuni.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "L'intersezione '{0}' è stata ridotta a 'never' perché in alcuni costituenti della proprietà '{1}' sono presenti tipi in conflitto.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "La parola chiave 'intrinsic' può essere usata solo per dichiarare tipi intrinseci forniti dal compilatore.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "Per usare frammenti JSX con l'opzione del compilatore 'jsxFactory', è necessario specificare l'opzione del compilatore 'jsxFragmentFactory'.", "The_last_overload_gave_the_following_error_2770": "L'ultimo overload ha restituito l'errore seguente.", "The_last_overload_is_declared_here_2771": "In questo punto viene dichiarato l'ultimo overload.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "La parte sinistra di un'espressione 'for...in' non può essere un criterio di destrutturazione.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "Nella parte sinistra di un'espressione 'for...in' non è possibile usare un'annotazione di tipo.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "La parte sinistra di un'istruzione 'for...in' non può essere un accesso a proprietà facoltativo.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "La parte sinistra di un'istruzione 'for...in' deve essere una variabile o un accesso a proprietà.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "La parte sinistra di un'espressione 'for...in' deve essere di tipo 'string' o 'any'.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "Nella parte sinistra di un'espressione 'for...of' non è possibile usare un'annotazione di tipo.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "La parte sinistra di un'istruzione 'for...of' non può essere un accesso a proprietà facoltativo.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "La parte sinistra di un'istruzione 'for...of' non può essere 'async'.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "La parte sinistra di un'istruzione 'for...of' deve essere una variabile o un accesso a proprietà.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "La parte sinistra di un'operazione aritmetica deve essere di tipo 'any', 'number', 'bigint' o un tipo enumerazione.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "La parte sinistra di un'espressione di assegnazione non può essere un accesso a proprietà facoltativo.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "La parte sinistra di un'espressione di assegnazione deve essere una variabile o un accesso a proprietà.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "La parte sinistra di un'espressione 'instanceof' deve essere di tipo 'any' oppure essere un tipo di oggetto o un parametro di tipo.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Impostazioni locali usate per la visualizzazione di messaggi all'utente, ad esempio 'it-it'", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "Profondità massima delle dipendenze per la ricerca in node_modules e il caricamento dei file JavaScript.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "L'operando di un operatore 'delete' non può essere un identificatore privato.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "L'operando di un operatore 'delete' non può essere una proprietà di sola lettura.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "L'operando di un operatore 'delete' deve essere un riferimento a proprietà.", "The_operand_of_a_delete_operator_must_be_optional_2790": "L'operando di un operatore 'delete' deve essere facoltativo.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "L'operando di un operatore di incremento o decremento non può essere un accesso a proprietà facoltativo.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "L'operando di un operatore di incremento o decremento deve essere una variabile o un accesso a proprietà.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "In questo punto il parser dovrebbe trovare un simbolo '{1}' abbinato al token '{0}'.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "La radice del progetto è ambigua, ma è necessaria per risolvere i '{0}' delle voci della mappa di esportazione nel file '{1}'. Specificare l'opzione del compilatore 'rootDir' per disambiguare.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "La radice del progetto è ambigua, ma è necessaria per risolvere i '{0}' delle voci della mappa di importazione nel file '{1}'. Specificare l'opzione del compilatore 'rootDir' per disambiguare.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "Non è possibile accedere alla proprietà '{0}' nel tipo '{1}' all'interno di questa classe perché è nascosta da un altro identificatore privato con la stessa ortografia.", "The_return_type_of_a_get_accessor_must_be_assignable_to_its_set_accessor_type_2380": "Il tipo restituito di una funzione di accesso 'get' deve essere assegnabile al relativo tipo di funzione di accesso 'set'", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "Il tipo restituito di una funzione di espressione Decorator del parametro deve essere 'void' o 'any'.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "Il tipo restituito di una funzione di espressione Decorator della proprietà deve essere 'void' o 'any'.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "Il tipo restituito di una funzione asincrona deve essere una promessa valida oppure non deve contenere un membro 'then' chiamabile.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "Il tipo restituito di un metodo o una funzione asincrona deve essere il tipo globale Promise<T>. Si intendeva scrivere 'Promise<{0}>'?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "La parte destra di un'istruzione 'for...in' deve essere di tipo 'any' oppure essere un tipo di oggetto o un parametro di tipo, ma in questo caso il tipo è '{0}'.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "La parte destra di un'operazione aritmetica deve essere di tipo 'any', 'number', 'bigint' o un tipo enumerazione.", "The_right_hand_side_of_an_instanceof_expression_must_be_of_type_any_or_of_a_type_assignable_to_the_F_2359": "La parte destra di un'espressione 'instanceof' deve essere di tipo 'any' o di un tipo assegnabile al tipo di interfaccia 'Function'.", "The_root_value_of_a_0_file_must_be_an_object_5092": "Il valore radice di un file '{0}' deve essere un oggetto.", "The_shadowing_declaration_of_0_is_defined_here_18017": "La dichiarazione di oscuramento di '{0}' viene definita in questo punto", "The_signature_0_of_1_is_deprecated_6387": "La firma '{0}' di '{1}' è deprecata.", "The_specified_path_does_not_exist_Colon_0_5058": "Il percorso specificato non esiste: '{0}'.", "The_tag_was_first_specified_here_8034": "Il tag è stato specificato per la prima volta in questo punto.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "La destinazione di un'assegnazione rest di oggetto non può essere un accesso a proprietà facoltativo.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "La destinazione di un'assegnazione REST di oggetto deve essere una variabile o un accesso a proprietà.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "Il contesto 'this' del tipo '{0}' non è assegnabile a quello 'this' di tipo '{1}' del metodo.", "The_this_types_of_each_signature_are_incompatible_2685": "I tipi 'this' delle singole firme non sono compatibili.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "Il tipo '{0}' è 'readonly' e non può essere assegnato al tipo modificabile '{1}'.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "Impossibile utilizzare il modificatore 'tipo' in un'esportazione denominata quando 'tipo di esportazione' viene usato nell'istruzione di esportazione.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "Impossibile utilizzare il modificatore 'tipo' in un'importazione denominata quando 'tipo di importazione' viene usato nella relativa istruzione di importazione.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "Il tipo di una dichiarazione di funzione deve corrispondere alla firma della funzione.", "The_type_of_this_expression_cannot_be_named_without_a_resolution_mode_assertion_which_is_an_unstable_2841": "Impossibile assegnare un nome al tipo di questa espressione senza un'asserzione 'resolution-mode', che è una funzionalità instabile. Usare TypeScript notturno per disattivare l'errore. Provare ad aggiornare con 'npm install -D typescript@next'.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "Impossibile serializzare questo tipo di nodo perché la sua proprietà '{0}' non può essere serializzata.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "Il tipo restituito dal metodo '{0}()' di un iteratore asincrono deve essere una promessa per un tipo con una proprietà 'value'.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "Il tipo restituito dal metodo '{0}()' di un iteratore deve contenere una proprietà 'value'.", "The_types_of_0_are_incompatible_between_these_types_2200": "I tipi di '{0}' sono incompatibili tra questi tipi.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "I tipi restituiti da '{0}' sono incompatibili tra questi tipi.", "The_value_0_cannot_be_used_here_18050": "Non è possibile usare qui il valore '{0}'.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "La dichiarazione di variabile di un'istruzione 'for...in' non può contenere un inizializzatore.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "La dichiarazione di variabile di un'istruzione 'for...of' non può contenere un inizializzatore.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "L'istruzione 'with' non è supportata. Il tipo di tutti i simboli in un blocco 'with' è 'any'.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "Con la proprietà '{0}' del tag JSX è previsto un singolo elemento figlio di tipo '{1}', ma sono stati specificati più elementi figlio.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "Con la proprietà '{0}' del tag JSX è previsto il tipo '{1}' che richiede più elementi figlio, ma è stato specificato un singolo elemento figlio.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "Questo confronto sembra non intenzionale perché i tipi '{0}' e '{1}' non presentano alcuna sovrapposizione.", "This_condition_will_always_return_0_2845": "Questa condizione restituirà sempre '{0}'.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "Questa condizione restituirà sempre '{0}' perché JavaScript confronta gli oggetti per riferimento, non per valore.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Questa condizione restituirà sempre true perché questo elemento '{0}' è sempre definito.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Questa condizione restituirà sempre true perché questa funzione è sempre definita. Si intendeva chiamarla?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Questa funzione del costruttore può essere convertita in una dichiarazione di classe.", "This_expression_is_not_callable_2349": "Questa espressione non può essere chiamata.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "Non è possibile chiamare questa espressione perché è una funzione di accesso 'get'. Si intendeva usarla senza '()'?", "This_expression_is_not_constructable_2351": "Questa espressione non può essere costruita.", "This_file_already_has_a_default_export_95130": "Per questo file esiste già un'esportazione predefinita", "This_import_is_never_used_as_a_value_and_must_use_import_type_because_importsNotUsedAsValues_is_set__1371": "Questa importazione non viene mai usata come valore e deve usare 'import type' perché 'importsNotUsedAsValues' è impostato su 'error'.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "Questa è la dichiarazione che verrà aumentata. Provare a spostare la dichiarazione che causa l'aumento nello stesso file.", "This_may_be_converted_to_an_async_function_80006": "<PERSON><PERSON>ò essere convertita in una funzione asincrona.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Questo membro non può avere un commento JSDoc con un tag '@override' perché non è dichiarato nella classe di base '{0}'.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "Questo membro non può avere un commento JSDoc con un tag 'override' perché non è dichiarato nella classe di base '{0}'. Intendevi '{1}'?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Questo membro non può avere un commento JSDoc con un tag '@override' perché la classe che lo contiene '{0}' non estende un'altra classe.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Questo membro non può includere un modificatore 'override' perché non è dichiarato nella classe di base '{0}'.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Questo membro non può includere un modificatore 'override' perché non è dichiarato nella classe di base '{0}'. Forse intendevi '{1}'?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Questo membro non può includere un modificatore 'override' perché la classe '{0}', che lo contiene, non estende un'altra classe.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "Questo membro deve avere un commento JSDoc con un tag '@override' perché sostituisce un membro nella classe di base '{0}'.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Questo membro deve includere un modificatore 'override' perché sovrascrive un membro nella classe di base '{0}'.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Questo membro deve includere un modificatore 'override' perché esegue l'override di un metodo astratto dichiarato nella classe di base '{0}'.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "È possibile fare riferimento a questo modulo solo con importazioni/esportazioni ECMAScript attivando il flag '{0}' e facendo riferimento alla relativa esportazione predefinita.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Il modulo viene dichiarato con 'export =' e può essere usato solo con un'importazione predefinita quando si usa il flag '{0}'.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Questa firma di overload non è compatibile con la relativa firma di implementazione.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Questo parametro non è consentito con la direttiva 'use strict'.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "Questa proprietà di parametro deve avere un commento JSDoc con un tag '@override' perché sostituisce un membro nella classe di base '{0}'.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Questa proprietà parametro deve includere un modificatore 'override' perché sovrascrive un membro nella classe di base '{0}'.", "This_spread_always_overwrites_this_property_2785": "Questo spread sovrascrive sempre questa proprietà.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Questa sintassi è riservata ai file con estensione MTS o CTS. Aggiungere una virgola finale o un vincolo esplicito.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Questa sintassi è riservata ai file con estensione mts o cts. Utilizzare un'espressione 'as'.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Con questa sintassi è richiesto un helper importato, ma il modulo '{0}' non è stato trovato.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Con questa sintassi è richiesto un helper importato denominato '{1}', che non esiste in '{0}'. Provare ad aggiornare la versione di '{0}'.", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Con questa sintassi è richiesto un helper importato denominato '{1}' con {2} parametri, che non è compatibile con quello presente in '{0}'. Provare ad aggiornare la versione di '{0}'.", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Questo parametro di tipo potrebbe richiedere un vincolo `extends {0}`.", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "Questo uso di 'import' non è valido. le chiamate 'import()' possono essere scritte, ma devono avere parentesi e non possono avere argomenti di tipo.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Per convertire questo file in un modulo ECMAScript, aggiungere il campo '\"type\": \"module\"' a '{0}'.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Per convertire il file in un modulo ECMAScript, modificarne l'estensione in '{0}' oppure aggiungere il campo '\"type\": \"module\"' a '{1}'.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Per convertire questo file in un modulo ECMAScript, modificarne l'estensione in '{0}' o creare un file package.json locale con '{ \"type\": \"module\" }'.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Per convertire questo file in un modulo ECMAScript, creare un file package.json locale con '{ \"type\": \"module\" }'.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Le espressioni 'await' di primo livello sono consentite solo quando l'opzione 'module' è impostata su 'es2022', 'esnext', 'system', 'node16', or 'nodenext', e l'opzione 'target' è impostata su 'es2017' o versione successiva.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "Le dichiarazioni di primo livello nei file con estensione d.ts devono iniziare con un modificatore 'declare' o 'export'.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "I cicli 'for await' di primo livello sono consentiti solo quando l'opzione 'module' è impostata su'es2022', 'esnext', 'system', 'node16', o 'nodenext' e l'opzione 'target' è impostata su 'es2017' o versione successiva.", "Trailing_comma_not_allowed_1009": "La virgola finale non è consentita.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Esegue il transpile di ogni file in un modulo separato (simile a 'ts.transpileModule').", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "Provare con `npm i --save-dev @types/{1}` se esiste oppure aggiungere un nuovo file di dichiarazione con estensione d.ts contenente `declare module '{0}';`", "Trying_other_entries_in_rootDirs_6110": "<PERSON><PERSON><PERSON><PERSON> effettuato un tentativo con altre voci in 'rootDirs'.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "Verrà effettuato un tentativo con la sostituzione '{0}'. Percorso candidato del modulo: '{1}'.", "Tuple_members_must_all_have_names_or_all_not_have_names_5084": "I membri di tupla devono tutti avere o non avere nomi.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "Il tipo di tupla '{0}' con lunghezza '{1}' non contiene elementi alla posizione di indice '{2}'.", "Tuple_type_arguments_circularly_reference_themselves_4110": "Gli argomenti tipo di tupla contengono un riferimento circolare a se stessi.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "Il tipo '{0}' può essere iterato solo quando si usa il flag '--downlevelIteration' o quando '--target' è impostato su 'es2015' o un valore superiore.", "Type_0_cannot_be_used_as_an_index_type_2538": "Non è possibile usare il tipo '{0}' come tipo di indice.", "Type_0_cannot_be_used_to_index_type_1_2536": "Non è possibile usare il tipo '{0}' per indicizzare il tipo '{1}'.", "Type_0_does_not_satisfy_the_constraint_1_2344": "Il tipo '{0}' non soddisfa il vincolo '{1}'.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "Il tipo '{0}' non soddisfa il tipo previsto '{1}'.", "Type_0_has_no_call_signatures_2757": "Il tipo '{0}' non contiene firme di chiamata.", "Type_0_has_no_construct_signatures_2761": "Il tipo '{0}' non contiene firme del costrutto.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "Nel tipo '{0}' non esiste alcuna firma dell'indice corrispondente per il tipo '{1}'.", "Type_0_has_no_properties_in_common_with_type_1_2559": "Il tipo '{0}' non ha proprietà in comune con il tipo '{1}'.", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "Il tipo '{0}' non ha firme per cui è applicabile l'elenco degli argomenti tipo.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "Nel tipo '{0}' mancano le proprietà seguenti del tipo '{1}': {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "Nel tipo '{0}' mancano le proprietà seguenti del tipo '{1}': {2} e altre {3}.", "Type_0_is_not_a_constructor_function_type_2507": "Il tipo '{0}' non è un tipo di funzione del costruttore.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_SlashES3_because_it_does_not_refer_to_a_Prom_1055": "Il tipo '{0}' non è un tipo restituito di funzione asincrona valido in ES5/ES3 perché non fa riferimento a un valore di costruttore compatibile con Promise.", "Type_0_is_not_an_array_type_2461": "Il tipo '{0}' non è un tipo matrice.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "Il tipo '{0}' non è un tipo matrice o stringa.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "Il tipo '{0}' non è un tipo matrice o stringa oppure non contiene un metodo '[Symbol.iterator]()' che restituisce un iteratore.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "Il tipo '{0}' non è un tipo matrice oppure non contiene un metodo '[Symbol.iterator]()' che restituisce un iteratore.", "Type_0_is_not_assignable_to_type_1_2322": "Il tipo '{0}' non è assegnabile al tipo '{1}'.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "Il tipo '{0}' non è assegnabile al tipo '{1}'. Si intendeva '{2}'?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "Il tipo '{0}' non è assegnabile al tipo '{1}'. Sono presenti due tipi diversi con questo nome, che però non sono correlati.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "Il tipo '{0}' non può essere assegnato al tipo '{1}' come indicato dall'annotazione di varianza.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "L'argomento di tipo '{0}' non può essere assegnato al tipo '{1}' con 'exactOptionalPropertyTypes: true'. Provare ad aggiungere 'undefined' ai tipi di proprietà di destinazione.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "L'argomento di tipo '{0}' non può essere assegnato al tipo '{1}' con 'exactOptionalPropertyTypes: true'. Provare ad aggiungere 'undefined' al tipo di destinazione.", "Type_0_is_not_comparable_to_type_1_2678": "Il tipo '{0}' non è confrontabile con il tipo '{1}'.", "Type_0_is_not_generic_2315": "Il tipo '{0}' non è generico.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "Il tipo '{0}' può rappresentare un valore primitivo, che non è consentito come operando destro dell'operatore 'in'.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "Il tipo '{0}' deve contenere un metodo '[Symbol.asyncIterator]()' che restituisce un iteratore asincrono.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "Il tipo '{0}' deve contenere un metodo '[Symbol.iterator]()' che restituisce un iteratore.", "Type_0_provides_no_match_for_the_signature_1_2658": "Il tipo '{0}' non fornisce corrispondenze per la firma '{1}'.", "Type_0_recursively_references_itself_as_a_base_type_2310": "Il tipo '{0}' fa riferimento a se stesso in modo ricorsivo come tipo di base.", "Type_Checking_6248": "Controllo del tipo", "Type_alias_0_circularly_references_itself_2456": "L'alias di tipo '{0}' contiene un riferimento circolare a se stesso.", "Type_alias_must_be_given_a_name_1439": "È necessario assegnare un nome all'alias del tipo.", "Type_alias_name_cannot_be_0_2457": "Il nome dell'alias di tipo non può essere '{0}'.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "<PERSON><PERSON> alias di tipo possono esere usati solo in file TypeScript.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "L'annotazione di tipo non può essere inclusa in una dichiarazione di costruttore.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Le annotazioni tipo possono essere usate solo in file TypeScript.", "Type_argument_expected_1140": "È previsto l'argomento tipo.", "Type_argument_list_cannot_be_empty_1099": "L'elenco degli argomenti tipo non può essere vuoto.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Gli argomenti tipo possono essere usati solo in file TypeScript.", "Type_arguments_cannot_be_used_here_1342": "Non è possibile usare argomenti tipo in questa posizione.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Gli argomenti tipo per '{0}' contengono un riferimento circolare a se stessi.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "Le espressioni di asserzione di tipo possono essere usate solo in file TypeScript.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "Il tipo alla posizione {0} nell'origine non è compatibile con il tipo alla posizione {1} nella destinazione.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "Il tipo alle posizioni dalla {0} alla {1} nell'origine non è compatibile con il tipo alla posizione {2} nella destinazione.", "Type_declaration_files_to_be_included_in_compilation_6124": "File della dichiarazione di tipo da includere nella compilazione.", "Type_expected_1110": "È previsto il tipo.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "L’importazione dei tipi di asserzione deve contenere esattamente una chiave, 'resolution-mode', con valore 'import' o 'require'.", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "La creazione di un'istanza di tipo presenta troppi livelli ed è probabilmente infinita.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "Il tipo viene usato come riferimento diretto o indiretto nel callback di fulfillment del relativo metodo 'then'.", "Type_library_referenced_via_0_from_file_1_1402": "Libreria dei tipi a cui viene fatto riferimento tramite '{0}' dal file '{1}'", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "Libreria dei tipi a cui viene fatto riferimento tramite '{0}' dal file '{1}' con packageId '{2}'", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "Il tipo dell'operando 'await' deve essere una promessa valida oppure non deve contenere un membro 'then' chiamabile.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "Il tipo del valore della proprietà calcolata è '{0}', che non è assegnabile al tipo '{1}'.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "Il tipo di variabile del membro di istanza '{0}' non può fare riferimento all'identificatore '{1}' dichiarato nel costruttore.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "Il tipo di elementi iterati di un operando 'yield*' deve essere una promessa valida oppure non deve contenere un membro 'then' chiamabile.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "Il tipo di proprietà '{0}' contiene un riferimento circolare a se stesso nel tipo con mapping '{1}'.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "Il tipo dell'operando 'yield' in un generatore asincrono deve essere una promessa valida oppure non deve contenere un membro 'then' chiamabile.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "Il tipo è originato in corrispondenza di questa importazione. Non è possibile chiamare o costruire un'importazione di tipo spazio dei nomi e verrà restituito un errore in fase di esecuzione. Provare a usare un'importazione predefinita o un'importazione di require in questo punto.", "Type_parameter_0_has_a_circular_constraint_2313": "Il parametro di tipo '{0}' contiene un vincolo circolare.", "Type_parameter_0_has_a_circular_default_2716": "Il parametro di tipo '{0}' contiene un'impostazione predefinita circolare.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "Il parametro di tipo '{0}' della firma di chiamata dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "Il parametro di tipo '{0}' della firma del costruttore dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "Il parametro di tipo '{0}' della classe esportata contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "Il parametro di tipo '{0}' della funzione esportata contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "Il parametro di tipo '{0}' dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "Il parametro di tipo '{0}' del tipo di oggetto con mapping esportato usa il nome privato '{1}'.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "Il parametro di tipo '{0}' dell'alias di tipo esportato contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "Il parametro di tipo '{0}' del metodo dell'interfaccia esportata contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "Il parametro di tipo '{0}' del metodo pubblico della classe esportata contiene o usa il nome privato '{1}'.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "Il parametro di tipo '{0}' del metodo statico pubblico della classe esportata contiene o usa il nome privato '{1}'.", "Type_parameter_declaration_expected_1139": "È prevista la dichiarazione di parametro di tipo.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Le dichiarazioni di parametro di tipo possono essere usate solo in file TypeScript.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Le impostazioni predefinite del parametro di tipo possono fare riferimento solo a parametri di tipo dichiarati in precedenza.", "Type_parameter_list_cannot_be_empty_1098": "L'elenco dei parametri di tipo non può essere vuoto.", "Type_parameter_name_cannot_be_0_2368": "Il nome del parametro di tipo non può essere '{0}'.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "I parametri di tipo non possono essere inclusi in una dichiarazione di costruttore.", "Type_predicate_0_is_not_assignable_to_1_1226": "Il predicato di tipo '{0}' non è assegnabile a '{1}'.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "Il tipo produce un tipo di tupla troppo grande da rappresentare.", "Type_reference_directive_0_was_not_resolved_6120": "======== La direttiva '{0}' del riferimento al tipo non è stata risolta. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== La direttiva '{0}' del riferimento al tipo è stata risolta in '{1}'. Primaria: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== La direttiva '{0}' del riferimento al tipo è stata risolta in '{1}' con ID pacchetto ID '{2}'. Primaria: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "Le espressioni di soddisfazione del tipo possono essere usate solo nei file TypeScript.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "I tipi non possono essere visualizzati nelle dichiarazioni di esportazione nei file JavaScript.", "Types_have_separate_declarations_of_a_private_property_0_2442": "I tipi contengono dichiarazioni separate di una proprietà privata '{0}'.", "Types_of_construct_signatures_are_incompatible_2419": "I tipi delle firme del costrutto sono incompatibili.", "Types_of_parameters_0_and_1_are_incompatible_2328": "I tipi dei parametri '{0}' e '{1}' sono incompatibili.", "Types_of_property_0_are_incompatible_2326": "I tipi della proprietà '{0}' sono incompatibili.", "Unable_to_open_file_0_6050": "Non è possibile aprire il file '{0}'.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "Non è possibile risolvere la firma dell'espressione Decorator della classe quando è chiamata come espressione.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "Non è possibile risolvere la firma dell'espressione Decorator del metodo quando è chiamata come espressione.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "Non è possibile risolvere la firma dell'espressione Decorator del parametro quando è chiamata come espressione.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "Non è possibile risolvere la firma dell'espressione Decorator della proprietà quando è chiamata come espressione.", "Unexpected_end_of_text_1126": "Fine del testo imprevista.", "Unexpected_keyword_or_identifier_1434": "Parola chiave o identificatore imprevisti.", "Unexpected_token_1012": "Token imprevisto.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Token imprevisto. È previsto un costruttore, un metodo, una funzione di accesso o una proprietà.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Token imprevisto. Sono previsti nomi di parametro senza parentesi graffe.", "Unexpected_token_Did_you_mean_or_gt_1382": "Token imprevisto. Si intendeva `{'>'}` o `>`?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Token imprevisto. Si intendeva `{'}'}` o `&rbrace;`?", "Unexpected_token_expected_1179": "Token imprevisto. È previsto '{'.", "Unknown_build_option_0_5072": "L'opzione di compilazione '{0}' è sconosciuta.", "Unknown_build_option_0_Did_you_mean_1_5077": "L'opzione di compilazione '{0}' è sconosciuta. Si intendeva '{1}'?", "Unknown_compiler_option_0_5023": "Opzione del compilatore sconosciuta: '{0}'.", "Unknown_compiler_option_0_Did_you_mean_1_5025": "L'opzione '{0}' del compilatore è sconosciuta. Si intendeva '{1}'?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Parola chiave o identificatore sconosciuti. Intendevi '{0}'?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "L'opzione 'excludes' è sconosciuta. Si intendeva 'exclude'?", "Unknown_type_acquisition_option_0_17010": "L'opzione '{0}' relativa all'acquisizione del tipo è sconosciuta.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "L'opzione di acquisizione del tipo '{0}' è sconosciuta. Si intendeva '{1}'?", "Unknown_watch_option_0_5078": "L'opzione '{0}' dell'espressione di controllo è sconosciuta.", "Unknown_watch_option_0_Did_you_mean_1_5079": "L'opzione '{0}' dell'espressione di controllo è sconosciuta. Si intendeva '{1}'?", "Unreachable_code_detected_7027": "È stato rilevato codice non raggiungibile.", "Unterminated_Unicode_escape_sequence_1199": "Sequenza di escape Unicode senza terminazione.", "Unterminated_quoted_string_in_response_file_0_6045": "Stringa tra virgolette senza terminazione nel file di risposta '{0}'.", "Unterminated_regular_expression_literal_1161": "Valore letterale di espressione regolare senza terminazione.", "Unterminated_string_literal_1002": "Valore letterale stringa senza terminazione.", "Unterminated_template_literal_1160": "Valore letterale di modello senza terminazione.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Le chiamate di funzione non tipizzate potrebbero non accettare argomenti tipo.", "Unused_label_7028": "Etichetta non usata.", "Unused_ts_expect_error_directive_2578": "Direttiva '@ts-expect-error' non usata.", "Update_import_from_0_90058": "Aggiornare l'importazione da \"{0}\"", "Updating_output_of_project_0_6373": "Aggiornamento dell'output del progetto '{0}'...", "Updating_output_timestamps_of_project_0_6359": "Aggiornamento dei timestamp di output del progetto '{0}'...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Aggiornamento dei timestamp di output non modificati del progetto '{0}'...", "Use_0_95174": "Usa `{0}`.", "Use_Number_isNaN_in_all_conditions_95175": "Usare 'Number.isNaN' in tutte le condizioni.", "Use_element_access_for_0_95145": "Usare l'accesso agli elementi per '{0}'", "Use_element_access_for_all_undeclared_properties_95146": "Usare l'accesso agli elementi per tutte le proprietà non dichiarate.", "Use_synthetic_default_member_95016": "Usare il membro 'default' sintetico.", "Using_0_subpath_1_with_target_2_6404": "<PERSON><PERSON><PERSON><PERSON> <PERSON> '{0}' sottotracciato '{1}' con destinazione '{2}'.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "L'uso di una stringa in un'istruzione 'for...of' è supportato solo in ECMAScript 5 e versioni successive.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "Se si usa --build, l'opzione -b modificherà il comportamento di tsc in modo che sia più simile a un agente di orchestrazione di compilazione che a un compilatore. Viene usata per attivare la compilazione di progetti compositi. Per altre informazioni, vedere {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "Using compiler options of project reference redirect '{0}'.", "VERSION_6036": "VERSIONE", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "Il valore di tipo '{0}' non ha proprietà in comune con il tipo '{1}'. Si intendeva chiamarlo?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "Il valore di tipo '{0}' non è chiamabile. Si intendeva includere 'new'?", "Variable_0_implicitly_has_an_1_type_7005": "La variabile '{0}' contiene implicitamente un tipo '{1}'.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "La variabile '{0}' include implicitamente un tipo '{1}', ma è possibile dedurre un tipo migliore dall'utilizzo.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "La variabile '{0}' include implicitamente il tipo '{1}' in alcuni punti, ma è possibile dedurre un tipo migliore dall'utilizzo.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "La variabile '{0}' contiene implicitamente il tipo '{1}' in alcune posizioni in cui non è possibile determinarne il tipo.", "Variable_0_is_used_before_being_assigned_2454": "La variabile '{0}' viene usata prima dell'assegnazione.", "Variable_declaration_expected_1134": "È prevista la dichiarazione di variabile.", "Variable_declaration_list_cannot_be_empty_1123": "L'elenco delle dichiarazioni di variabile non può essere vuoto.", "Variable_declaration_not_allowed_at_this_location_1440": "Dichiarazione di variabile non consentita in questa posizione.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "L'elemento variadic alla posizione {0} nell'origine non corrisponde all'elemento alla posizione {1} nella destinazione.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Le annotazioni di varianza sono supportate solo negli alias di tipo per oggetti, funzioni, costruttori e tipi mappati.", "Version_0_6029": "Versione {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Per altre informazioni su questo file, visitare https://aka.ms/tsconfig", "WATCH_OPTIONS_6918": "OPZIONI DELL'ESPRESSIONE DI CONTROLLO", "Watch_and_Build_Modes_6250": "Modalità di espressione di controllo e compilazione", "Watch_input_files_6005": "Controlla i file di input.", "Watch_option_0_requires_a_value_of_type_1_5080": "Con l'opzione '{0}' dell'espressione di controllo è richiesto un valore di tipo {1}.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "È possibile solo scrivere un tipo per '{0}' aggiungendo qui un tipo per l'intero parametro.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "Durante l'assegnazione di funzioni verifica che i parametri e i valori restituiti siano compatibili con il sottotipo.", "When_type_checking_take_into_account_null_and_undefined_6699": "Durante il controllo del tipo prende in considerazione 'null' e 'undefined'.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "Indica se mantenere l'output della console obsoleto in modalità espressione di controllo invece di pulire lo schermo.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Eseguire il wrapping di tutti i caratteri non validi in un contenitore di espressioni", "Wrap_all_object_literal_with_parentheses_95116": "Racchiudere tra parentesi tutti i valori letterali di oggetto", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Esegue il wrapping di JSX senza parentesi nel frammento JSX", "Wrap_in_JSX_fragment_95120": "Esegui il wrapping nel frammento JSX", "Wrap_invalid_character_in_an_expression_container_95108": "Eseguire il wrapping del carattere non valido in un contenitore di espressioni", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Racchiudere tra parentesi il corpo seguente che deve essere un valore letterale di oggetto", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "Per informazioni su tutte le opzioni del compilatore, vedere {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "Non è possibile rinominare un modulo tramite un'importazione globale.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "Non è possibile rinominare gli elementi definiti in una cartella 'node_modules'.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "Non è possibile rinominare gli elementi definiti in un'altra cartella 'node_modules'.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "Non è possibile rinominare elementi definiti nella libreria TypeScript standard.", "You_cannot_rename_this_element_8000": "Non è possibile rinominare questo elemento.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}' accetta un numero troppo ridotto di argomenti da usare come espressione Decorator in questo punto. Si intendeva chiamarlo prima e scrivere '@{0}()'?", "_0_and_1_index_signatures_are_incompatible_2330": "Le firme dell'indice '{0}' e '{1}' non sono compatibili.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "Non è possibile combinare le operazioni '{0}' e '{1}' senza parentesi.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "Gli attributi '{0}' sono stati specificati due volte. L'attributo denominato '{0}' verr<PERSON> sovras<PERSON>ritto.", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "'{0}' può essere importato solo attivando il flag 'esModuleInterop' e usando un'importazione predefinita.", "_0_can_only_be_imported_by_using_a_default_import_2595": "'{0}' può essere importato solo usando un'importazione predefinita.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "'{0}' può essere importato solo usando una chiamata 'require' o attivando il flag 'esModuleInterop' e usando un'importazione predefinita.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "'{0}' può essere importato solo usando una chiamata 'require' o usando un'importazione predefinita.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "'{0}' può essere importato solo usando 'import {1} = require({2})' o un'importazione predefinita.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "'{0}' può essere importato solo usando 'import {1} = require({2})' o attivando il flag 'esModuleInterop' e usando un'importazione predefinita.", "_0_cannot_be_compiled_under_isolatedModules_because_it_is_considered_a_global_script_file_Add_an_imp_1208": "Non è possibile compilare '{0}' in '--isolatedModules' perché viene considerato un file di script globale. Aggiungere un'istruzione import, export o un'istruzione 'export {}' vuota per trasformarlo in un modulo.", "_0_cannot_be_used_as_a_JSX_component_2786": "Non è possibile usare '{0}' come componente JSX.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "Non è possibile usare '{0}' come valore perché è stato esportato con 'export type'.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "Non è possibile usare '{0}' come valore perché è stato importato con 'import type'.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "I componenti di '{0}' non accettano testo come elementi figlio. Il tipo di testo in JSX è 'string ', ma il tipo previsto di '{1}' è '{2}'.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "Non è stato possibile creare un'istanza di '{0}' con un tipo arbitrario che potrebbe non essere correlato a '{1}'.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "Le dichiarazioni '{0}' possono essere usate solo in file TypeScript.", "_0_expected_1005": "È previsto '{0}'.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "In '{0}' non è presente alcun membro esportato denominato '{1}'. Si intendeva '{2}'?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "'{0}' include implicitamente un tipo restituito '{1}', ma è possibile dedurre un tipo migliore dall'utilizzo.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "'{0}' contiene implicitamente il tipo restituito 'any', perché non contiene un'annotazione di tipo restituito e viene usato come riferimento diretto o indiretto in una delle relative espressioni restituite.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "'{0}' contiene implicitamente il tipo 'any', perché non contiene un'annotazione di tipo e viene usato come riferimento diretto o indiretto nel relativo inizializzatore.", "_0_index_signatures_are_incompatible_2634": "Le firme dell'indice '{0}' non sono compatibili.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "Il tipo di indice '{0}' '{1}' non è assegnabile al tipo di indice '{2}' '{3}'.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' è una primitiva, ma '{1}' è un oggetto wrapper. Quando possibile, preferire '{0}'.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}' è un tipo e non può essere importato nei file JavaScript. Usare '{1}' in un'annotazione di tipo JSDoc.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_preserveValueImports_and_isolatedMod_1444": "'{0}' è un tipo e deve essere importato usando un'importazione solo di tipi quando 'preserveValueImports' e 'isolatedModules' sono entrambi abilitati.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "'{0}' è una ridenominazione inutilizzata di '{1}'. Si intendeva utilizzarla come annotazione di tipo?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "'{0}' è assegnabile al vincolo di tipo '{1}', ma è possibile creare un'istanza di '{1}' con un sottotipo diverso del vincolo '{2}'.", "_0_is_automatically_exported_here_18044": "'{0}' viene esportato automaticamente qui.", "_0_is_declared_but_its_value_is_never_read_6133": "L'elemento '{0}' è dichiarato, ma il suo valore non viene mai letto.", "_0_is_declared_but_never_used_6196": "La variabile '{0}' è dichiarata, ma non viene mai usata.", "_0_is_declared_here_2728": "In questo punto viene dichiarato '{0}'.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "'{0}' è definito come proprietà nella classe '{1}', ma in questo punto ne viene eseguito l'override in '{2}' come funzione di accesso.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "'{0}' è definito come funzione di accesso nella classe '{1}', ma in questo punto ne viene eseguito l'override in '{2}' come proprietà di istanza.", "_0_is_deprecated_6385": "'{0}' è deprecato.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}' non è una metaproprietà valida per la parola chiave '{1}'. Si intendeva '{2}'?", "_0_is_not_allowed_as_a_parameter_name_1390": "'{0}' non è un nome di parametro consentito.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "'{0}' non è consentito come nome di una dichiarazione di variabile.", "_0_is_of_type_unknown_18046": "'{0}' è di tipo 'unknown'.", "_0_is_possibly_null_18047": "'{0}' è probabilmente 'null'.", "_0_is_possibly_null_or_undefined_18049": "'{0}' è probabilmente 'null' o 'undefined'.", "_0_is_possibly_undefined_18048": "'{0}' è probabilmente 'undefined'.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}' viene usato come riferimento diretto o indiretto nella relativa espressione di base.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}' viene usato come riferimento diretto o indiretto nella relativa annotazione di tipo.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "'{0}' è specificato più di una volta, quindi il relativo utilizzo verrà sovrascritto.", "_0_list_cannot_be_empty_1097": "L'elenco '{0}' non può essere vuoto.", "_0_modifier_already_seen_1030": "Il modificatore '{0}' è già presente.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "Il modificatore '{0}' può essere presente solo in un parametro di tipo di una classe, un'interfaccia o un alias di tipo", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "Il modificatore '{0}' non può essere incluso in una dichiarazione di costruttore.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "Il modificatore '{0}' non può essere incluso in un elemento modulo o spazio dei nomi.", "_0_modifier_cannot_appear_on_a_parameter_1090": "Il modificatore '{0}' non può essere incluso in un parametro.", "_0_modifier_cannot_appear_on_a_type_member_1070": "Il modificatore '{0}' non può essere incluso in un membro di tipo.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "Il modificatore '{0}' non può essere incluso in un parametro di tipo.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "Il modificatore '{0}' non può essere incluso in una firma dell'indice.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "Il modificatore '{0}' non può essere incluso in elementi di classe di questo tipo.", "_0_modifier_cannot_be_used_here_1042": "Non è possibile usare il modificatore '{0}' in questo punto.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "Non è possibile usare il modificatore '{0}' in un contesto di ambiente.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "Non è possibile usare il modificatore '{0}' con il modificatore '{1}'.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "Non è possibile usare il modificatore '{0}' con un identificatore privato.", "_0_modifier_must_precede_1_modifier_1029": "Il modificatore '{0}' deve precedere il modificatore '{1}'.", "_0_needs_an_explicit_type_annotation_2782": "'{0}' richiede un'annotazione di tipo esplicita.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' fa riferimento solo a un tipo, ma qui viene usato come spazio dei nomi.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' fa riferimento solo a un tipo, ma qui viene usato come valore.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "'{0}' fa riferimento solo a un tipo, ma qui viene usato come valore. Si intendeva usare '{1} in {0}'?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "'{0}' si riferisce solo a un tipo, ma in questo punto viene usato come valore. È necessario modificare la libreria di destinazione? Provare a impostare l'opzione 'lib' del compilatore su es2015 o versioni successive.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' fa riferimento a un istruzione globale UMD, ma il file corrente è un modulo. Provare ad aggiungere un'importazione.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "'{0}' fa riferimento a un valore, ma qui viene usato come tipo. Si intendeva 'typeof {0}'?", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_preserveVa_1446": "'{0}' si risolve in una dichiarazione solo di tipi e deve essere importato usando un'importazione solo di tipi quando 'preserveValueImports' e 'isolatedModules' sono entrambi abilitati.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_isol_1448": "'{0}' si risolve in una dichiarazione solo di tipi e deve essere riesportato usando una riesportazione solo di tipi quando 'isolatedModules' è abilitato.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "' {0}' deve essere impostato all'interno dell'oggetto 'compilerOptions' del file JSON di configurazione", "_0_tag_already_specified_1223": "Il tag '{0}' è già specificato.", "_0_was_also_declared_here_6203": "In questo punto viene dichiarato anche '{0}'.", "_0_was_exported_here_1377": "In questo punto è stato esportato '{0}'.", "_0_was_imported_here_1376": "In questo punto è stato importato '{0}'.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "'{0}', in cui manca l'annotazione di tipo restituito, contiene implicitamente un tipo restituito '{1}'.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "'{0}', in cui manca l'annotazione di tipo restituito, contiene implicitamente un tipo yield '{1}'.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "Il modificatore 'abstract' può essere incluso solo in una dichiarazione di classe, metodo o proprietà.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "Il modificatore 'accessor' può essere visualizzato solo in una dichiarazione di proprietà.", "and_here_6204": "e in questo punto.", "arguments_cannot_be_referenced_in_property_initializers_2815": "impossibile fare riferimento agli 'argomenti' negli inizializzatori di proprietà.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": considera i file con importazioni, esportazioni, import.meta, jsx (con jsx: react-jsx) o il formato esm (con modulo: node16+) come moduli.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "Le espressioni 'await' sono consentite solo al primo livello di un file quando il file è un modulo, ma questo file non contiene importazioni o esportazioni. Provare ad aggiungere un elemento 'export {}' vuoto per trasformare il file in un modulo.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "Le espressioni 'await' sono consentite solo all'interno di funzioni asincrone e al primo livello di moduli.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "Non è possibile usare le espressioni 'await' in un inizializzatore di parametri.", "await_has_no_effect_on_the_type_of_this_expression_80007": "'await' non ha alcun effetto sul tipo di questa espressione.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "L'opzione 'baseUrl' è impostata su '{0}'. Verrà usato questo valore per risolvere il nome del modulo non relativo '{1}'.", "can_only_be_used_at_the_start_of_a_file_18026": "'#!' può essere usato solo all'inizio di un file.", "case_or_default_expected_1130": "È previsto 'case' o 'default'.", "catch_or_finally_expected_1472": "È previsto 'catch' o 'finally'.", "const_declarations_can_only_be_declared_inside_a_block_1156": "Le dichiarazioni 'const' possono essere dichiarate solo all'interno di un blocco.", "const_declarations_must_be_initialized_1155": "Le dichiarazioni 'const' devono essere inizializzate.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "L'inizializzatore del membro di enumerazione 'const' è stato valutato come valore non finito.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "L'inizializzatore del membro di enumerazione 'const' è stato valutato come valore non consentito 'NaN'.", "const_enum_member_initializers_can_only_contain_literal_values_and_other_computed_enum_values_2474": "gli inizializzatori di membri di enumerazione const possono contenere solo valori letterali e altri valori di enumerazione calcolati.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "Le enumerazioni 'const' possono essere usate solo in espressioni di accesso a proprietà o indice oppure nella parte destra di un'assegnazione di esportazione, di una dichiarazione di importazione o di una query su tipo.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "Non è possibile usare 'constructor' come nome di proprietà di un parametro.", "constructor_is_a_reserved_word_18012": "'#constructor' è una parola riservata.", "default_Colon_6903": "impostazione predefinita:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "Non è possibile chiamare 'delete' su un identificatore in modalità strict.", "export_Asterisk_does_not_re_export_a_default_1195": "'export *' non consente di riesportare esportazioni predefinite.", "export_can_only_be_used_in_TypeScript_files_8003": "'export =' può essere usato solo in file TypeScript.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "Non è possibile applicare il modificatore 'export' a moduli di ambiente e aumenti di modulo perché sono sempre visibili.", "extends_clause_already_seen_1172": "La clausola 'extends' è già presente.", "extends_clause_must_precede_implements_clause_1173": "La clausola 'extends' deve precedere la clausola 'implements'.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "La clausola 'extends' della classe esportata '{0}' contiene o usa il nome privato '{1}'.", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "La clausola 'extends' della classe esportata contiene o usa il nome privato '{0}'.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "La clausola 'extends' dell'interfaccia esportata '{0}' contiene o usa il nome privato '{1}'.", "false_unless_composite_is_set_6906": "`false`, a meno che non sia impostato `composite`", "false_unless_strict_is_set_6905": "`false`, a meno che non sia impostato `strict`", "file_6025": "file", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "I cicli 'for await' sono consentiti solo al primo livello di un file quando il file è un modulo, ma questo file non contiene importazioni o esportazioni. Provare ad aggiungere un elemento 'export {}' vuoto per trasformare il file in un modulo.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "I cicli 'for await' sono consentiti solo all'interno di funzioni asincrone e al primo livello di moduli.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "Le funzioni di accesso 'get' e 'set' non possono dichiarare parametri 'this'.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "`[]` se è specificato `files`; in caso contrario, `[\"**/*\"]5D;`", "implements_clause_already_seen_1175": "La clausola 'implements' è già presente.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "Le clausole 'implements' possono essere usate solo in file TypeScript.", "import_can_only_be_used_in_TypeScript_files_8002": "'import ... =' può essere usato solo in file TypeScript.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "Le dichiarazioni 'infer' sono consentite solo nella clausola 'extends' di un tipo condizionale.", "let_declarations_can_only_be_declared_inside_a_block_1157": "Le dichiarazioni 'let' possono essere dichiarate solo all'interno di un blocco.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "Non è consentito usare 'let' come nome in dichiarazioni 'let' o 'const'.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "module === `AMD` o `UMD` o `System` o `ES6`, quindi `Classic`; in caso contrario `Node`", "module_system_or_esModuleInterop_6904": "module === \"system\" o esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "L'espressione 'new', nella cui destinazione manca una firma del costrutto, contiene implicitamente un tipo 'any'.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "`[\"node_modules\", \"bower_components\", \"jspm_packages\"]`, nonché il valore di `outDir` se ne è specificato uno.", "one_of_Colon_6900": "uno di:", "one_or_more_Colon_6901": "uno o più:", "options_6024": "opzioni", "or_JSX_element_expected_1145": "Previsto elemento '{' o JSX.", "or_expected_1144": "È previsto '{' o ';'.", "package_json_does_not_have_a_0_field_6100": "Il file 'package.json' non contiene un campo '{0}'.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "'package.json' non contiene alcuna voce di 'typesVersions' corrispondente alla versione '{0}'.", "package_json_had_a_falsy_0_field_6220": "'package.json' contiene un campo '{0}' falso.", "package_json_has_0_field_1_that_references_2_6101": "Il file 'package.json' contiene il campo '{1}' di '{0}' che fa riferimento a '{2}'.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "'package.json' contiene una voce '{0}' di 'typesVersions' che non corrisponde a un intervallo semver valido.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "'package.json' contiene una voce '{0}' di 'typesVersions' che corrisponde alla versione '{1}' del compilatore. Verrà cercato un criterio per la corrispondenza con il nome di modulo '{2}'.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "'package.json' contiene un campo 'typesVersions' con mapping tra percorsi specifici della versione.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "L’ambito package.json '{0}' esegue esplicitamente il mapping dell'identificatore ' {1}' su null.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "L'ambito package.json '{0}' contiene un tipo non valido per la destinazione dell'identificatore '{1}'", "package_json_scope_0_has_no_imports_defined_6273": "L'ambito package.json '{0}' non ha importazioni definite.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "È specificata l'opzione 'paths'. Verrà cercato un criterio per la corrispondenza con il nome del modulo '{0}'.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "Il modificatore 'readonly' può essere incluso solo in una dichiarazione di proprietà o una firma dell'indice.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "Il modificatore di tipo 'readonly' è consentito solo in tipi di valore letterale matrice e tupla.", "require_call_may_be_converted_to_an_import_80005": "La chiamata a 'require' può essere convertita in un'importazione.", "resolution_mode_assertions_are_only_supported_when_moduleResolution_is_node16_or_nodenext_1452": "Le asserzioni 'resolution-mode' sono supportate solo quando 'moduleResolution' è 'node16' o 'nodenext'.", "resolution_mode_assertions_are_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_wi_4125": "Le asserzioni 'resolution-mode' sono instabili. Usare TypeScript notturno per disattivare questo errore. Provare ad eseguire l'aggiornamento con 'npm install -D typescript@next'.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "'resolution-mode' può essere impostata solo per le importazioni di tipo.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "'resolution-mode' è l'unica chiave valida per l’importazione dei tipi di asserzioni.", "resolution_mode_should_be_either_require_or_import_1453": "'resolution-mode' deve essere 'require' o 'import'.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "L'opzione 'rootDirs' è impostata e verrà usata per risolvere il nome del modulo relativo '{0}'.", "super_can_only_be_referenced_in_a_derived_class_2335": "È possibile fare riferimento a 'super' solo in una classe derivata.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "È possibile fare riferimento a 'super' solo in membri di classi derivate o espressioni letterali di oggetto.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "Non è possibile fare riferimento a 'super' in un nome di proprietà calcolato.", "super_cannot_be_referenced_in_constructor_arguments_2336": "Non è possibile fare riferimento a 'super' in argomenti del costruttore.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "'super' è consentito solo in membri di espressioni letterali di oggetto quando il valore dell'opzione 'target' è 'ES2015' o superiore.", "super_may_not_use_type_arguments_2754": "'super' non può usare argomenti di tipo.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "È necessario chiamare 'super' prima di accedere a una proprietà di 'super' nel costruttore di una classe derivata.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "È necessario chiamare 'super' prima di accedere a 'this' nel costruttore di una classe derivata.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' deve essere seguito da un elenco di argomento o da un accesso membro.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "L'accesso alla proprietà 'super' è consentito solo in un costruttore, in una funzione membro o in una funzione di accesso di membro di una classe derivata.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "Non è possibile fare riferimento a 'this' in un nome di proprietà calcolato.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "Non è possibile fare riferimento a 'this' nel corpo di un modulo o di uno spazio dei nomi.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "Non è possibile fare riferimento a 'this' in un inizializzatore di proprietà statica.", "this_cannot_be_referenced_in_constructor_arguments_2333": "Non è possibile fare riferimento a 'this' in argomenti del costruttore.", "this_cannot_be_referenced_in_current_location_2332": "Non è possibile fare riferimento a 'this' nella posizione corrente.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this' contiene implicitamente il tipo 'any' perché non include un'annotazione di tipo.", "true_for_ES2022_and_above_including_ESNext_6930": "'true' per ES2022 e versioni successive, incluso ESNext.", "true_if_composite_false_otherwise_6909": "`true` se è `composite`; in caso contrario, `false`", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: il compilatore TypeScript", "type_Colon_6902": "tipo:", "unique_symbol_types_are_not_allowed_here_1335": "I tipi 'unique symbol' non sono consentiti in questo punto.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "I tipi 'unique symbol' sono consentiti solo nelle variabili in un'istruzione di variabile.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "Non è possibile usare i tipi 'unique symbol' in una dichiarazione di variabile con nome di binding.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "Non è possibile usare la direttiva 'use strict' con un elenco di parametri non semplice.", "use_strict_directive_used_here_1349": "In questo punto è stata usata la direttiva 'use strict'.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "Le istruzioni 'with' non sono consentite in un blocco di funzione asincrona.", "with_statements_are_not_allowed_in_strict_mode_1101": "Le istruzioni 'with' non sono consentite in modalità strict.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "Con l'espressione 'yield' viene restituito implicitamente un tipo 'any' perché per il generatore che lo contiene non è presente un'annotazione di tipo restituito.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "Non è possibile usare le espressioni 'yield' in un inizializzatore di parametri."}