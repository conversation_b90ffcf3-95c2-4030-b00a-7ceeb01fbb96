import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { KeycloakService } from 'keycloak-angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <!-- Header -->
      <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container-fluid">
          <a class="navbar-brand" href="#">
            <i class="fas fa-hospital-alt me-2"></i>
            Hospital Management
          </a>
          
          <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                <i class="fas fa-user-circle me-1"></i>
                {{ userProfile?.firstName || 'User' }}
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" (click)="goToProfile()">
                  <i class="fas fa-user me-2"></i>Profile
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" (click)="logout()">
                  <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a></li>
              </ul>
            </div>
          </div>
        </div>
      </nav>

      <!-- Welcome Section -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card bg-gradient-primary text-white">
            <div class="card-body">
              <h2 class="card-title">
                Welcome back, {{ userProfile?.firstName || 'User' }}!
              </h2>
              <p class="card-text">
                Role: <span class="badge bg-light text-dark">{{ getUserRole() }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="row mb-4">
        <div class="col-12">
          <h4 class="mb-3">Quick Actions</h4>
        </div>
        
        <!-- Admin Actions -->
        <div class="col-md-3 mb-3" *ngIf="hasRole('ADMIN')">
          <div class="card h-100 border-primary">
            <div class="card-body text-center">
              <i class="fas fa-users fa-3x text-primary mb-3"></i>
              <h5 class="card-title">Manage Staff</h5>
              <p class="card-text">Add and manage doctors and nurses</p>
              <button class="btn btn-primary" (click)="navigateTo('/doctors')">
                Manage Doctors
              </button>
            </div>
          </div>
        </div>

        <!-- Doctor Actions -->
        <div class="col-md-3 mb-3" *ngIf="hasRole('DOCTOR')">
          <div class="card h-100 border-success">
            <div class="card-body text-center">
              <i class="fas fa-user-injured fa-3x text-success mb-3"></i>
              <h5 class="card-title">My Patients</h5>
              <p class="card-text">View and manage your patients</p>
              <button class="btn btn-success" (click)="navigateTo('/patients')">
                View Patients
              </button>
            </div>
          </div>
        </div>

        <!-- Nurse Actions -->
        <div class="col-md-3 mb-3" *ngIf="hasRole('NURSE')">
          <div class="card h-100 border-info">
            <div class="card-body text-center">
              <i class="fas fa-notes-medical fa-3x text-info mb-3"></i>
              <h5 class="card-title">Patient Care</h5>
              <p class="card-text">Assist with patient care tasks</p>
              <button class="btn btn-info" (click)="navigateTo('/patients')">
                Patient Care
              </button>
            </div>
          </div>
        </div>

        <!-- Common Actions -->
        <div class="col-md-3 mb-3">
          <div class="card h-100 border-warning">
            <div class="card-body text-center">
              <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
              <h5 class="card-title">Appointments</h5>
              <p class="card-text">View scheduled appointments</p>
              <button class="btn btn-warning" disabled>
                Coming Soon
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Statistics -->
      <div class="row">
        <div class="col-12">
          <h4 class="mb-3">System Overview</h4>
        </div>
        
        <div class="col-md-3 mb-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Total Patients</h6>
                  <h3>1,234</h3>
                </div>
                <i class="fas fa-user-injured fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-3 mb-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Active Doctors</h6>
                  <h3>45</h3>
                </div>
                <i class="fas fa-user-md fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-3 mb-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Active Nurses</h6>
                  <h3>89</h3>
                </div>
                <i class="fas fa-user-nurse fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-3 mb-3">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Today's Appointments</h6>
                  <h3>67</h3>
                </div>
                <i class="fas fa-calendar-day fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .bg-gradient-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .card {
      border-radius: 10px;
      transition: transform 0.2s;
    }
    
    .card:hover {
      transform: translateY(-2px);
    }
    
    .opacity-75 {
      opacity: 0.75;
    }
  `]
})
export class DashboardComponent implements OnInit {
  userProfile: any = {};
  userRoles: string[] = [];

  constructor(
    private keycloakService: KeycloakService,
    private router: Router
  ) {}

  async ngOnInit() {
    try {
      this.userProfile = await this.keycloakService.loadUserProfile();
      this.userRoles = this.keycloakService.getUserRoles();
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }

  getUserRole(): string {
    if (this.hasRole('ADMIN')) return 'Administrator';
    if (this.hasRole('DOCTOR')) return 'Doctor';
    if (this.hasRole('NURSE')) return 'Nurse';
    if (this.hasRole('PATIENT')) return 'Patient';
    return 'User';
  }

  hasRole(role: string): boolean {
    return this.userRoles.includes(role);
  }

  navigateTo(path: string) {
    this.router.navigate([path]);
  }

  goToProfile() {
    this.router.navigate(['/profile']);
  }

  async logout() {
    await this.keycloakService.logout(window.location.origin);
  }
}
