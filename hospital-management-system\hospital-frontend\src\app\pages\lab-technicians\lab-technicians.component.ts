import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-lab-technicians',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-flask me-2"></i>Lab Technician Management</h2>
        <button class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add New Lab Technician
        </button>
      </div>
      
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>License #</th>
                  <th>Specialization</th>
                  <th>Lab Section</th>
                  <th>Shift</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>LT001</td>
                  <td><PERSON></td>
                  <td>LT123456</td>
                  <td>Hematology</td>
                  <td>Clinical Lab</td>
                  <td>Day</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <tr>
                  <td>LT002</td>
                  <td>Robert Miller</td>
                  <td>LT789012</td>
                  <td>Microbiology</td>
                  <td>Pathology</td>
                  <td>Night</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <!-- More lab technician rows would be loaded dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class LabTechniciansComponent {}
