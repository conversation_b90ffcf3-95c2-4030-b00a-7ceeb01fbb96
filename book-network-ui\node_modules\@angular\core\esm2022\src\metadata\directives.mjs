/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ChangeDetectionStrategy } from '../change_detection/constants';
import { compileComponent, compileDirective } from '../render3/jit/directive';
import { compilePipe } from '../render3/jit/pipe';
import { makeDecorator, makePropDecorator } from '../util/decorators';
/**
 * Type of the Directive metadata.
 *
 * @publicApi
 */
export const Directive = makeDecorator('Directive', (dir = {}) => dir, undefined, undefined, (type, meta) => compileDirective(type, meta));
/**
 * Component decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const Component = makeDecorator('Component', (c = {}) => ({ changeDetection: ChangeDetectionStrategy.Default, ...c }), Directive, undefined, (type, meta) => compileComponent(type, meta));
/**
 * @Annotation
 * @publicApi
 */
export const Pipe = makeDecorator('Pipe', (p) => ({ pure: true, ...p }), undefined, undefined, (type, meta) => compilePipe(type, meta));
/**
 * @Annotation
 * @publicApi
 */
export const Input = makePropDecorator('Input', (arg) => {
    if (!arg) {
        return {};
    }
    return typeof arg === 'string' ? { alias: arg } : arg;
});
/**
 * @Annotation
 * @publicApi
 */
export const Output = makePropDecorator('Output', (alias) => ({ alias }));
/**
 * @Annotation
 * @publicApi
 */
export const HostBinding = makePropDecorator('HostBinding', (hostPropertyName) => ({ hostPropertyName }));
/**
 * Decorator that binds a DOM event to a host listener and supplies configuration metadata.
 * Angular invokes the supplied handler method when the host element emits the specified event,
 * and updates the bound element with the result.
 *
 * If the handler method returns false, applies `preventDefault` on the bound element.
 *
 * @usageNotes
 *
 * The following example declares a directive
 * that attaches a click listener to a button and counts clicks.
 *
 * ```ts
 * @Directive({selector: 'button[counting]'})
 * class CountClicks {
 *   numberOfClicks = 0;
 *
 *   @HostListener('click', ['$event.target'])
 *   onClick(btn) {
 *     console.log('button', btn, 'number of clicks:', this.numberOfClicks++);
 *   }
 * }
 *
 * @Component({
 *   selector: 'app',
 *   template: '<button counting>Increment</button>',
 * })
 * class App {}
 *
 * ```
 *
 * The following example registers another DOM event handler that listens for `Enter` key-press
 * events on the global `window`.
 * ``` ts
 * import { HostListener, Component } from "@angular/core";
 *
 * @Component({
 *   selector: 'app',
 *   template: `<h1>Hello, you have pressed enter {{counter}} number of times!</h1> Press enter key
 * to increment the counter.
 *   <button (click)="resetCounter()">Reset Counter</button>`
 * })
 * class AppComponent {
 *   counter = 0;
 *   @HostListener('window:keydown.enter', ['$event'])
 *   handleKeyDown(event: KeyboardEvent) {
 *     this.counter++;
 *   }
 *   resetCounter() {
 *     this.counter = 0;
 *   }
 * }
 * ```
 * The list of valid key names for `keydown` and `keyup` events
 * can be found here:
 * https://www.w3.org/TR/DOM-Level-3-Events-key/#named-key-attribute-values
 *
 * Note that keys can also be combined, e.g. `@HostListener('keydown.shift.a')`.
 *
 * The global target names that can be used to prefix an event name are
 * `document:`, `window:` and `body:`.
 *
 * @Annotation
 * @publicApi
 */
export const HostListener = makePropDecorator('HostListener', (eventName, args) => ({ eventName, args }));
//# sourceMappingURL=data:application/json;base64,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