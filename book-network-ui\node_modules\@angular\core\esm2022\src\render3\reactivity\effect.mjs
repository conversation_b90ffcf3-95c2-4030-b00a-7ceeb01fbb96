/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertInInjectionContext } from '../../di/contextual';
import { Injector } from '../../di/injector';
import { inject } from '../../di/injector_compatibility';
import { ɵɵdefineInjectable } from '../../di/interface/defs';
import { DestroyRef } from '../../linker/destroy_ref';
import { watch } from '../../signals';
/**
 * Tracks all effects registered within a given application and runs them via `flush`.
 */
export class EffectManager {
    constructor() {
        this.all = new Set();
        this.queue = new Map();
    }
    create(effectFn, destroyRef, allowSignalWrites) {
        const zone = (typeof Zone === 'undefined') ? null : Zone.current;
        const w = watch(effectFn, (watch) => {
            if (!this.all.has(watch)) {
                return;
            }
            this.queue.set(watch, zone);
        }, allowSignalWrites);
        this.all.add(w);
        // Effects start dirty.
        w.notify();
        let unregisterOnDestroy;
        const destroy = () => {
            w.cleanup();
            unregisterOnDestroy?.();
            this.all.delete(w);
            this.queue.delete(w);
        };
        unregisterOnDestroy = destroyRef?.onDestroy(destroy);
        return {
            destroy,
        };
    }
    flush() {
        if (this.queue.size === 0) {
            return;
        }
        for (const [watch, zone] of this.queue) {
            this.queue.delete(watch);
            if (zone) {
                zone.run(() => watch.run());
            }
            else {
                watch.run();
            }
        }
    }
    get isQueueEmpty() {
        return this.queue.size === 0;
    }
    /** @nocollapse */
    static { this.ɵprov = ɵɵdefineInjectable({
        token: EffectManager,
        providedIn: 'root',
        factory: () => new EffectManager(),
    }); }
}
/**
 * Create a global `Effect` for the given reactive function.
 *
 * @developerPreview
 */
export function effect(effectFn, options) {
    !options?.injector && assertInInjectionContext(effect);
    const injector = options?.injector ?? inject(Injector);
    const effectManager = injector.get(EffectManager);
    const destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;
    return effectManager.create(effectFn, destroyRef, !!options?.allowSignalWrites);
}
//# sourceMappingURL=data:application/json;base64,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