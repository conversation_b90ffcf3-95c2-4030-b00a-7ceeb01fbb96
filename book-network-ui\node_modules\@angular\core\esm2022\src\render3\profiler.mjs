/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
let profilerCallback = null;
/**
 * Sets the callback function which will be invoked before and after performing certain actions at
 * runtime (for example, before and after running change detection).
 *
 * Warning: this function is *INTERNAL* and should not be relied upon in application's code.
 * The contract of the function might be changed in any release and/or the function can be removed
 * completely.
 *
 * @param profiler function provided by the caller or null value to disable profiling.
 */
export const setProfiler = (profiler) => {
    profilerCallback = profiler;
};
/**
 * Profiler function which wraps user code executed by the runtime.
 *
 * @param event ProfilerEvent corresponding to the execution context
 * @param instance component instance
 * @param hookOrListener lifecycle hook function or output listener. The value depends on the
 *  execution context
 * @returns
 */
export const profiler = function (event, instance, hookOrListener) {
    if (profilerCallback != null /* both `null` and `undefined` */) {
        profilerCallback(event, instance, hookOrListener);
    }
};
//# sourceMappingURL=data:application/json;base64,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