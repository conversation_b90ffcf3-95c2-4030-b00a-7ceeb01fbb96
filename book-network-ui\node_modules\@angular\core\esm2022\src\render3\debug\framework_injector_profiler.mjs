/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { EnvironmentInjector } from '../../di/r3_injector';
import { throwError } from '../../util/assert';
import { getComponentDef } from '../definition';
import { getNodeInjectorLView, NodeInjector } from '../di';
import { setInjectorProfiler } from './injector_profiler';
/**
 * These are the data structures that our framework injector profiler will fill with data in order
 * to support DI debugging APIs.
 *
 * resolverToTokenToDependencies: Maps an injector to a Map of tokens to an Array of
 * dependencies. Injector -> Token -> Dependencies This is used to support the
 * getDependenciesFromInjectable API, which takes in an injector and a token and returns it's
 * dependencies.
 *
 * resolverToProviders: Maps a DI resolver (an Injector or an LView) to the providers configured
 * within it This is used to support the getInjectorProviders API, which takes in an injector and
 * returns the providers that it was configured with.
 *
 * standaloneInjectorToComponent: Maps the injector of a standalone component to the standalone
 * component that it is associated with. Used in the getInjectorProviders API, specificially in the
 * discovery of import paths for each provider. This is necessary because the imports array of a
 * standalone component is processed and configured in its standalone injector, but exists within
 * the component's definition. Because getInjectorProviders takes in an injector, if that injector
 * is the injector of a standalone component, we need to be able to discover the place where the
 * imports array is located (the component) in order to flatten the imports array within it to
 * discover all of it's providers.
 *
 *
 * All of these data structures are instantiated with WeakMaps. This will ensure that the presence
 * of any object in the keys of these maps does not prevent the garbage collector from collecting
 * those objects. Because of this property of WeakMaps, these data structures will never be the
 * source of a memory leak.
 *
 * An example of this advantage: When components are destroyed, we don't need to do
 * any additional work to remove that component from our mappings.
 *
 */
class DIDebugData {
    constructor() {
        this.resolverToTokenToDependencies = new WeakMap();
        this.resolverToProviders = new WeakMap();
        this.standaloneInjectorToComponent = new WeakMap();
    }
    reset() {
        this.resolverToTokenToDependencies =
            new WeakMap();
        this.resolverToProviders = new WeakMap();
        this.standaloneInjectorToComponent = new WeakMap();
    }
}
let frameworkDIDebugData = new DIDebugData();
export function getFrameworkDIDebugData() {
    return frameworkDIDebugData;
}
/**
 * Initalize default handling of injector events. This handling parses events
 * as they are emitted and constructs the data structures necessary to support
 * some of debug APIs.
 *
 * See handleInjectEvent, handleCreateEvent and handleProviderConfiguredEvent
 * for descriptions of each handler
 *
 * Supported APIs:
 *               - getDependenciesFromInjectable
 *               - getInjectorProviders
 */
export function setupFrameworkInjectorProfiler() {
    frameworkDIDebugData.reset();
    setInjectorProfiler((injectorProfilerEvent) => handleInjectorProfilerEvent(injectorProfilerEvent));
}
function handleInjectorProfilerEvent(injectorProfilerEvent) {
    const { context, type } = injectorProfilerEvent;
    if (type === 0 /* InjectorProfilerEventType.Inject */) {
        handleInjectEvent(context, injectorProfilerEvent.service);
    }
    else if (type === 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */) {
        handleInstanceCreatedByInjectorEvent(context, injectorProfilerEvent.instance);
    }
    else if (type === 2 /* InjectorProfilerEventType.ProviderConfigured */) {
        handleProviderConfiguredEvent(context, injectorProfilerEvent.providerRecord);
    }
}
/**
 *
 * Stores the injected service in frameworkDIDebugData.resolverToTokenToDependencies
 * based on it's injector and token.
 *
 * @param context InjectorProfilerContext the injection context that this event occurred in.
 * @param data InjectedService the service associated with this inject event.
 *
 */
function handleInjectEvent(context, data) {
    const diResolver = getDIResolver(context.injector);
    if (diResolver === null) {
        throwError('An Inject event must be run within an injection context.');
    }
    const diResolverToInstantiatedToken = frameworkDIDebugData.resolverToTokenToDependencies;
    if (!diResolverToInstantiatedToken.has(diResolver)) {
        diResolverToInstantiatedToken.set(diResolver, new WeakMap());
    }
    // if token is a primitive type, ignore this event. We do this because we cannot keep track of
    // non-primitive tokens in WeakMaps since they are not garbage collectable.
    if (!canBeHeldWeakly(context.token)) {
        return;
    }
    const instantiatedTokenToDependencies = diResolverToInstantiatedToken.get(diResolver);
    if (!instantiatedTokenToDependencies.has(context.token)) {
        instantiatedTokenToDependencies.set(context.token, []);
    }
    const { token, value, flags } = data;
    instantiatedTokenToDependencies.get(context.token).push({ token, value, flags });
}
/**
 *
 * If the created instance is an instance of a standalone component, maps the injector to that
 * standalone component in frameworkDIDebugData.standaloneInjectorToComponent
 *
 * @param context InjectorProfilerContext the injection context that this event occurred in.
 * @param data InjectorCreatedInstance an object containing the instance that was just created
 *
 */
function handleInstanceCreatedByInjectorEvent(context, data) {
    const { value } = data;
    if (getDIResolver(context.injector) === null) {
        throwError('An InjectorCreatedInstance event must be run within an injection context.');
    }
    // if our value is an instance of a standalone component, map the injector of that standalone
    // component to the component class. Otherwise, this event is a noop.
    let standaloneComponent = undefined;
    if (typeof value === 'object') {
        standaloneComponent = value?.constructor;
    }
    if (standaloneComponent === undefined || !isStandaloneComponent(standaloneComponent)) {
        return;
    }
    const environmentInjector = context.injector.get(EnvironmentInjector, null, { optional: true });
    // Standalone components should have an environment injector. If one cannot be
    // found we may be in a test case for low level functionality that did not explictly
    // setup this injector. In those cases, we simply ignore this event.
    if (environmentInjector === null) {
        return;
    }
    const { standaloneInjectorToComponent } = frameworkDIDebugData;
    // If our injector has already been mapped, as is the case
    // when a standalone component imports another standalone component,
    // we consider the original component (the component doing the importing)
    // as the component connected to our injector.
    if (standaloneInjectorToComponent.has(environmentInjector)) {
        return;
    }
    // If our injector hasn't been mapped, then we map it to the standalone component
    standaloneInjectorToComponent.set(environmentInjector, standaloneComponent);
}
function isStandaloneComponent(value) {
    const def = getComponentDef(value);
    return !!def?.standalone;
}
/**
 *
 * Stores the emitted ProviderRecords from the InjectorProfilerEventType.ProviderConfigured
 * event in frameworkDIDebugData.resolverToProviders
 *
 * @param context InjectorProfilerContext the injection context that this event occurred in.
 * @param data ProviderRecord an object containing the instance that was just created
 *
 */
function handleProviderConfiguredEvent(context, data) {
    const { resolverToProviders } = frameworkDIDebugData;
    const diResolver = getDIResolver(context?.injector);
    if (diResolver === null) {
        throwError('A ProviderConfigured event must be run within an injection context.');
    }
    if (!resolverToProviders.has(diResolver)) {
        resolverToProviders.set(diResolver, []);
    }
    resolverToProviders.get(diResolver).push(data);
}
function getDIResolver(injector) {
    let diResolver = null;
    if (injector === undefined) {
        return diResolver;
    }
    // We use the LView as the diResolver for NodeInjectors because they
    // do not persist anywhere in the framework. They are simply wrappers around an LView and a TNode
    // that do persist. Because of this, we rely on the LView of the NodeInjector in order to use
    // as a concrete key to represent this injector. If we get the same LView back later, we know
    // we're looking at the same injector.
    if (injector instanceof NodeInjector) {
        diResolver = getNodeInjectorLView(injector);
    }
    // Other injectors can be used a keys for a map because their instances
    // persist
    else {
        diResolver = injector;
    }
    return diResolver;
}
// inspired by
// https://tc39.es/ecma262/multipage/executable-code-and-execution-contexts.html#sec-canbeheldweakly
function canBeHeldWeakly(value) {
    // we check for value !== null here because typeof null === 'object
    return value !== null &&
        (typeof value === 'object' || typeof value === 'function' || typeof value === 'symbol');
}
//# sourceMappingURL=data:application/json;base64,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