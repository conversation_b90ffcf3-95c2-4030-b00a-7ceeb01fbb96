/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertInInjectionContext, DestroyRef, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
/**
 * Operator which completes the Observable when the calling context (component, directive, service,
 * etc) is destroyed.
 *
 * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be
 *     passed explicitly to use `takeUntilDestroyed` outside of an [injection
 * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.
 *
 * @developerPreview
 */
export function takeUntilDestroyed(destroyRef) {
    if (!destroyRef) {
        assertInInjectionContext(takeUntilDestroyed);
        destroyRef = inject(DestroyRef);
    }
    const destroyed$ = new Observable(observer => {
        const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));
        return unregisterFn;
    });
    return (source) => {
        return source.pipe(takeUntil(destroyed$));
    };
}
//# sourceMappingURL=data:application/json;base64,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