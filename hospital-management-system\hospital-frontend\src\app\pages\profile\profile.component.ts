import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { KeycloakService } from 'keycloak-angular';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="row">
        <div class="col-md-8 mx-auto">
          <div class="card">
            <div class="card-header bg-primary text-white">
              <h4 class="mb-0">
                <i class="fas fa-user-circle me-2"></i>User Profile
              </h4>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-4 text-center">
                  <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-muted"></i>
                  </div>
                  <h5>{{ userProfile?.firstName }} {{ userProfile?.lastName }}</h5>
                  <p class="text-muted">{{ getUserRole() }}</p>
                </div>
                <div class="col-md-8">
                  <div class="row mb-3">
                    <div class="col-sm-4"><strong>First Name:</strong></div>
                    <div class="col-sm-8">{{ userProfile?.firstName || 'N/A' }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4"><strong>Last Name:</strong></div>
                    <div class="col-sm-8">{{ userProfile?.lastName || 'N/A' }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4"><strong>Email:</strong></div>
                    <div class="col-sm-8">{{ userProfile?.email || 'N/A' }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4"><strong>Username:</strong></div>
                    <div class="col-sm-8">{{ userProfile?.username || 'N/A' }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4"><strong>Roles:</strong></div>
                    <div class="col-sm-8">
                      <span *ngFor="let role of userRoles" class="badge bg-primary me-1">
                        {{ role }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="mt-4">
                <button class="btn btn-primary me-2">
                  <i class="fas fa-edit me-1"></i>Edit Profile
                </button>
                <button class="btn btn-outline-secondary">
                  <i class="fas fa-key me-1"></i>Change Password
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class ProfileComponent implements OnInit {
  userProfile: any = {};
  userRoles: string[] = [];

  constructor(private keycloakService: KeycloakService) {}

  async ngOnInit() {
    try {
      this.userProfile = await this.keycloakService.loadUserProfile();
      this.userRoles = this.keycloakService.getUserRoles();
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }

  getUserRole(): string {
    if (this.userRoles.includes('ADMIN')) return 'Administrator';
    if (this.userRoles.includes('DOCTOR')) return 'Doctor';
    if (this.userRoles.includes('NURSE')) return 'Nurse';
    if (this.userRoles.includes('PHARMACIST')) return 'Pharmacist';
    if (this.userRoles.includes('LAB_TECHNICIAN')) return 'Lab Technician';
    if (this.userRoles.includes('RADIOLOGIST')) return 'Radiologist';
    if (this.userRoles.includes('RECEPTIONIST')) return 'Receptionist';
    if (this.userRoles.includes('THERAPIST')) return 'Therapist';
    if (this.userRoles.includes('TECHNICIAN')) return 'Technician';
    if (this.userRoles.includes('PATIENT')) return 'Patient';
    return 'User';
  }
}
