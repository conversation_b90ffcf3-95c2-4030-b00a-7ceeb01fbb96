/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { getLViewById } from './lview_tracking';
/**
 * The internal view context which is specific to a given DOM element, directive or
 * component instance. Each value in here (besides the LView and element node details)
 * can be present, null or undefined. If undefined then it implies the value has not been
 * looked up yet, otherwise, if null, then a lookup was executed and nothing was found.
 *
 * Each value will get filled when the respective value is examined within the getContext
 * function. The component, element and each directive instance will share the same instance
 * of the context.
 */
export class LContext {
    /** Component's parent view data. */
    get lView() {
        return getLViewById(this.lViewId);
    }
    constructor(
    /**
     * ID of the component's parent view data.
     */
    lViewId, 
    /**
     * The index instance of the node.
     */
    nodeIndex, 
    /**
     * The instance of the DOM node that is attached to the lNode.
     */
    native) {
        this.lViewId = lViewId;
        this.nodeIndex = nodeIndex;
        this.native = native;
    }
}
//# sourceMappingURL=data:application/json;base64,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