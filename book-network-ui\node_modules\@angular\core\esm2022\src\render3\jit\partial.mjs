/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { FactoryTarget, getCompilerFacade } from '../../compiler/compiler_facade';
import { setClassMetadata } from '../metadata';
import { angularCoreEnv } from './environment';
/**
 * Compiles a partial directive declaration object into a full directive definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareDirective(decl) {
    const compiler = getCompilerFacade({ usage: 1 /* JitCompilerUsage.PartialDeclaration */, kind: 'directive', type: decl.type });
    return compiler.compileDirectiveDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵfac.js`, decl);
}
/**
 * Evaluates the class metadata declaration.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareClassMetadata(decl) {
    setClassMetadata(decl.type, decl.decorators, decl.ctorParameters ?? null, decl.propDecorators ?? null);
}
/**
 * Compiles a partial component declaration object into a full component definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareComponent(decl) {
    const compiler = getCompilerFacade({ usage: 1 /* JitCompilerUsage.PartialDeclaration */, kind: 'component', type: decl.type });
    return compiler.compileComponentDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵcmp.js`, decl);
}
/**
 * Compiles a partial pipe declaration object into a full pipe definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareFactory(decl) {
    const compiler = getCompilerFacade({
        usage: 1 /* JitCompilerUsage.PartialDeclaration */,
        kind: getFactoryKind(decl.target),
        type: decl.type
    });
    return compiler.compileFactoryDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵfac.js`, decl);
}
function getFactoryKind(target) {
    switch (target) {
        case FactoryTarget.Directive:
            return 'directive';
        case FactoryTarget.Component:
            return 'component';
        case FactoryTarget.Injectable:
            return 'injectable';
        case FactoryTarget.Pipe:
            return 'pipe';
        case FactoryTarget.NgModule:
            return 'NgModule';
    }
}
/**
 * Compiles a partial injectable declaration object into a full injectable definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareInjectable(decl) {
    const compiler = getCompilerFacade({ usage: 1 /* JitCompilerUsage.PartialDeclaration */, kind: 'injectable', type: decl.type });
    return compiler.compileInjectableDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵprov.js`, decl);
}
/**
 * These enums are used in the partial factory declaration calls.
 */
export { FactoryTarget } from '../../compiler/compiler_facade';
/**
 * Compiles a partial injector declaration object into a full injector definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareInjector(decl) {
    const compiler = getCompilerFacade({ usage: 1 /* JitCompilerUsage.PartialDeclaration */, kind: 'NgModule', type: decl.type });
    return compiler.compileInjectorDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵinj.js`, decl);
}
/**
 * Compiles a partial NgModule declaration object into a full NgModule definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclareNgModule(decl) {
    const compiler = getCompilerFacade({ usage: 1 /* JitCompilerUsage.PartialDeclaration */, kind: 'NgModule', type: decl.type });
    return compiler.compileNgModuleDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵmod.js`, decl);
}
/**
 * Compiles a partial pipe declaration object into a full pipe definition object.
 *
 * @codeGenApi
 */
export function ɵɵngDeclarePipe(decl) {
    const compiler = getCompilerFacade({ usage: 1 /* JitCompilerUsage.PartialDeclaration */, kind: 'pipe', type: decl.type });
    return compiler.compilePipeDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵpipe.js`, decl);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicGFydGlhbC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL3JlbmRlcjMvaml0L3BhcnRpYWwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxFQUFDLGFBQWEsRUFBRSxpQkFBaUIsRUFBaU0sTUFBTSxnQ0FBZ0MsQ0FBQztBQUVoUixPQUFPLEVBQUMsZ0JBQWdCLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDN0MsT0FBTyxFQUFDLGNBQWMsRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUU3Qzs7OztHQUlHO0FBQ0gsTUFBTSxVQUFVLG9CQUFvQixDQUFDLElBQThCO0lBQ2pFLE1BQU0sUUFBUSxHQUFHLGlCQUFpQixDQUM5QixFQUFDLEtBQUssNkNBQXFDLEVBQUUsSUFBSSxFQUFFLFdBQVcsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBQyxDQUFDLENBQUM7SUFDdEYsT0FBTyxRQUFRLENBQUMsMkJBQTJCLENBQ3ZDLGNBQWMsRUFBRSxTQUFTLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUM7QUFDL0QsQ0FBQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFNLFVBQVUsd0JBQXdCLENBQUMsSUFJeEM7SUFDQyxnQkFBZ0IsQ0FDWixJQUFJLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxVQUFVLEVBQUUsSUFBSSxDQUFDLGNBQWMsSUFBSSxJQUFJLEVBQUUsSUFBSSxDQUFDLGNBQWMsSUFBSSxJQUFJLENBQUMsQ0FBQztBQUM1RixDQUFDO0FBRUQ7Ozs7R0FJRztBQUNILE1BQU0sVUFBVSxvQkFBb0IsQ0FBQyxJQUE4QjtJQUNqRSxNQUFNLFFBQVEsR0FBRyxpQkFBaUIsQ0FDOUIsRUFBQyxLQUFLLDZDQUFxQyxFQUFFLElBQUksRUFBRSxXQUFXLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxJQUFJLEVBQUMsQ0FBQyxDQUFDO0lBQ3RGLE9BQU8sUUFBUSxDQUFDLDJCQUEyQixDQUN2QyxjQUFjLEVBQUUsU0FBUyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksVUFBVSxFQUFFLElBQUksQ0FBQyxDQUFDO0FBQy9ELENBQUM7QUFFRDs7OztHQUlHO0FBQ0gsTUFBTSxVQUFVLGtCQUFrQixDQUFDLElBQTRCO0lBQzdELE1BQU0sUUFBUSxHQUFHLGlCQUFpQixDQUFDO1FBQ2pDLEtBQUssNkNBQXFDO1FBQzFDLElBQUksRUFBRSxjQUFjLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQztRQUNqQyxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUk7S0FDaEIsQ0FBQyxDQUFDO0lBQ0gsT0FBTyxRQUFRLENBQUMseUJBQXlCLENBQ3JDLGNBQWMsRUFBRSxTQUFTLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUM7QUFDL0QsQ0FBQztBQUVELFNBQVMsY0FBYyxDQUFDLE1BQXFCO0lBQzNDLFFBQVEsTUFBTSxFQUFFO1FBQ2QsS0FBSyxhQUFhLENBQUMsU0FBUztZQUMxQixPQUFPLFdBQVcsQ0FBQztRQUNyQixLQUFLLGFBQWEsQ0FBQyxTQUFTO1lBQzFCLE9BQU8sV0FBVyxDQUFDO1FBQ3JCLEtBQUssYUFBYSxDQUFDLFVBQVU7WUFDM0IsT0FBTyxZQUFZLENBQUM7UUFDdEIsS0FBSyxhQUFhLENBQUMsSUFBSTtZQUNyQixPQUFPLE1BQU0sQ0FBQztRQUNoQixLQUFLLGFBQWEsQ0FBQyxRQUFRO1lBQ3pCLE9BQU8sVUFBVSxDQUFDO0tBQ3JCO0FBQ0gsQ0FBQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFNLFVBQVUscUJBQXFCLENBQUMsSUFBK0I7SUFDbkUsTUFBTSxRQUFRLEdBQUcsaUJBQWlCLENBQzlCLEVBQUMsS0FBSyw2Q0FBcUMsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsSUFBSSxFQUFDLENBQUMsQ0FBQztJQUN2RixPQUFPLFFBQVEsQ0FBQyw0QkFBNEIsQ0FDeEMsY0FBYyxFQUFFLFNBQVMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLFdBQVcsRUFBRSxJQUFJLENBQUMsQ0FBQztBQUNoRSxDQUFDO0FBRUQ7O0dBRUc7QUFDSCxPQUFPLEVBQUMsYUFBYSxFQUFDLE1BQU0sZ0NBQWdDLENBQUM7QUFFN0Q7Ozs7R0FJRztBQUNILE1BQU0sVUFBVSxtQkFBbUIsQ0FBQyxJQUE2QjtJQUMvRCxNQUFNLFFBQVEsR0FBRyxpQkFBaUIsQ0FDOUIsRUFBQyxLQUFLLDZDQUFxQyxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsSUFBSSxFQUFFLElBQUksQ0FBQyxJQUFJLEVBQUMsQ0FBQyxDQUFDO0lBQ3JGLE9BQU8sUUFBUSxDQUFDLDBCQUEwQixDQUN0QyxjQUFjLEVBQUUsU0FBUyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksVUFBVSxFQUFFLElBQUksQ0FBQyxDQUFDO0FBQy9ELENBQUM7QUFFRDs7OztHQUlHO0FBQ0gsTUFBTSxVQUFVLG1CQUFtQixDQUFDLElBQTZCO0lBQy9ELE1BQU0sUUFBUSxHQUFHLGlCQUFpQixDQUM5QixFQUFDLEtBQUssNkNBQXFDLEVBQUUsSUFBSSxFQUFFLFVBQVUsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBQyxDQUFDLENBQUM7SUFDckYsT0FBTyxRQUFRLENBQUMsMEJBQTBCLENBQ3RDLGNBQWMsRUFBRSxTQUFTLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxVQUFVLEVBQUUsSUFBSSxDQUFDLENBQUM7QUFDL0QsQ0FBQztBQUVEOzs7O0dBSUc7QUFDSCxNQUFNLFVBQVUsZUFBZSxDQUFDLElBQXlCO0lBQ3ZELE1BQU0sUUFBUSxHQUFHLGlCQUFpQixDQUM5QixFQUFDLEtBQUssNkNBQXFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBQyxDQUFDLENBQUM7SUFDakYsT0FBTyxRQUFRLENBQUMsc0JBQXNCLENBQUMsY0FBYyxFQUFFLFNBQVMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLFdBQVcsRUFBRSxJQUFJLENBQUMsQ0FBQztBQUNuRyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7RmFjdG9yeVRhcmdldCwgZ2V0Q29tcGlsZXJGYWNhZGUsIEppdENvbXBpbGVyVXNhZ2UsIFIzRGVjbGFyZUNvbXBvbmVudEZhY2FkZSwgUjNEZWNsYXJlRGlyZWN0aXZlRmFjYWRlLCBSM0RlY2xhcmVGYWN0b3J5RmFjYWRlLCBSM0RlY2xhcmVJbmplY3RhYmxlRmFjYWRlLCBSM0RlY2xhcmVJbmplY3RvckZhY2FkZSwgUjNEZWNsYXJlTmdNb2R1bGVGYWNhZGUsIFIzRGVjbGFyZVBpcGVGYWNhZGV9IGZyb20gJy4uLy4uL2NvbXBpbGVyL2NvbXBpbGVyX2ZhY2FkZSc7XG5pbXBvcnQge1R5cGV9IGZyb20gJy4uLy4uL2ludGVyZmFjZS90eXBlJztcbmltcG9ydCB7c2V0Q2xhc3NNZXRhZGF0YX0gZnJvbSAnLi4vbWV0YWRhdGEnO1xuaW1wb3J0IHthbmd1bGFyQ29yZUVudn0gZnJvbSAnLi9lbnZpcm9ubWVudCc7XG5cbi8qKlxuICogQ29tcGlsZXMgYSBwYXJ0aWFsIGRpcmVjdGl2ZSBkZWNsYXJhdGlvbiBvYmplY3QgaW50byBhIGZ1bGwgZGlyZWN0aXZlIGRlZmluaXRpb24gb2JqZWN0LlxuICpcbiAqIEBjb2RlR2VuQXBpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiDJtcm1bmdEZWNsYXJlRGlyZWN0aXZlKGRlY2w6IFIzRGVjbGFyZURpcmVjdGl2ZUZhY2FkZSk6IHVua25vd24ge1xuICBjb25zdCBjb21waWxlciA9IGdldENvbXBpbGVyRmFjYWRlKFxuICAgICAge3VzYWdlOiBKaXRDb21waWxlclVzYWdlLlBhcnRpYWxEZWNsYXJhdGlvbiwga2luZDogJ2RpcmVjdGl2ZScsIHR5cGU6IGRlY2wudHlwZX0pO1xuICByZXR1cm4gY29tcGlsZXIuY29tcGlsZURpcmVjdGl2ZURlY2xhcmF0aW9uKFxuICAgICAgYW5ndWxhckNvcmVFbnYsIGBuZzovLy8ke2RlY2wudHlwZS5uYW1lfS/JtWZhYy5qc2AsIGRlY2wpO1xufVxuXG4vKipcbiAqIEV2YWx1YXRlcyB0aGUgY2xhc3MgbWV0YWRhdGEgZGVjbGFyYXRpb24uXG4gKlxuICogQGNvZGVHZW5BcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVuZ0RlY2xhcmVDbGFzc01ldGFkYXRhKGRlY2w6IHtcbiAgdHlwZTogVHlwZTxhbnk+OyBkZWNvcmF0b3JzOiBhbnlbXTtcbiAgY3RvclBhcmFtZXRlcnM/OiAoKSA9PiBhbnlbXTtcbiAgcHJvcERlY29yYXRvcnM/OiB7W2ZpZWxkOiBzdHJpbmddOiBhbnl9O1xufSk6IHZvaWQge1xuICBzZXRDbGFzc01ldGFkYXRhKFxuICAgICAgZGVjbC50eXBlLCBkZWNsLmRlY29yYXRvcnMsIGRlY2wuY3RvclBhcmFtZXRlcnMgPz8gbnVsbCwgZGVjbC5wcm9wRGVjb3JhdG9ycyA/PyBudWxsKTtcbn1cblxuLyoqXG4gKiBDb21waWxlcyBhIHBhcnRpYWwgY29tcG9uZW50IGRlY2xhcmF0aW9uIG9iamVjdCBpbnRvIGEgZnVsbCBjb21wb25lbnQgZGVmaW5pdGlvbiBvYmplY3QuXG4gKlxuICogQGNvZGVHZW5BcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVuZ0RlY2xhcmVDb21wb25lbnQoZGVjbDogUjNEZWNsYXJlQ29tcG9uZW50RmFjYWRlKTogdW5rbm93biB7XG4gIGNvbnN0IGNvbXBpbGVyID0gZ2V0Q29tcGlsZXJGYWNhZGUoXG4gICAgICB7dXNhZ2U6IEppdENvbXBpbGVyVXNhZ2UuUGFydGlhbERlY2xhcmF0aW9uLCBraW5kOiAnY29tcG9uZW50JywgdHlwZTogZGVjbC50eXBlfSk7XG4gIHJldHVybiBjb21waWxlci5jb21waWxlQ29tcG9uZW50RGVjbGFyYXRpb24oXG4gICAgICBhbmd1bGFyQ29yZUVudiwgYG5nOi8vLyR7ZGVjbC50eXBlLm5hbWV9L8m1Y21wLmpzYCwgZGVjbCk7XG59XG5cbi8qKlxuICogQ29tcGlsZXMgYSBwYXJ0aWFsIHBpcGUgZGVjbGFyYXRpb24gb2JqZWN0IGludG8gYSBmdWxsIHBpcGUgZGVmaW5pdGlvbiBvYmplY3QuXG4gKlxuICogQGNvZGVHZW5BcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVuZ0RlY2xhcmVGYWN0b3J5KGRlY2w6IFIzRGVjbGFyZUZhY3RvcnlGYWNhZGUpOiB1bmtub3duIHtcbiAgY29uc3QgY29tcGlsZXIgPSBnZXRDb21waWxlckZhY2FkZSh7XG4gICAgdXNhZ2U6IEppdENvbXBpbGVyVXNhZ2UuUGFydGlhbERlY2xhcmF0aW9uLFxuICAgIGtpbmQ6IGdldEZhY3RvcnlLaW5kKGRlY2wudGFyZ2V0KSxcbiAgICB0eXBlOiBkZWNsLnR5cGVcbiAgfSk7XG4gIHJldHVybiBjb21waWxlci5jb21waWxlRmFjdG9yeURlY2xhcmF0aW9uKFxuICAgICAgYW5ndWxhckNvcmVFbnYsIGBuZzovLy8ke2RlY2wudHlwZS5uYW1lfS/JtWZhYy5qc2AsIGRlY2wpO1xufVxuXG5mdW5jdGlvbiBnZXRGYWN0b3J5S2luZCh0YXJnZXQ6IEZhY3RvcnlUYXJnZXQpIHtcbiAgc3dpdGNoICh0YXJnZXQpIHtcbiAgICBjYXNlIEZhY3RvcnlUYXJnZXQuRGlyZWN0aXZlOlxuICAgICAgcmV0dXJuICdkaXJlY3RpdmUnO1xuICAgIGNhc2UgRmFjdG9yeVRhcmdldC5Db21wb25lbnQ6XG4gICAgICByZXR1cm4gJ2NvbXBvbmVudCc7XG4gICAgY2FzZSBGYWN0b3J5VGFyZ2V0LkluamVjdGFibGU6XG4gICAgICByZXR1cm4gJ2luamVjdGFibGUnO1xuICAgIGNhc2UgRmFjdG9yeVRhcmdldC5QaXBlOlxuICAgICAgcmV0dXJuICdwaXBlJztcbiAgICBjYXNlIEZhY3RvcnlUYXJnZXQuTmdNb2R1bGU6XG4gICAgICByZXR1cm4gJ05nTW9kdWxlJztcbiAgfVxufVxuXG4vKipcbiAqIENvbXBpbGVzIGEgcGFydGlhbCBpbmplY3RhYmxlIGRlY2xhcmF0aW9uIG9iamVjdCBpbnRvIGEgZnVsbCBpbmplY3RhYmxlIGRlZmluaXRpb24gb2JqZWN0LlxuICpcbiAqIEBjb2RlR2VuQXBpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiDJtcm1bmdEZWNsYXJlSW5qZWN0YWJsZShkZWNsOiBSM0RlY2xhcmVJbmplY3RhYmxlRmFjYWRlKTogdW5rbm93biB7XG4gIGNvbnN0IGNvbXBpbGVyID0gZ2V0Q29tcGlsZXJGYWNhZGUoXG4gICAgICB7dXNhZ2U6IEppdENvbXBpbGVyVXNhZ2UuUGFydGlhbERlY2xhcmF0aW9uLCBraW5kOiAnaW5qZWN0YWJsZScsIHR5cGU6IGRlY2wudHlwZX0pO1xuICByZXR1cm4gY29tcGlsZXIuY29tcGlsZUluamVjdGFibGVEZWNsYXJhdGlvbihcbiAgICAgIGFuZ3VsYXJDb3JlRW52LCBgbmc6Ly8vJHtkZWNsLnR5cGUubmFtZX0vybVwcm92LmpzYCwgZGVjbCk7XG59XG5cbi8qKlxuICogVGhlc2UgZW51bXMgYXJlIHVzZWQgaW4gdGhlIHBhcnRpYWwgZmFjdG9yeSBkZWNsYXJhdGlvbiBjYWxscy5cbiAqL1xuZXhwb3J0IHtGYWN0b3J5VGFyZ2V0fSBmcm9tICcuLi8uLi9jb21waWxlci9jb21waWxlcl9mYWNhZGUnO1xuXG4vKipcbiAqIENvbXBpbGVzIGEgcGFydGlhbCBpbmplY3RvciBkZWNsYXJhdGlvbiBvYmplY3QgaW50byBhIGZ1bGwgaW5qZWN0b3IgZGVmaW5pdGlvbiBvYmplY3QuXG4gKlxuICogQGNvZGVHZW5BcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVuZ0RlY2xhcmVJbmplY3RvcihkZWNsOiBSM0RlY2xhcmVJbmplY3RvckZhY2FkZSk6IHVua25vd24ge1xuICBjb25zdCBjb21waWxlciA9IGdldENvbXBpbGVyRmFjYWRlKFxuICAgICAge3VzYWdlOiBKaXRDb21waWxlclVzYWdlLlBhcnRpYWxEZWNsYXJhdGlvbiwga2luZDogJ05nTW9kdWxlJywgdHlwZTogZGVjbC50eXBlfSk7XG4gIHJldHVybiBjb21waWxlci5jb21waWxlSW5qZWN0b3JEZWNsYXJhdGlvbihcbiAgICAgIGFuZ3VsYXJDb3JlRW52LCBgbmc6Ly8vJHtkZWNsLnR5cGUubmFtZX0vybVpbmouanNgLCBkZWNsKTtcbn1cblxuLyoqXG4gKiBDb21waWxlcyBhIHBhcnRpYWwgTmdNb2R1bGUgZGVjbGFyYXRpb24gb2JqZWN0IGludG8gYSBmdWxsIE5nTW9kdWxlIGRlZmluaXRpb24gb2JqZWN0LlxuICpcbiAqIEBjb2RlR2VuQXBpXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiDJtcm1bmdEZWNsYXJlTmdNb2R1bGUoZGVjbDogUjNEZWNsYXJlTmdNb2R1bGVGYWNhZGUpOiB1bmtub3duIHtcbiAgY29uc3QgY29tcGlsZXIgPSBnZXRDb21waWxlckZhY2FkZShcbiAgICAgIHt1c2FnZTogSml0Q29tcGlsZXJVc2FnZS5QYXJ0aWFsRGVjbGFyYXRpb24sIGtpbmQ6ICdOZ01vZHVsZScsIHR5cGU6IGRlY2wudHlwZX0pO1xuICByZXR1cm4gY29tcGlsZXIuY29tcGlsZU5nTW9kdWxlRGVjbGFyYXRpb24oXG4gICAgICBhbmd1bGFyQ29yZUVudiwgYG5nOi8vLyR7ZGVjbC50eXBlLm5hbWV9L8m1bW9kLmpzYCwgZGVjbCk7XG59XG5cbi8qKlxuICogQ29tcGlsZXMgYSBwYXJ0aWFsIHBpcGUgZGVjbGFyYXRpb24gb2JqZWN0IGludG8gYSBmdWxsIHBpcGUgZGVmaW5pdGlvbiBvYmplY3QuXG4gKlxuICogQGNvZGVHZW5BcGlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIMm1ybVuZ0RlY2xhcmVQaXBlKGRlY2w6IFIzRGVjbGFyZVBpcGVGYWNhZGUpOiB1bmtub3duIHtcbiAgY29uc3QgY29tcGlsZXIgPSBnZXRDb21waWxlckZhY2FkZShcbiAgICAgIHt1c2FnZTogSml0Q29tcGlsZXJVc2FnZS5QYXJ0aWFsRGVjbGFyYXRpb24sIGtpbmQ6ICdwaXBlJywgdHlwZTogZGVjbC50eXBlfSk7XG4gIHJldHVybiBjb21waWxlci5jb21waWxlUGlwZURlY2xhcmF0aW9uKGFuZ3VsYXJDb3JlRW52LCBgbmc6Ly8vJHtkZWNsLnR5cGUubmFtZX0vybVwaXBlLmpzYCwgZGVjbCk7XG59XG4iXX0=