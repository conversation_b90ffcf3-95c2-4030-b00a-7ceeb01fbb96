package com.hospital.patient;

import com.hospital.common.BaseEntity;
import com.hospital.doctor.Doctor;
import com.hospital.nurse.Nurse;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "patient")
public class Patient extends BaseEntity {

    private String firstName;
    private String lastName;
    private LocalDate dateOfBirth;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String address;
    private String emergencyContact;
    private String emergencyContactPhone;
    private String bloodType;
    private String allergies;
    private String medicalHistory;
    
    @Enumerated(EnumType.STRING)
    private Gender gender;
    
    @Enumerated(EnumType.STRING)
    private PatientStatus status;
    
    @ManyToOne
    @JoinColumn(name = "assigned_doctor_id")
    private Doctor assignedDoctor;
    
    @ManyToMany(mappedBy = "assignedPatients")
    private List<Nurse> assignedNurses;

    public String getFullName() {
        return firstName + " " + lastName;
    }
    
    public Integer getAge() {
        if (dateOfBirth != null) {
            return LocalDate.now().getYear() - dateOfBirth.getYear();
        }
        return null;
    }
}

enum Gender {
    MALE,
    FEMALE,
    OTHER
}

enum PatientStatus {
    ACTIVE,
    DISCHARGED,
    ADMITTED,
    EMERGENCY,
    DECEASED
}
