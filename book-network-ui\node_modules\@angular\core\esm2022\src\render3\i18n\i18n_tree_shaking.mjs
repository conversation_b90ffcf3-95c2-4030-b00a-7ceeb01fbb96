/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
let _icuContainerIterate;
/**
 * Iterator which provides ability to visit all of the `TIcuContainerNode` root `RNode`s.
 */
export function icuContainerIterate(tIcuContainerNode, lView) {
    return _icuContainerIterate(tIcuContainerNode, lView);
}
/**
 * Ensures that `IcuContainerVisitor`'s implementation is present.
 *
 * This function is invoked when i18n instruction comes across an ICU. The purpose is to allow the
 * bundler to tree shake ICU logic and only load it if ICU instruction is executed.
 */
export function ensureIcuContainerVisitorLoaded(loader) {
    if (_icuContainerIterate === undefined) {
        // Do not inline this function. We want to keep `ensureIcuContainerVisitorLoaded` light, so it
        // can be inlined into call-site.
        _icuContainerIterate = loader();
    }
}
//# sourceMappingURL=data:application/json;base64,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