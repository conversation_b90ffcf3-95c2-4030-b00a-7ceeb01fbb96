/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { defaultEquals, SIGNAL } from './api';
import { throwInvalidWriteToSignalError } from './errors';
import { producerAccessed, producerNotifyConsumers, producerUpdatesAllowed, REACTIVE_NODE, } from './graph';
/**
 * If set, called after `WritableSignal`s are updated.
 *
 * This hook can be used to achieve various effects, such as running effects synchronously as part
 * of setting a signal.
 */
let postSignalSetFn = null;
/**
 * Create a `Signal` that can be set or updated directly.
 *
 * @developerPreview
 */
export function signal(initialValue, options) {
    const node = Object.create(SIGNAL_NODE);
    node.value = initialValue;
    options?.equal && (node.equal = options.equal);
    function signalFn() {
        producerAccessed(node);
        return node.value;
    }
    signalFn.set = signalSetFn;
    signalFn.update = signalUpdateFn;
    signalFn.mutate = signalMutateFn;
    signalFn.asReadonly = signalAsReadonlyFn;
    signalFn[SIGNAL] = node;
    return signalFn;
}
export function setPostSignalSetFn(fn) {
    const prev = postSignalSetFn;
    postSignalSetFn = fn;
    return prev;
}
// Note: Using an IIFE here to ensure that the spread assignment is not considered
// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.
// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.
const SIGNAL_NODE = /* @__PURE__ */ (() => {
    return {
        ...REACTIVE_NODE,
        equal: defaultEquals,
        readonlyFn: undefined,
    };
})();
function signalValueChanged(node) {
    node.version++;
    producerNotifyConsumers(node);
    postSignalSetFn?.();
}
function signalSetFn(newValue) {
    const node = this[SIGNAL];
    if (!producerUpdatesAllowed()) {
        throwInvalidWriteToSignalError();
    }
    if (!node.equal(node.value, newValue)) {
        node.value = newValue;
        signalValueChanged(node);
    }
}
function signalUpdateFn(updater) {
    if (!producerUpdatesAllowed()) {
        throwInvalidWriteToSignalError();
    }
    signalSetFn.call(this, updater(this[SIGNAL].value));
}
function signalMutateFn(mutator) {
    const node = this[SIGNAL];
    if (!producerUpdatesAllowed()) {
        throwInvalidWriteToSignalError();
    }
    // Mutate bypasses equality checks as it's by definition changing the value.
    mutator(node.value);
    signalValueChanged(node);
}
function signalAsReadonlyFn() {
    const node = this[SIGNAL];
    if (node.readonlyFn === undefined) {
        const readonlyFn = () => this();
        readonlyFn[SIGNAL] = node;
        node.readonlyFn = readonlyFn;
    }
    return node.readonlyFn;
}
//# sourceMappingURL=data:application/json;base64,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