package com.hospital.nurse;

import com.hospital.common.BaseEntity;
import com.hospital.patient.Patient;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "nurse")
public class Nurse extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String department;
    private String shift; // DAY, NIGHT, ROTATING
    private String licenseNumber;
    private Integer yearsOfExperience;
    
    @Enumerated(EnumType.STRING)
    private NurseStatus status;
    
    @ManyToMany
    @JoinTable(
        name = "nurse_patient_assignment",
        joinColumns = @JoinColumn(name = "nurse_id"),
        inverseJoinColumns = @JoinColumn(name = "patient_id")
    )
    private List<Patient> assignedPatients;

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum NurseStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    RETIRED
}
