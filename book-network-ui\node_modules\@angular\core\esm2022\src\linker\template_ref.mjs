/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { getCurrentTNode, getLView } from '../render3/state';
import { createAndRenderEmbeddedLView } from '../render3/view_manipulation';
import { ViewRef as R3_ViewRef } from '../render3/view_ref';
import { assertDefined } from '../util/assert';
import { createElementRef } from './element_ref';
/**
 * Represents an embedded template that can be used to instantiate embedded views.
 * To instantiate embedded views based on a template, use the `ViewContainerRef`
 * method `createEmbeddedView()`.
 *
 * Access a `TemplateRef` instance by placing a directive on an `<ng-template>`
 * element (or directive prefixed with `*`). The `TemplateRef` for the embedded view
 * is injected into the constructor of the directive,
 * using the `TemplateRef` token.
 *
 * You can also use a `Query` to find a `TemplateRef` associated with
 * a component or a directive.
 *
 * @see {@link ViewContainerRef}
 * @see [Navigate the Component Tree with DI](guide/dependency-injection-navtree)
 *
 * @publicApi
 */
export class TemplateRef {
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ELEMENT_ID__ = injectTemplateRef; }
}
const ViewEngineTemplateRef = TemplateRef;
// TODO(alxhub): combine interface and implementation. Currently this is challenging since something
// in g3 depends on them being separate.
const R3TemplateRef = class TemplateRef extends ViewEngineTemplateRef {
    constructor(_declarationLView, _declarationTContainer, elementRef) {
        super();
        this._declarationLView = _declarationLView;
        this._declarationTContainer = _declarationTContainer;
        this.elementRef = elementRef;
    }
    /**
     * Returns an `ssrId` associated with a TView, which was used to
     * create this instance of the `TemplateRef`.
     *
     * @internal
     */
    get ssrId() {
        return this._declarationTContainer.tView?.ssrId || null;
    }
    createEmbeddedView(context, injector) {
        return this.createEmbeddedViewImpl(context, injector);
    }
    /**
     * @internal
     */
    createEmbeddedViewImpl(context, injector, hydrationInfo) {
        const embeddedLView = createAndRenderEmbeddedLView(this._declarationLView, this._declarationTContainer, context, { injector, hydrationInfo });
        return new R3_ViewRef(embeddedLView);
    }
};
/**
 * Creates a TemplateRef given a node.
 *
 * @returns The TemplateRef instance to use
 */
export function injectTemplateRef() {
    return createTemplateRef(getCurrentTNode(), getLView());
}
/**
 * Creates a TemplateRef and stores it on the injector.
 *
 * @param hostTNode The node on which a TemplateRef is requested
 * @param hostLView The `LView` to which the node belongs
 * @returns The TemplateRef instance or null if we can't create a TemplateRef on a given node type
 */
export function createTemplateRef(hostTNode, hostLView) {
    if (hostTNode.type & 4 /* TNodeType.Container */) {
        ngDevMode && assertDefined(hostTNode.tView, 'TView must be allocated');
        return new R3TemplateRef(hostLView, hostTNode, createElementRef(hostTNode, hostLView));
    }
    return null;
}
//# sourceMappingURL=data:application/json;base64,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