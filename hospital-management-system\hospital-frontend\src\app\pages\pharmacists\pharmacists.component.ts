import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-pharmacists',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-pills me-2"></i>Pharmacist Management</h2>
        <button class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add New Pharmacist
        </button>
      </div>
      
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>License #</th>
                  <th>Specialization</th>
                  <th>Department</th>
                  <th>Shift</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>PH001</td>
                  <td>Dr. <PERSON></td>
                  <td>PH123456</td>
                  <td>Clinical Pharmacy</td>
                  <td>Main Pharmacy</td>
                  <td>Day</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <tr>
                  <td>PH002</td>
                  <td>Dr. Lisa Wilson</td>
                  <td>PH789012</td>
                  <td>Hospital Pharmacy</td>
                  <td>ICU Pharmacy</td>
                  <td>Night</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <!-- More pharmacist rows would be loaded dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class PharmacistsComponent {}
