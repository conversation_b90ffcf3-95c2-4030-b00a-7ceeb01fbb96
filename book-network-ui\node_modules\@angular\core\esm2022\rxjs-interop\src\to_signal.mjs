/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertInInjectionContext, computed, DestroyRef, inject, signal } from '@angular/core';
import { RuntimeError } from '../../src/errors';
import { untracked } from '../../src/signals';
export function toSignal(source, options) {
    const requiresCleanup = !options?.manualCleanup;
    requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);
    const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;
    // Note: T is the Observable value type, and U is the initial value type. They don't have to be
    // the same - the returned signal gives values of type `T`.
    let state;
    if (options?.requireSync) {
        // Initially the signal is in a `NoValue` state.
        state = signal({ kind: 0 /* StateKind.NoValue */ });
    }
    else {
        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.
        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue });
    }
    untracked(() => {
        const sub = source.subscribe({
            next: value => state.set({ kind: 1 /* StateKind.Value */, value }),
            error: error => state.set({ kind: 2 /* StateKind.Error */, error }),
            // Completion of the Observable is meaningless to the signal. Signals don't have a concept of
            // "complete".
        });
        if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {
            throw new RuntimeError(601 /* RuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');
        }
        // Unsubscribe when the current context is destroyed, if requested.
        cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));
    });
    // The actual returned signal is a `computed` of the `State` signal, which maps the various states
    // to either values or errors.
    return computed(() => {
        const current = state();
        switch (current.kind) {
            case 1 /* StateKind.Value */:
                return current.value;
            case 2 /* StateKind.Error */:
                throw current.error;
            case 0 /* StateKind.NoValue */:
                // This shouldn't really happen because the error is thrown on creation.
                // TODO(alxhub): use a RuntimeError when we finalize the error semantics
                throw new RuntimeError(601 /* RuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');
        }
    });
}
//# sourceMappingURL=data:application/json;base64,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