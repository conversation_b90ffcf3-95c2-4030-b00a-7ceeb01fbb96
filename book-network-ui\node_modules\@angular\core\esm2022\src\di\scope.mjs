/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from './injection_token';
/**
 * An internal token whose presence in an injector indicates that the injector should treat itself
 * as a root scoped injector when processing requests for unknown tokens which may indicate
 * they are provided in the root scope.
 */
export const INJECTOR_SCOPE = new InjectionToken('Set Injector scope.');
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2NvcGUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy9kaS9zY29wZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCxPQUFPLEVBQUMsY0FBYyxFQUFDLE1BQU0sbUJBQW1CLENBQUM7QUFLakQ7Ozs7R0FJRztBQUNILE1BQU0sQ0FBQyxNQUFNLGNBQWMsR0FBRyxJQUFJLGNBQWMsQ0FBcUIscUJBQXFCLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQge0luamVjdGlvblRva2VufSBmcm9tICcuL2luamVjdGlvbl90b2tlbic7XG5cblxuZXhwb3J0IHR5cGUgSW5qZWN0b3JTY29wZSA9ICdyb290J3wncGxhdGZvcm0nfCdlbnZpcm9ubWVudCc7XG5cbi8qKlxuICogQW4gaW50ZXJuYWwgdG9rZW4gd2hvc2UgcHJlc2VuY2UgaW4gYW4gaW5qZWN0b3IgaW5kaWNhdGVzIHRoYXQgdGhlIGluamVjdG9yIHNob3VsZCB0cmVhdCBpdHNlbGZcbiAqIGFzIGEgcm9vdCBzY29wZWQgaW5qZWN0b3Igd2hlbiBwcm9jZXNzaW5nIHJlcXVlc3RzIGZvciB1bmtub3duIHRva2VucyB3aGljaCBtYXkgaW5kaWNhdGVcbiAqIHRoZXkgYXJlIHByb3ZpZGVkIGluIHRoZSByb290IHNjb3BlLlxuICovXG5leHBvcnQgY29uc3QgSU5KRUNUT1JfU0NPUEUgPSBuZXcgSW5qZWN0aW9uVG9rZW48SW5qZWN0b3JTY29wZXxudWxsPignU2V0IEluamVjdG9yIHNjb3BlLicpO1xuIl19