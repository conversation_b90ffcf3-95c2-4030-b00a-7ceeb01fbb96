services:
  postgres:
    container_name: postgres-sql-hospital
    image: postgres
    environment:
      POSTGRES_USER: hospital_user
      POSTGRES_PASSWORD: hospital_password
      PGDATA: /var/lib/postgresql/data
      POSTGRES_DB: hospital_management
    volumes:
      - postgres:/data/postgres
    ports:
      - 5433:5432
    networks:
      - hospital-network
    restart: unless-stopped

  mail-dev:
    container_name: mail-dev-hospital
    image: maildev/maildev
    ports:
      - 1081:1080
      - 1026:1025

  keycloak:
    container_name: keycloak-hospital
    image: quay.io/keycloak/keycloak:24.0.2
    ports:
      - 9091:8080
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    networks:
      - hospital-network
    command:
      - "start-dev"
    volumes:
      - ./keycloak/realm:/opt/keycloak/data/import
    # Uncomment the line below to import realm on startup
    # command: ["start-dev", "--import-realm"]

networks:
  hospital-network:
    driver: bridge

volumes:
  postgres:
    driver: local
  keycloak:
    driver: local
