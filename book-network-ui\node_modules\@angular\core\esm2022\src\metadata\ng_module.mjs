/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { compileNgModule } from '../render3/jit/module';
import { makeDecorator } from '../util/decorators';
/**
 * @Annotation
 */
export const NgModule = makeDecorator('NgModule', (ngModule) => ngModule, undefined, undefined, 
/**
 * Decorator that marks the following class as an NgModule, and supplies
 * configuration metadata for it.
 *
 * * The `declarations` option configures the compiler
 * with information about what belongs to the NgModule.
 * * The `providers` options configures the NgModule's injector to provide
 * dependencies the NgModule members.
 * * The `imports` and `exports` options bring in members from other modules, and make
 * this module's members available to others.
 */
(type, meta) => compileNgModule(type, meta));
//# sourceMappingURL=data:application/json;base64,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