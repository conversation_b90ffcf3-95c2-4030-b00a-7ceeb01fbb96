/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// Required as the signals library is in a separate package, so we need to explicitly ensure the
// global `ngDevMode` type is defined.
import '../../util/ng_dev_mode';
/**
 * The currently active consumer `ReactiveNode`, if running code in a reactive context.
 *
 * Change this via `setActiveConsumer`.
 */
let activeConsumer = null;
let inNotificationPhase = false;
export function setActiveConsumer(consumer) {
    const prev = activeConsumer;
    activeConsumer = consumer;
    return prev;
}
export const REACTIVE_NODE = {
    version: 0,
    dirty: false,
    producerNode: undefined,
    producerLastReadVersion: undefined,
    producerIndexOfThis: undefined,
    nextProducerIndex: 0,
    liveConsumerNode: undefined,
    liveConsumerIndexOfThis: undefined,
    consumerAllowSignalWrites: false,
    consumerIsAlwaysLive: false,
    producerMustRecompute: () => false,
    producerRecomputeValue: () => { },
    consumerMarkedDirty: () => { },
};
/**
 * Called by implementations when a producer's signal is read.
 */
export function producerAccessed(node) {
    if (inNotificationPhase) {
        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ?
            `Assertion error: signal read during notification phase` :
            '');
    }
    if (activeConsumer === null) {
        // Accessed outside of a reactive context, so nothing to record.
        return;
    }
    // This producer is the `idx`th dependency of `activeConsumer`.
    const idx = activeConsumer.nextProducerIndex++;
    assertConsumerNode(activeConsumer);
    if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {
        // There's been a change in producers since the last execution of `activeConsumer`.
        // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and
        // replaced with `this`.
        //
        // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in
        // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need
        // to remove it from the stale producer's `liveConsumer`s.
        if (consumerIsLive(activeConsumer)) {
            const staleProducer = activeConsumer.producerNode[idx];
            producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);
            // At this point, the only record of `staleProducer` is the reference at
            // `activeConsumer.producerNode[idx]` which will be overwritten below.
        }
    }
    if (activeConsumer.producerNode[idx] !== node) {
        // We're a new dependency of the consumer (at `idx`).
        activeConsumer.producerNode[idx] = node;
        // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a
        // placeholder value.
        activeConsumer.producerIndexOfThis[idx] =
            consumerIsLive(activeConsumer) ? producerAddLiveConsumer(node, activeConsumer, idx) : 0;
    }
    activeConsumer.producerLastReadVersion[idx] = node.version;
}
/**
 * Ensure this producer's `version` is up-to-date.
 */
export function producerUpdateValueVersion(node) {
    if (consumerIsLive(node) && !node.dirty) {
        // A live consumer will be marked dirty by producers, so a clean state means that its version
        // is guaranteed to be up-to-date.
        return;
    }
    if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {
        // None of our producers report a change since the last time they were read, so no
        // recomputation of our value is necessary, and we can consider ourselves clean.
        node.dirty = false;
        return;
    }
    node.producerRecomputeValue(node);
    // After recomputing the value, we're no longer dirty.
    node.dirty = false;
}
/**
 * Propagate a dirty notification to live consumers of this producer.
 */
export function producerNotifyConsumers(node) {
    if (node.liveConsumerNode === undefined) {
        return;
    }
    // Prevent signal reads when we're updating the graph
    const prev = inNotificationPhase;
    inNotificationPhase = true;
    try {
        for (const consumer of node.liveConsumerNode) {
            if (!consumer.dirty) {
                consumerMarkDirty(consumer);
            }
        }
    }
    finally {
        inNotificationPhase = prev;
    }
}
/**
 * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,
 * based on the current consumer context.
 */
export function producerUpdatesAllowed() {
    return activeConsumer?.consumerAllowSignalWrites !== false;
}
export function consumerMarkDirty(node) {
    node.dirty = true;
    producerNotifyConsumers(node);
    node.consumerMarkedDirty?.(node);
}
/**
 * Prepare this consumer to run a computation in its reactive context.
 *
 * Must be called by subclasses which represent reactive computations, before those computations
 * begin.
 */
export function consumerBeforeComputation(node) {
    node && (node.nextProducerIndex = 0);
    return setActiveConsumer(node);
}
/**
 * Finalize this consumer's state after a reactive computation has run.
 *
 * Must be called by subclasses which represent reactive computations, after those computations
 * have finished.
 */
export function consumerAfterComputation(node, prevConsumer) {
    setActiveConsumer(prevConsumer);
    if (!node || node.producerNode === undefined || node.producerIndexOfThis === undefined ||
        node.producerLastReadVersion === undefined) {
        return;
    }
    if (consumerIsLive(node)) {
        // For live consumers, we need to remove the producer -> consumer edge for any stale producers
        // which weren't dependencies after the recomputation.
        for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {
            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);
        }
    }
    // Truncate the producer tracking arrays.
    // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but
    // benchmarking has shown that individual pop operations are faster.
    while (node.producerNode.length > node.nextProducerIndex) {
        node.producerNode.pop();
        node.producerLastReadVersion.pop();
        node.producerIndexOfThis.pop();
    }
}
/**
 * Determine whether this consumer has any dependencies which have changed since the last time
 * they were read.
 */
export function consumerPollProducersForChange(node) {
    assertConsumerNode(node);
    // Poll producers for change.
    for (let i = 0; i < node.producerNode.length; i++) {
        const producer = node.producerNode[i];
        const seenVersion = node.producerLastReadVersion[i];
        // First check the versions. A mismatch means that the producer's value is known to have
        // changed since the last time we read it.
        if (seenVersion !== producer.version) {
            return true;
        }
        // The producer's version is the same as the last time we read it, but it might itself be
        // stale. Force the producer to recompute its version (calculating a new value if necessary).
        producerUpdateValueVersion(producer);
        // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the
        // versions still match then it has not changed since the last time we read it.
        if (seenVersion !== producer.version) {
            return true;
        }
    }
    return false;
}
/**
 * Disconnect this consumer from the graph.
 */
export function consumerDestroy(node) {
    assertConsumerNode(node);
    if (consumerIsLive(node)) {
        // Drop all connections from the graph to this node.
        for (let i = 0; i < node.producerNode.length; i++) {
            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);
        }
    }
    // Truncate all the arrays to drop all connection from this node to the graph.
    node.producerNode.length = node.producerLastReadVersion.length = node.producerIndexOfThis.length =
        0;
    if (node.liveConsumerNode) {
        node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;
    }
}
/**
 * Add `consumer` as a live consumer of this node.
 *
 * Note that this operation is potentially transitive. If this node becomes live, then it becomes
 * a live consumer of all of its current producers.
 */
function producerAddLiveConsumer(node, consumer, indexOfThis) {
    assertProducerNode(node);
    assertConsumerNode(node);
    if (node.liveConsumerNode.length === 0) {
        // When going from 0 to 1 live consumers, we become a live consumer to our producers.
        for (let i = 0; i < node.producerNode.length; i++) {
            node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);
        }
    }
    node.liveConsumerIndexOfThis.push(indexOfThis);
    return node.liveConsumerNode.push(consumer) - 1;
}
/**
 * Remove the live consumer at `idx`.
 */
function producerRemoveLiveConsumerAtIndex(node, idx) {
    assertProducerNode(node);
    assertConsumerNode(node);
    if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {
        throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);
    }
    if (node.liveConsumerNode.length === 1) {
        // When removing the last live consumer, we will no longer be live. We need to remove
        // ourselves from our producers' tracking (which may cause consumer-producers to lose
        // liveness as well).
        for (let i = 0; i < node.producerNode.length; i++) {
            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);
        }
    }
    // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single
    // live consumer, this is a no-op.
    const lastIdx = node.liveConsumerNode.length - 1;
    node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];
    node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];
    // Truncate the array.
    node.liveConsumerNode.length--;
    node.liveConsumerIndexOfThis.length--;
    // If the index is still valid, then we need to fix the index pointer from the producer to this
    // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).
    if (idx < node.liveConsumerNode.length) {
        const idxProducer = node.liveConsumerIndexOfThis[idx];
        const consumer = node.liveConsumerNode[idx];
        assertConsumerNode(consumer);
        consumer.producerIndexOfThis[idxProducer] = idx;
    }
}
function consumerIsLive(node) {
    return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;
}
function assertConsumerNode(node) {
    node.producerNode ??= [];
    node.producerIndexOfThis ??= [];
    node.producerLastReadVersion ??= [];
}
function assertProducerNode(node) {
    node.liveConsumerNode ??= [];
    node.liveConsumerIndexOfThis ??= [];
}
//# sourceMappingURL=data:application/json;base64,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