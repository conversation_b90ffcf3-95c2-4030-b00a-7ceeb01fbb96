/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * @module
 * @description
 * Entry point from which you should import all public core APIs.
 */
export * from './metadata';
export * from './version';
export * from './di';
export { createPlatform, assertPlatform, destroyPlatform, getPlatform, PlatformRef, ApplicationRef, provideZoneChangeDetection, createPlatformFactory, NgProbeToken, APP_BOOTSTRAP_LISTENER } from './application_ref';
export { enableProdMode, isDevMode } from './util/is_dev_mode';
export { APP_ID, PACKAGE_ROOT_URL, PLATFORM_INITIALIZER, PLATFORM_ID, ANIMATION_MODULE_TYPE, CSP_NONCE } from './application_tokens';
export { APP_INITIALIZER, ApplicationInitStatus } from './application_init';
export * from './zone';
export * from './render';
export * from './linker';
export * from './linker/ng_module_factory_loader_impl';
export { DebugElement, DebugEventListener, DebugNode, asNativeElements, getDebugNode } from './debug/debug_node';
export { Testability, TestabilityRegistry, setTestabilityGetter } from './testability/testability';
export * from './change_detection';
export * from './platform_core_providers';
export { TRANSLATIONS, TRANSLATIONS_FORMAT, LOCALE_ID, DEFAULT_CURRENCY_CODE, MissingTranslationStrategy } from './i18n/tokens';
export { ApplicationModule } from './application_module';
export { Type } from './interface/type';
export { EventEmitter } from './event_emitter';
export { ErrorHandler } from './error_handler';
export * from './core_private_export';
export * from './core_render3_private_export';
export * from './core_reactivity_export';
export { SecurityContext } from './sanitization/security';
export { Sanitizer } from './sanitization/sanitizer';
export { createNgModule, createNgModuleRef, createEnvironmentInjector } from './render3/ng_module_ref';
export { createComponent, reflectComponentType } from './render3/component';
export { isStandalone } from './render3/definition';
export { afterRender, afterNextRender } from './render3/after_render_hooks';
export { mergeApplicationConfig } from './application_config';
export { makeStateKey, TransferState } from './transfer_state';
export { booleanAttribute, numberAttribute } from './util/coercion';
import { global } from './util/global';
if (typeof ngDevMode !== 'undefined' && ngDevMode) {
    // This helper is to give a reasonable error message to people upgrading to v9 that have not yet
    // installed `@angular/localize` in their app.
    // tslint:disable-next-line: no-toplevel-property-access
    global.$localize = global.$localize || function () {
        throw new Error('It looks like your application or one of its dependencies is using i18n.\n' +
            'Angular 9 introduced a global `$localize()` function that needs to be loaded.\n' +
            'Please run `ng add @angular/localize` from the Angular CLI.\n' +
            '(For non-CLI projects, add `import \'@angular/localize/init\';` to your `polyfills.ts` file.\n' +
            'For server-side rendering applications add the import to your `main.server.ts` file.)');
    };
}
//# sourceMappingURL=data:application/json;base64,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