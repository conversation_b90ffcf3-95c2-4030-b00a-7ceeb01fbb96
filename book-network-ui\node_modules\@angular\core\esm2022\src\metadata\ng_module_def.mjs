/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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