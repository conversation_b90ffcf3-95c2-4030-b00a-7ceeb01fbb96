/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertEqual, assertGreaterThan, assertGreaterThanOrEqual, throwError } from '../../util/assert';
import { assertTIcu, assertTNode } from '../assert';
import { createTNodeAtIndex } from '../instructions/shared';
import { assertTNodeType } from '../node_assert';
import { setI18nHandling } from '../node_manipulation';
import { getInsertInFrontOfRNodeWithI18n, processI18nInsertBefore } from '../node_manipulation_i18n';
import { addTNodeAndUpdateInsertBeforeIndex } from './i18n_insert_before_index';
/**
 * Retrieve `TIcu` at a given `index`.
 *
 * The `TIcu` can be stored either directly (if it is nested ICU) OR
 * it is stored inside tho `TIcuContainer` if it is top level ICU.
 *
 * The reason for this is that the top level ICU need a `TNode` so that they are part of the render
 * tree, but nested ICU's have no TNode, because we don't know ahead of time if the nested ICU is
 * expressed (parent ICU may have selected a case which does not contain it.)
 *
 * @param tView Current `TView`.
 * @param index Index where the value should be read from.
 */
export function getTIcu(tView, index) {
    const value = tView.data[index];
    if (value === null || typeof value === 'string')
        return null;
    if (ngDevMode &&
        !(value.hasOwnProperty('tView') || value.hasOwnProperty('currentCaseLViewIndex'))) {
        throwError('We expect to get \'null\'|\'TIcu\'|\'TIcuContainer\', but got: ' + value);
    }
    // Here the `value.hasOwnProperty('currentCaseLViewIndex')` is a polymorphic read as it can be
    // either TIcu or TIcuContainerNode. This is not ideal, but we still think it is OK because it
    // will be just two cases which fits into the browser inline cache (inline cache can take up to
    // 4)
    const tIcu = value.hasOwnProperty('currentCaseLViewIndex') ? value :
        value.value;
    ngDevMode && assertTIcu(tIcu);
    return tIcu;
}
/**
 * Store `TIcu` at a give `index`.
 *
 * The `TIcu` can be stored either directly (if it is nested ICU) OR
 * it is stored inside tho `TIcuContainer` if it is top level ICU.
 *
 * The reason for this is that the top level ICU need a `TNode` so that they are part of the render
 * tree, but nested ICU's have no TNode, because we don't know ahead of time if the nested ICU is
 * expressed (parent ICU may have selected a case which does not contain it.)
 *
 * @param tView Current `TView`.
 * @param index Index where the value should be stored at in `Tview.data`
 * @param tIcu The TIcu to store.
 */
export function setTIcu(tView, index, tIcu) {
    const tNode = tView.data[index];
    ngDevMode &&
        assertEqual(tNode === null || tNode.hasOwnProperty('tView'), true, 'We expect to get \'null\'|\'TIcuContainer\'');
    if (tNode === null) {
        tView.data[index] = tIcu;
    }
    else {
        ngDevMode && assertTNodeType(tNode, 32 /* TNodeType.Icu */);
        tNode.value = tIcu;
    }
}
/**
 * Set `TNode.insertBeforeIndex` taking the `Array` into account.
 *
 * See `TNode.insertBeforeIndex`
 */
export function setTNodeInsertBeforeIndex(tNode, index) {
    ngDevMode && assertTNode(tNode);
    let insertBeforeIndex = tNode.insertBeforeIndex;
    if (insertBeforeIndex === null) {
        setI18nHandling(getInsertInFrontOfRNodeWithI18n, processI18nInsertBefore);
        insertBeforeIndex = tNode.insertBeforeIndex =
            [null /* may be updated to number later */, index];
    }
    else {
        assertEqual(Array.isArray(insertBeforeIndex), true, 'Expecting array here');
        insertBeforeIndex.push(index);
    }
}
/**
 * Create `TNode.type=TNodeType.Placeholder` node.
 *
 * See `TNodeType.Placeholder` for more information.
 */
export function createTNodePlaceholder(tView, previousTNodes, index) {
    const tNode = createTNodeAtIndex(tView, index, 64 /* TNodeType.Placeholder */, null, null);
    addTNodeAndUpdateInsertBeforeIndex(previousTNodes, tNode);
    return tNode;
}
/**
 * Returns current ICU case.
 *
 * ICU cases are stored as index into the `TIcu.cases`.
 * At times it is necessary to communicate that the ICU case just switched and that next ICU update
 * should update all bindings regardless of the mask. In such a case the we store negative numbers
 * for cases which have just been switched. This function removes the negative flag.
 */
export function getCurrentICUCaseIndex(tIcu, lView) {
    const currentCase = lView[tIcu.currentCaseLViewIndex];
    return currentCase === null ? currentCase : (currentCase < 0 ? ~currentCase : currentCase);
}
export function getParentFromIcuCreateOpCode(mergedCode) {
    return mergedCode >>> 17 /* IcuCreateOpCode.SHIFT_PARENT */;
}
export function getRefFromIcuCreateOpCode(mergedCode) {
    return (mergedCode & 131070 /* IcuCreateOpCode.MASK_REF */) >>> 1 /* IcuCreateOpCode.SHIFT_REF */;
}
export function getInstructionFromIcuCreateOpCode(mergedCode) {
    return mergedCode & 1 /* IcuCreateOpCode.MASK_INSTRUCTION */;
}
export function icuCreateOpCode(opCode, parentIdx, refIdx) {
    ngDevMode && assertGreaterThanOrEqual(parentIdx, 0, 'Missing parent index');
    ngDevMode && assertGreaterThan(refIdx, 0, 'Missing ref index');
    return opCode | parentIdx << 17 /* IcuCreateOpCode.SHIFT_PARENT */ | refIdx << 1 /* IcuCreateOpCode.SHIFT_REF */;
}
//# sourceMappingURL=data:application/json;base64,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