/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { ALLOW_MULTIPLE_PLATFORMS as ɵALLOW_MULTIPLE_PLATFORMS, internalCreateApplication as ɵinternalCreateApplication } from './application_ref';
export { ENABLED_SSR_FEATURES as ɵENABLED_SSR_FEATURES } from './application_tokens';
export { defaultIterableDiffers as ɵdefaultIterableDiffers, defaultKeyValueDiffers as ɵdefaultKeyValueDiffers } from './change_detection/change_detection';
export { Console as ɵConsole } from './console';
export { convertToBitFlags as ɵconvertToBitFlags, setCurrentInjector as ɵsetCurrentInjector } from './di/injector_compatibility';
export { getInjectableDef as ɵgetInjectableDef } from './di/interface/defs';
export { isEnvironmentProviders as ɵisEnvironmentProviders } from './di/interface/provider';
export { INJECTOR_SCOPE as ɵINJECTOR_SCOPE } from './di/scope';
export { XSS_SECURITY_URL as ɵXSS_SECURITY_URL } from './error_details_base_url';
export { formatRuntimeError as ɵformatRuntimeError, RuntimeError as ɵRuntimeError } from './errors';
export { annotateForHydration as ɵannotateForHydration } from './hydration/annotate';
export { withDomHydration as ɵwithDomHydration } from './hydration/api';
export { IS_HYDRATION_DOM_REUSE_ENABLED as ɵIS_HYDRATION_DOM_REUSE_ENABLED } from './hydration/tokens';
export { SSR_CONTENT_INTEGRITY_MARKER as ɵSSR_CONTENT_INTEGRITY_MARKER } from './hydration/utils';
export { findLocaleData as ɵfindLocaleData, getLocaleCurrencyCode as ɵgetLocaleCurrencyCode, getLocalePluralCase as ɵgetLocalePluralCase, LocaleDataIndex as ɵLocaleDataIndex, registerLocaleData as ɵregisterLocaleData, unregisterAllLocaleData as ɵunregisterLocaleData } from './i18n/locale_data_api';
export { DEFAULT_LOCALE_ID as ɵDEFAULT_LOCALE_ID } from './i18n/localization';
export { InitialRenderPendingTasks as ɵInitialRenderPendingTasks } from './initial_render_pending_tasks';
export { ComponentFactory as ɵComponentFactory } from './linker/component_factory';
export { clearResolutionOfComponentResourcesQueue as ɵclearResolutionOfComponentResourcesQueue, resolveComponentResources as ɵresolveComponentResources } from './metadata/resource_loading';
export { ReflectionCapabilities as ɵReflectionCapabilities } from './reflection/reflection_capabilities';
export { setInjectorProfilerContext as ɵsetInjectorProfilerContext } from './render3/debug/injector_profiler';
export { allowSanitizationBypassAndThrow as ɵallowSanitizationBypassAndThrow, getSanitizationBypassType as ɵgetSanitizationBypassType, unwrapSafeValue as ɵunwrapSafeValue } from './sanitization/bypass';
export { _sanitizeHtml as ɵ_sanitizeHtml } from './sanitization/html_sanitizer';
export { _sanitizeUrl as ɵ_sanitizeUrl } from './sanitization/url_sanitizer';
export { setAlternateWeakRefImpl as ɵsetAlternateWeakRefImpl } from './signals';
export { TESTABILITY as ɵTESTABILITY, TESTABILITY_GETTER as ɵTESTABILITY_GETTER } from './testability/testability';
export { booleanAttribute, numberAttribute } from './util/coercion';
export { devModeEqual as ɵdevModeEqual } from './util/comparison';
export { global as ɵglobal } from './util/global';
export { isPromise as ɵisPromise, isSubscribable as ɵisSubscribable } from './util/lang';
export { stringify as ɵstringify } from './util/stringify';
export { NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR as ɵNOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR } from './view/provider_flags';
//# sourceMappingURL=data:application/json;base64,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