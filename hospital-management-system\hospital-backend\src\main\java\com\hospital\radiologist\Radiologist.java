package com.hospital.radiologist;

import com.hospital.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "radiologist")
public class Radiologist extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String licenseNumber;
    private String specialization; // Diagnostic Radiology, Interventional Radiology, etc.
    private String department;
    private Integer yearsOfExperience;
    
    @Enumerated(EnumType.STRING)
    private RadiologistStatus status;
    
    // Radiology-specific fields
    private String boardCertifications; // ABR, etc.
    private String subspecialties; // Neuroradiology, Cardiac Imaging, etc.
    private String imagingModalities; // CT, MRI, X-Ray, Ultrasound, etc.
    private Boolean canPerformInterventions;
    private String hospitalAffiliations;
    private Integer averageReportsPerDay;

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum RadiologistStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    SUSPENDED,
    RETIRED
}
