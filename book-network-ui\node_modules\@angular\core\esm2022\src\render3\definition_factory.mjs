/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { stringify } from '../util/stringify';
import { NG_FACTORY_DEF } from './fields';
export function getFactoryDef(type, throwNotFound) {
    const hasFactoryDef = type.hasOwnProperty(NG_FACTORY_DEF);
    if (!hasFactoryDef && throwNotFound === true && ngDevMode) {
        throw new Error(`Type ${stringify(type)} does not have 'ɵfac' property.`);
    }
    return hasFactoryDef ? type[NG_FACTORY_DEF] : null;
}
//# sourceMappingURL=data:application/json;base64,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