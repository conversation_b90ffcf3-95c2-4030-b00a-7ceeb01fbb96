package com.hospital.doctor;

import com.hospital.common.BaseEntity;
import com.hospital.patient.Patient;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "doctor")
public class Doctor extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String specialization;
    private String licenseNumber;
    private String department;
    private Integer yearsOfExperience;
    
    @Enumerated(EnumType.STRING)
    private DoctorStatus status;
    
    @OneToMany(mappedBy = "assignedDoctor")
    private List<Patient> patients;

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum DoctorStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    RETIRED
}
