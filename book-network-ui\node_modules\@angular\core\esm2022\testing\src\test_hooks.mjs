/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Public Test Library for unit testing Angular applications. Assumes that you are running
 * with <PERSON>, <PERSON><PERSON>, or a similar framework which exports a beforeEach function and
 * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.
 */
import { resetFakeAsyncZone } from './fake_async';
import { TestBedImpl } from './test_bed';
// Reset the test providers and the fake async zone before each test.
// We keep a guard because somehow this file can make it into a bundle and be executed
// beforeEach is only defined when executing the tests
globalThis.beforeEach?.(getCleanupHook(false));
// We provide both a `beforeEach` and `afterEach`, because the updated behavior for
// tearing down the module is supposed to run after the test so that we can associate
// teardown errors with the correct test.
// We keep a guard because somehow this file can make it into a bundle and be executed
// afterEach is only defined when executing the tests
globalThis.afterEach?.(getCleanupHook(true));
function getCleanupHook(expectedTeardownValue) {
    return () => {
        const testBed = TestBedImpl.INSTANCE;
        if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {
            testBed.resetTestingModule();
            resetFakeAsyncZone();
        }
    };
}
/**
 * This API should be removed. But doing so seems to break `google3` and so it requires a bit of
 * investigation.
 *
 * A work around is to mark it as `@codeGenApi` for now and investigate later.
 *
 * @codeGenApi
 */
// TODO(iminar): Remove this code in a safe way.
export const __core_private_testing_placeholder__ = '';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVzdF9ob29rcy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvdGVzdGluZy9zcmMvdGVzdF9ob29rcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSDs7OztHQUlHO0FBRUgsT0FBTyxFQUFDLGtCQUFrQixFQUFDLE1BQU0sY0FBYyxDQUFDO0FBQ2hELE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxZQUFZLENBQUM7QUFFdkMscUVBQXFFO0FBQ3JFLHNGQUFzRjtBQUN0RixzREFBc0Q7QUFDdEQsVUFBVSxDQUFDLFVBQVUsRUFBRSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO0FBRS9DLG1GQUFtRjtBQUNuRixxRkFBcUY7QUFDckYseUNBQXlDO0FBQ3pDLHNGQUFzRjtBQUN0RixxREFBcUQ7QUFDckQsVUFBVSxDQUFDLFNBQVMsRUFBRSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO0FBRTdDLFNBQVMsY0FBYyxDQUFDLHFCQUE4QjtJQUNwRCxPQUFPLEdBQUcsRUFBRTtRQUNWLE1BQU0sT0FBTyxHQUFHLFdBQVcsQ0FBQyxRQUFRLENBQUM7UUFDckMsSUFBSSxPQUFPLENBQUMsMkJBQTJCLEVBQUUsS0FBSyxxQkFBcUIsRUFBRTtZQUNuRSxPQUFPLENBQUMsa0JBQWtCLEVBQUUsQ0FBQztZQUM3QixrQkFBa0IsRUFBRSxDQUFDO1NBQ3RCO0lBQ0gsQ0FBQyxDQUFDO0FBQ0osQ0FBQztBQUVEOzs7Ozs7O0dBT0c7QUFDSCxnREFBZ0Q7QUFDaEQsTUFBTSxDQUFDLE1BQU0sb0NBQW9DLEdBQUcsRUFBRSxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8qKlxuICogUHVibGljIFRlc3QgTGlicmFyeSBmb3IgdW5pdCB0ZXN0aW5nIEFuZ3VsYXIgYXBwbGljYXRpb25zLiBBc3N1bWVzIHRoYXQgeW91IGFyZSBydW5uaW5nXG4gKiB3aXRoIEphc21pbmUsIE1vY2hhLCBvciBhIHNpbWlsYXIgZnJhbWV3b3JrIHdoaWNoIGV4cG9ydHMgYSBiZWZvcmVFYWNoIGZ1bmN0aW9uIGFuZFxuICogYWxsb3dzIHRlc3RzIHRvIGJlIGFzeW5jaHJvbm91cyBieSBlaXRoZXIgcmV0dXJuaW5nIGEgcHJvbWlzZSBvciB1c2luZyBhICdkb25lJyBwYXJhbWV0ZXIuXG4gKi9cblxuaW1wb3J0IHtyZXNldEZha2VBc3luY1pvbmV9IGZyb20gJy4vZmFrZV9hc3luYyc7XG5pbXBvcnQge1Rlc3RCZWRJbXBsfSBmcm9tICcuL3Rlc3RfYmVkJztcblxuLy8gUmVzZXQgdGhlIHRlc3QgcHJvdmlkZXJzIGFuZCB0aGUgZmFrZSBhc3luYyB6b25lIGJlZm9yZSBlYWNoIHRlc3QuXG4vLyBXZSBrZWVwIGEgZ3VhcmQgYmVjYXVzZSBzb21laG93IHRoaXMgZmlsZSBjYW4gbWFrZSBpdCBpbnRvIGEgYnVuZGxlIGFuZCBiZSBleGVjdXRlZFxuLy8gYmVmb3JlRWFjaCBpcyBvbmx5IGRlZmluZWQgd2hlbiBleGVjdXRpbmcgdGhlIHRlc3RzXG5nbG9iYWxUaGlzLmJlZm9yZUVhY2g/LihnZXRDbGVhbnVwSG9vayhmYWxzZSkpO1xuXG4vLyBXZSBwcm92aWRlIGJvdGggYSBgYmVmb3JlRWFjaGAgYW5kIGBhZnRlckVhY2hgLCBiZWNhdXNlIHRoZSB1cGRhdGVkIGJlaGF2aW9yIGZvclxuLy8gdGVhcmluZyBkb3duIHRoZSBtb2R1bGUgaXMgc3VwcG9zZWQgdG8gcnVuIGFmdGVyIHRoZSB0ZXN0IHNvIHRoYXQgd2UgY2FuIGFzc29jaWF0ZVxuLy8gdGVhcmRvd24gZXJyb3JzIHdpdGggdGhlIGNvcnJlY3QgdGVzdC5cbi8vIFdlIGtlZXAgYSBndWFyZCBiZWNhdXNlIHNvbWVob3cgdGhpcyBmaWxlIGNhbiBtYWtlIGl0IGludG8gYSBidW5kbGUgYW5kIGJlIGV4ZWN1dGVkXG4vLyBhZnRlckVhY2ggaXMgb25seSBkZWZpbmVkIHdoZW4gZXhlY3V0aW5nIHRoZSB0ZXN0c1xuZ2xvYmFsVGhpcy5hZnRlckVhY2g/LihnZXRDbGVhbnVwSG9vayh0cnVlKSk7XG5cbmZ1bmN0aW9uIGdldENsZWFudXBIb29rKGV4cGVjdGVkVGVhcmRvd25WYWx1ZTogYm9vbGVhbikge1xuICByZXR1cm4gKCkgPT4ge1xuICAgIGNvbnN0IHRlc3RCZWQgPSBUZXN0QmVkSW1wbC5JTlNUQU5DRTtcbiAgICBpZiAodGVzdEJlZC5zaG91bGRUZWFyRG93blRlc3RpbmdNb2R1bGUoKSA9PT0gZXhwZWN0ZWRUZWFyZG93blZhbHVlKSB7XG4gICAgICB0ZXN0QmVkLnJlc2V0VGVzdGluZ01vZHVsZSgpO1xuICAgICAgcmVzZXRGYWtlQXN5bmNab25lKCk7XG4gICAgfVxuICB9O1xufVxuXG4vKipcbiAqIFRoaXMgQVBJIHNob3VsZCBiZSByZW1vdmVkLiBCdXQgZG9pbmcgc28gc2VlbXMgdG8gYnJlYWsgYGdvb2dsZTNgIGFuZCBzbyBpdCByZXF1aXJlcyBhIGJpdCBvZlxuICogaW52ZXN0aWdhdGlvbi5cbiAqXG4gKiBBIHdvcmsgYXJvdW5kIGlzIHRvIG1hcmsgaXQgYXMgYEBjb2RlR2VuQXBpYCBmb3Igbm93IGFuZCBpbnZlc3RpZ2F0ZSBsYXRlci5cbiAqXG4gKiBAY29kZUdlbkFwaVxuICovXG4vLyBUT0RPKGltaW5hcik6IFJlbW92ZSB0aGlzIGNvZGUgaW4gYSBzYWZlIHdheS5cbmV4cG9ydCBjb25zdCBfX2NvcmVfcHJpdmF0ZV90ZXN0aW5nX3BsYWNlaG9sZGVyX18gPSAnJztcbiJdfQ==