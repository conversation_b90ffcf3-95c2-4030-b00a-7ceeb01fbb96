/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ChangeDetectionStrategy } from '../change_detection/constants';
import { formatRuntimeError } from '../errors';
import { ViewEncapsulation } from '../metadata/view';
import { noSideEffects } from '../util/closure';
import { EMPTY_ARRAY, EMPTY_OBJ } from '../util/empty';
import { initNgDevMode } from '../util/ng_dev_mode';
import { stringify } from '../util/stringify';
import { NG_COMP_DEF, NG_DIR_DEF, NG_MOD_DEF, NG_PIPE_DEF } from './fields';
import { stringifyCSSSelectorList } from './node_selector_matcher';
/**
 * Create a component definition object.
 *
 *
 * # Example
 * ```
 * class MyComponent {
 *   // Generated by Angular Template Compiler
 *   // [Symbol] syntax will not be supported by TypeScript until v2.7
 *   static ɵcmp = defineComponent({
 *     ...
 *   });
 * }
 * ```
 * @codeGenApi
 */
export function ɵɵdefineComponent(componentDefinition) {
    return noSideEffects(() => {
        // Initialize ngDevMode. This must be the first statement in ɵɵdefineComponent.
        // See the `initNgDevMode` docstring for more information.
        (typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode();
        const baseDef = getNgDirectiveDef(componentDefinition);
        const def = {
            ...baseDef,
            decls: componentDefinition.decls,
            vars: componentDefinition.vars,
            template: componentDefinition.template,
            consts: componentDefinition.consts || null,
            ngContentSelectors: componentDefinition.ngContentSelectors,
            onPush: componentDefinition.changeDetection === ChangeDetectionStrategy.OnPush,
            directiveDefs: null,
            pipeDefs: null,
            dependencies: baseDef.standalone && componentDefinition.dependencies || null,
            getStandaloneInjector: null,
            signals: componentDefinition.signals ?? false,
            data: componentDefinition.data || {},
            encapsulation: componentDefinition.encapsulation || ViewEncapsulation.Emulated,
            styles: componentDefinition.styles || EMPTY_ARRAY,
            _: null,
            schemas: componentDefinition.schemas || null,
            tView: null,
            id: '',
        };
        initFeatures(def);
        const dependencies = componentDefinition.dependencies;
        def.directiveDefs = extractDefListOrFactory(dependencies, /* pipeDef */ false);
        def.pipeDefs = extractDefListOrFactory(dependencies, /* pipeDef */ true);
        def.id = getComponentId(def);
        return def;
    });
}
/**
 * Generated next to NgModules to monkey-patch directive and pipe references onto a component's
 * definition, when generating a direct reference in the component file would otherwise create an
 * import cycle.
 *
 * See [this explanation](https://hackmd.io/Odw80D0pR6yfsOjg_7XCJg?view) for more details.
 *
 * @codeGenApi
 */
export function ɵɵsetComponentScope(type, directives, pipes) {
    const def = type.ɵcmp;
    def.directiveDefs = extractDefListOrFactory(directives, /* pipeDef */ false);
    def.pipeDefs = extractDefListOrFactory(pipes, /* pipeDef */ true);
}
export function extractDirectiveDef(type) {
    return getComponentDef(type) || getDirectiveDef(type);
}
function nonNull(value) {
    return value !== null;
}
/**
 * @codeGenApi
 */
export function ɵɵdefineNgModule(def) {
    return noSideEffects(() => {
        const res = {
            type: def.type,
            bootstrap: def.bootstrap || EMPTY_ARRAY,
            declarations: def.declarations || EMPTY_ARRAY,
            imports: def.imports || EMPTY_ARRAY,
            exports: def.exports || EMPTY_ARRAY,
            transitiveCompileScopes: null,
            schemas: def.schemas || null,
            id: def.id || null,
        };
        return res;
    });
}
/**
 * Adds the module metadata that is necessary to compute the module's transitive scope to an
 * existing module definition.
 *
 * Scope metadata of modules is not used in production builds, so calls to this function can be
 * marked pure to tree-shake it from the bundle, allowing for all referenced declarations
 * to become eligible for tree-shaking as well.
 *
 * @codeGenApi
 */
export function ɵɵsetNgModuleScope(type, scope) {
    return noSideEffects(() => {
        const ngModuleDef = getNgModuleDef(type, true);
        ngModuleDef.declarations = scope.declarations || EMPTY_ARRAY;
        ngModuleDef.imports = scope.imports || EMPTY_ARRAY;
        ngModuleDef.exports = scope.exports || EMPTY_ARRAY;
    });
}
/**
 * Inverts an inputs or outputs lookup such that the keys, which were the
 * minified keys, are part of the values, and the values are parsed so that
 * the publicName of the property is the new key
 *
 * e.g. for
 *
 * ```
 * class Comp {
 *   @Input()
 *   propName1: string;
 *
 *   @Input('publicName2')
 *   declaredPropName2: number;
 * }
 * ```
 *
 * will be serialized as
 *
 * ```
 * {
 *   propName1: 'propName1',
 *   declaredPropName2: ['publicName2', 'declaredPropName2'],
 * }
 * ```
 *
 * which is than translated by the minifier as:
 *
 * ```
 * {
 *   minifiedPropName1: 'propName1',
 *   minifiedPropName2: ['publicName2', 'declaredPropName2'],
 * }
 * ```
 *
 * becomes: (public name => minifiedName)
 *
 * ```
 * {
 *  'propName1': 'minifiedPropName1',
 *  'publicName2': 'minifiedPropName2',
 * }
 * ```
 *
 * Optionally the function can take `secondary` which will result in: (public name => declared name)
 *
 * ```
 * {
 *  'propName1': 'propName1',
 *  'publicName2': 'declaredPropName2',
 * }
 * ```
 *

 */
function invertObject(obj, secondary) {
    if (obj == null)
        return EMPTY_OBJ;
    const newLookup = {};
    for (const minifiedKey in obj) {
        if (obj.hasOwnProperty(minifiedKey)) {
            let publicName = obj[minifiedKey];
            let declaredName = publicName;
            if (Array.isArray(publicName)) {
                declaredName = publicName[1];
                publicName = publicName[0];
            }
            newLookup[publicName] = minifiedKey;
            if (secondary) {
                (secondary[publicName] = declaredName);
            }
        }
    }
    return newLookup;
}
/**
 * Create a directive definition object.
 *
 * # Example
 * ```ts
 * class MyDirective {
 *   // Generated by Angular Template Compiler
 *   // [Symbol] syntax will not be supported by TypeScript until v2.7
 *   static ɵdir = ɵɵdefineDirective({
 *     ...
 *   });
 * }
 * ```
 *
 * @codeGenApi
 */
export function ɵɵdefineDirective(directiveDefinition) {
    return noSideEffects(() => {
        const def = getNgDirectiveDef(directiveDefinition);
        initFeatures(def);
        return def;
    });
}
/**
 * Create a pipe definition object.
 *
 * # Example
 * ```
 * class MyPipe implements PipeTransform {
 *   // Generated by Angular Template Compiler
 *   static ɵpipe = definePipe({
 *     ...
 *   });
 * }
 * ```
 * @param pipeDef Pipe definition generated by the compiler
 *
 * @codeGenApi
 */
export function ɵɵdefinePipe(pipeDef) {
    return {
        type: pipeDef.type,
        name: pipeDef.name,
        factory: null,
        pure: pipeDef.pure !== false,
        standalone: pipeDef.standalone === true,
        onDestroy: pipeDef.type.prototype.ngOnDestroy || null
    };
}
/**
 * The following getter methods retrieve the definition from the type. Currently the retrieval
 * honors inheritance, but in the future we may change the rule to require that definitions are
 * explicit. This would require some sort of migration strategy.
 */
export function getComponentDef(type) {
    return type[NG_COMP_DEF] || null;
}
export function getDirectiveDef(type) {
    return type[NG_DIR_DEF] || null;
}
export function getPipeDef(type) {
    return type[NG_PIPE_DEF] || null;
}
/**
 * Checks whether a given Component, Directive or Pipe is marked as standalone.
 * This will return false if passed anything other than a Component, Directive, or Pipe class
 * See [this guide](/guide/standalone-components) for additional information:
 *
 * @param type A reference to a Component, Directive or Pipe.
 * @publicApi
 */
export function isStandalone(type) {
    const def = getComponentDef(type) || getDirectiveDef(type) || getPipeDef(type);
    return def !== null ? def.standalone : false;
}
export function getNgModuleDef(type, throwNotFound) {
    const ngModuleDef = type[NG_MOD_DEF] || null;
    if (!ngModuleDef && throwNotFound === true) {
        throw new Error(`Type ${stringify(type)} does not have 'ɵmod' property.`);
    }
    return ngModuleDef;
}
function getNgDirectiveDef(directiveDefinition) {
    const declaredInputs = {};
    return {
        type: directiveDefinition.type,
        providersResolver: null,
        factory: null,
        hostBindings: directiveDefinition.hostBindings || null,
        hostVars: directiveDefinition.hostVars || 0,
        hostAttrs: directiveDefinition.hostAttrs || null,
        contentQueries: directiveDefinition.contentQueries || null,
        declaredInputs,
        inputTransforms: null,
        inputConfig: directiveDefinition.inputs || EMPTY_OBJ,
        exportAs: directiveDefinition.exportAs || null,
        standalone: directiveDefinition.standalone === true,
        signals: directiveDefinition.signals === true,
        selectors: directiveDefinition.selectors || EMPTY_ARRAY,
        viewQuery: directiveDefinition.viewQuery || null,
        features: directiveDefinition.features || null,
        setInput: null,
        findHostDirectiveDefs: null,
        hostDirectives: null,
        inputs: invertObject(directiveDefinition.inputs, declaredInputs),
        outputs: invertObject(directiveDefinition.outputs),
    };
}
function initFeatures(definition) {
    definition.features?.forEach((fn) => fn(definition));
}
function extractDefListOrFactory(dependencies, pipeDef) {
    if (!dependencies) {
        return null;
    }
    const defExtractor = pipeDef ? getPipeDef : extractDirectiveDef;
    return () => (typeof dependencies === 'function' ? dependencies() : dependencies)
        .map(dep => defExtractor(dep))
        .filter(nonNull);
}
/**
 * A map that contains the generated component IDs and type.
 */
export const GENERATED_COMP_IDS = new Map();
/**
 * A method can returns a component ID from the component definition using a variant of DJB2 hash
 * algorithm.
 */
function getComponentId(componentDef) {
    let hash = 0;
    // We cannot rely solely on the component selector as the same selector can be used in different
    // modules.
    //
    // `componentDef.style` is not used, due to it causing inconsistencies. Ex: when server
    // component styles has no sourcemaps and browsers do.
    //
    // Example:
    // https://github.com/angular/components/blob/d9f82c8f95309e77a6d82fd574c65871e91354c2/src/material/core/option/option.ts#L248
    // https://github.com/angular/components/blob/285f46dc2b4c5b127d356cb7c4714b221f03ce50/src/material/legacy-core/option/option.ts#L32
    const hashSelectors = [
        componentDef.selectors,
        componentDef.ngContentSelectors,
        componentDef.hostVars,
        componentDef.hostAttrs,
        componentDef.consts,
        componentDef.vars,
        componentDef.decls,
        componentDef.encapsulation,
        componentDef.standalone,
        componentDef.signals,
        componentDef.exportAs,
        JSON.stringify(componentDef.inputs),
        JSON.stringify(componentDef.outputs),
        // We cannot use 'componentDef.type.name' as the name of the symbol will change and will not
        // match in the server and browser bundles.
        Object.getOwnPropertyNames(componentDef.type.prototype),
        !!componentDef.contentQueries,
        !!componentDef.viewQuery,
    ].join('|');
    for (const char of hashSelectors) {
        hash = Math.imul(31, hash) + char.charCodeAt(0) << 0;
    }
    // Force positive number hash.
    // 2147483647 = equivalent of Integer.MAX_VALUE.
    hash += 2147483647 + 1;
    const compId = 'c' + hash;
    if (typeof ngDevMode === 'undefined' || ngDevMode) {
        if (GENERATED_COMP_IDS.has(compId)) {
            const previousCompDefType = GENERATED_COMP_IDS.get(compId);
            if (previousCompDefType !== componentDef.type) {
                console.warn(formatRuntimeError(-912 /* RuntimeErrorCode.COMPONENT_ID_COLLISION */, `Component ID generation collision detected. Components '${previousCompDefType.name}' and '${componentDef.type.name}' with selector '${stringifyCSSSelectorList(componentDef
                    .selectors)}' generated the same component ID. To fix this, you can change the selector of one of those components or add an extra host attribute to force a different ID.`));
            }
        }
        else {
            GENERATED_COMP_IDS.set(compId, componentDef.type);
        }
    }
    return compId;
}
//# sourceMappingURL=data:application/json;base64,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