spring:
  datasource:
    url: ****************************************************
    username: hospital_user
    password: hospital_password
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
    database: postgresql
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  mail:
    host: localhost
    port: 1026
    username: hospital
    password: hospital
    properties:
      mail:
        smtp:
          trust: "*"
        auth: true
        starttls:
          enabled: true
        connectiontimeout: 5000
        timeout: 3000
        writetimeout: 5000
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: "http://localhost:9091/realms/hospital-management"
application:
  file:
    uploads:
      photos-output-path: ./uploads
server:
  port: 8088
