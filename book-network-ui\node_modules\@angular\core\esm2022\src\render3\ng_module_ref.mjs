/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createInjectorWithoutInjectorInstances } from '../di/create_injector';
import { getNullInjector, R3Injector } from '../di/r3_injector';
import { ComponentFactoryResolver as viewEngine_ComponentFactoryResolver } from '../linker/component_factory_resolver';
import { NgModuleFactory as viewEngine_NgModuleFactory, NgModuleRef as viewEngine_NgModuleRef } from '../linker/ng_module_factory';
import { assertDefined } from '../util/assert';
import { stringify } from '../util/stringify';
import { ComponentFactoryResolver } from './component_ref';
import { getNgModuleDef } from './definition';
import { maybeUnwrapFn } from './util/misc_utils';
/**
 * Returns a new NgModuleRef instance based on the NgModule class and parent injector provided.
 *
 * @param ngModule NgModule class.
 * @param parentInjector Optional injector instance to use as a parent for the module injector. If
 *     not provided, `NullInjector` will be used instead.
 * @returns NgModuleRef that represents an NgModule instance.
 *
 * @publicApi
 */
export function createNgModule(ngModule, parentInjector) {
    return new NgModuleRef(ngModule, parentInjector ?? null, []);
}
/**
 * The `createNgModule` function alias for backwards-compatibility.
 * Please avoid using it directly and use `createNgModule` instead.
 *
 * @deprecated Use `createNgModule` instead.
 */
export const createNgModuleRef = createNgModule;
export class NgModuleRef extends viewEngine_NgModuleRef {
    constructor(ngModuleType, _parent, additionalProviders) {
        super();
        this._parent = _parent;
        // tslint:disable-next-line:require-internal-with-underscore
        this._bootstrapComponents = [];
        this.destroyCbs = [];
        // When bootstrapping a module we have a dependency graph that looks like this:
        // ApplicationRef -> ComponentFactoryResolver -> NgModuleRef. The problem is that if the
        // module being resolved tries to inject the ComponentFactoryResolver, it'll create a
        // circular dependency which will result in a runtime error, because the injector doesn't
        // exist yet. We work around the issue by creating the ComponentFactoryResolver ourselves
        // and providing it, rather than letting the injector resolve it.
        this.componentFactoryResolver = new ComponentFactoryResolver(this);
        const ngModuleDef = getNgModuleDef(ngModuleType);
        ngDevMode &&
            assertDefined(ngModuleDef, `NgModule '${stringify(ngModuleType)}' is not a subtype of 'NgModuleType'.`);
        this._bootstrapComponents = maybeUnwrapFn(ngModuleDef.bootstrap);
        this._r3Injector = createInjectorWithoutInjectorInstances(ngModuleType, _parent, [
            { provide: viewEngine_NgModuleRef, useValue: this }, {
                provide: viewEngine_ComponentFactoryResolver,
                useValue: this.componentFactoryResolver
            },
            ...additionalProviders
        ], stringify(ngModuleType), new Set(['environment']));
        // We need to resolve the injector types separately from the injector creation, because
        // the module might be trying to use this ref in its constructor for DI which will cause a
        // circular error that will eventually error out, because the injector isn't created yet.
        this._r3Injector.resolveInjectorInitializers();
        this.instance = this._r3Injector.get(ngModuleType);
    }
    get injector() {
        return this._r3Injector;
    }
    destroy() {
        ngDevMode && assertDefined(this.destroyCbs, 'NgModule already destroyed');
        const injector = this._r3Injector;
        !injector.destroyed && injector.destroy();
        this.destroyCbs.forEach(fn => fn());
        this.destroyCbs = null;
    }
    onDestroy(callback) {
        ngDevMode && assertDefined(this.destroyCbs, 'NgModule already destroyed');
        this.destroyCbs.push(callback);
    }
}
export class NgModuleFactory extends viewEngine_NgModuleFactory {
    constructor(moduleType) {
        super();
        this.moduleType = moduleType;
    }
    create(parentInjector) {
        return new NgModuleRef(this.moduleType, parentInjector, []);
    }
}
export function createNgModuleRefWithProviders(moduleType, parentInjector, additionalProviders) {
    return new NgModuleRef(moduleType, parentInjector, additionalProviders);
}
export class EnvironmentNgModuleRefAdapter extends viewEngine_NgModuleRef {
    constructor(config) {
        super();
        this.componentFactoryResolver = new ComponentFactoryResolver(this);
        this.instance = null;
        const injector = new R3Injector([
            ...config.providers,
            { provide: viewEngine_NgModuleRef, useValue: this },
            { provide: viewEngine_ComponentFactoryResolver, useValue: this.componentFactoryResolver },
        ], config.parent || getNullInjector(), config.debugName, new Set(['environment']));
        this.injector = injector;
        if (config.runEnvironmentInitializers) {
            injector.resolveInjectorInitializers();
        }
    }
    destroy() {
        this.injector.destroy();
    }
    onDestroy(callback) {
        this.injector.onDestroy(callback);
    }
}
/**
 * Create a new environment injector.
 *
 * Learn more about environment injectors in
 * [this guide](guide/standalone-components#environment-injectors).
 *
 * @param providers An array of providers.
 * @param parent A parent environment injector.
 * @param debugName An optional name for this injector instance, which will be used in error
 *     messages.
 *
 * @publicApi
 */
export function createEnvironmentInjector(providers, parent, debugName = null) {
    const adapter = new EnvironmentNgModuleRefAdapter({ providers, parent, debugName, runEnvironmentInitializers: true });
    return adapter.injector;
}
//# sourceMappingURL=data:application/json;base64,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