package com.hospital.labtechnician;

import com.hospital.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "lab_technician")
public class LabTechnician extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String licenseNumber;
    private String specialization; // Hematology, Microbiology, Chemistry, etc.
    private String department; // Clinical Lab, Pathology, Blood Bank, etc.
    private Integer yearsOfExperience;
    private String shift; // DAY, NIGHT, ROTATING
    
    @Enumerated(EnumType.STRING)
    private LabTechnicianStatus status;
    
    // Lab-specific fields
    private String certifications; // ASCP, AMT, etc.
    private String labSection; // Chemistry, Hematology, Microbiology, etc.
    private Boolean canPerformComplexTests;
    private String equipmentCertifications; // Specific equipment they're certified on
    private String supervisorId; // Reference to supervising technologist

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum LabTechnicianStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    SUSPENDED,
    RETIRED
}
