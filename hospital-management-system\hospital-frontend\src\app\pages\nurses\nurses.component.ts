import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-nurses',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-nurse me-2"></i>Nurse Management</h2>
        <button class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add New Nurse
        </button>
      </div>
      
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Department</th>
                  <th>Shift</th>
                  <th>Experience</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>N001</td>
                  <td><PERSON></td>
                  <td>Emergency</td>
                  <td>Day</td>
                  <td>8 years</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <!-- More nurse rows would be loaded dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class NursesComponent {}
