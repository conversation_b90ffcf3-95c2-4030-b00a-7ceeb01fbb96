/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { noSideEffects } from './closure';
export const ANNOTATIONS = '__annotations__';
export const PARAMETERS = '__parameters__';
export const PROP_METADATA = '__prop__metadata__';
/**
 * @suppress {globalThis}
 */
export function makeDecorator(name, props, parentClass, additionalProcessing, typeFn) {
    return noSideEffects(() => {
        const metaCtor = makeMetadataCtor(props);
        function DecoratorFactory(...args) {
            if (this instanceof DecoratorFactory) {
                metaCtor.call(this, ...args);
                return this;
            }
            const annotationInstance = new DecoratorFactory(...args);
            return function TypeDecorator(cls) {
                if (typeFn)
                    typeFn(cls, ...args);
                // Use of Object.defineProperty is important since it creates non-enumerable property which
                // prevents the property is copied during subclassing.
                const annotations = cls.hasOwnProperty(ANNOTATIONS) ?
                    cls[ANNOTATIONS] :
                    Object.defineProperty(cls, ANNOTATIONS, { value: [] })[ANNOTATIONS];
                annotations.push(annotationInstance);
                if (additionalProcessing)
                    additionalProcessing(cls);
                return cls;
            };
        }
        if (parentClass) {
            DecoratorFactory.prototype = Object.create(parentClass.prototype);
        }
        DecoratorFactory.prototype.ngMetadataName = name;
        DecoratorFactory.annotationCls = DecoratorFactory;
        return DecoratorFactory;
    });
}
function makeMetadataCtor(props) {
    return function ctor(...args) {
        if (props) {
            const values = props(...args);
            for (const propName in values) {
                this[propName] = values[propName];
            }
        }
    };
}
export function makeParamDecorator(name, props, parentClass) {
    return noSideEffects(() => {
        const metaCtor = makeMetadataCtor(props);
        function ParamDecoratorFactory(...args) {
            if (this instanceof ParamDecoratorFactory) {
                metaCtor.apply(this, args);
                return this;
            }
            const annotationInstance = new ParamDecoratorFactory(...args);
            ParamDecorator.annotation = annotationInstance;
            return ParamDecorator;
            function ParamDecorator(cls, unusedKey, index) {
                // Use of Object.defineProperty is important since it creates non-enumerable property which
                // prevents the property is copied during subclassing.
                const parameters = cls.hasOwnProperty(PARAMETERS) ?
                    cls[PARAMETERS] :
                    Object.defineProperty(cls, PARAMETERS, { value: [] })[PARAMETERS];
                // there might be gaps if some in between parameters do not have annotations.
                // we pad with nulls.
                while (parameters.length <= index) {
                    parameters.push(null);
                }
                (parameters[index] = parameters[index] || []).push(annotationInstance);
                return cls;
            }
        }
        if (parentClass) {
            ParamDecoratorFactory.prototype = Object.create(parentClass.prototype);
        }
        ParamDecoratorFactory.prototype.ngMetadataName = name;
        ParamDecoratorFactory.annotationCls = ParamDecoratorFactory;
        return ParamDecoratorFactory;
    });
}
export function makePropDecorator(name, props, parentClass, additionalProcessing) {
    return noSideEffects(() => {
        const metaCtor = makeMetadataCtor(props);
        function PropDecoratorFactory(...args) {
            if (this instanceof PropDecoratorFactory) {
                metaCtor.apply(this, args);
                return this;
            }
            const decoratorInstance = new PropDecoratorFactory(...args);
            function PropDecorator(target, name) {
                // target is undefined with standard decorators. This case is not supported and will throw
                // if this decorator is used in JIT mode with standard decorators.
                if (target === undefined) {
                    throw new Error('Standard Angular field decorators are not supported in JIT mode.');
                }
                const constructor = target.constructor;
                // Use of Object.defineProperty is important because it creates a non-enumerable property
                // which prevents the property from being copied during subclassing.
                const meta = constructor.hasOwnProperty(PROP_METADATA) ?
                    constructor[PROP_METADATA] :
                    Object.defineProperty(constructor, PROP_METADATA, { value: {} })[PROP_METADATA];
                meta[name] = meta.hasOwnProperty(name) && meta[name] || [];
                meta[name].unshift(decoratorInstance);
                if (additionalProcessing)
                    additionalProcessing(target, name, ...args);
            }
            return PropDecorator;
        }
        if (parentClass) {
            PropDecoratorFactory.prototype = Object.create(parentClass.prototype);
        }
        PropDecoratorFactory.prototype.ngMetadataName = name;
        PropDecoratorFactory.annotationCls = PropDecoratorFactory;
        return PropDecoratorFactory;
    });
}
//# sourceMappingURL=data:application/json;base64,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