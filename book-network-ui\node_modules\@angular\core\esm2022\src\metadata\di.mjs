/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { makePropDecorator } from '../util/decorators';
// Stores the default value of `emitDistinctChangesOnly` when the `emitDistinctChangesOnly` is not
// explicitly set.
export const emitDistinctChangesOnlyDefaultValue = true;
/**
 * Base class for query metadata.
 *
 * @see {@link ContentChildren}
 * @see {@link ContentChild}
 * @see {@link ViewChildren}
 * @see {@link ViewChild}
 *
 * @publicApi
 */
export class Query {
}
/**
 * ContentChildren decorator and metadata.
 *
 *
 * @Annotation
 * @publicApi
 */
export const ContentChildren = makePropDecorator('ContentChildren', (selector, data = {}) => ({
    selector,
    first: false,
    isViewQuery: false,
    descendants: false,
    emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,
    ...data
}), Query);
/**
 * ContentChild decorator and metadata.
 *
 *
 * @Annotation
 *
 * @publicApi
 */
export const ContentChild = makePropDecorator('ContentChild', (selector, data = {}) => ({ selector, first: true, isViewQuery: false, descendants: true, ...data }), Query);
/**
 * ViewChildren decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const ViewChildren = makePropDecorator('ViewChildren', (selector, data = {}) => ({
    selector,
    first: false,
    isViewQuery: true,
    descendants: true,
    emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,
    ...data
}), Query);
/**
 * ViewChild decorator and metadata.
 *
 * @Annotation
 * @publicApi
 */
export const ViewChild = makePropDecorator('ViewChild', (selector, data) => ({ selector, first: true, isViewQuery: true, descendants: true, ...data }), Query);
//# sourceMappingURL=data:application/json;base64,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