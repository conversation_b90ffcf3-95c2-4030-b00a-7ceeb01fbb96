"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2022 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){const t=e.performance;function n(e){t&&t.mark&&t.mark(e)}function o(e,n){t&&t.measure&&t.measure(e,n)}n("Zone");const r=e.__Zone_symbol_prefix||"__zone_symbol__";function s(e){return r+e}const a=!0===e[s("forceDuplicateZoneCheck")];if(e.Zone){if(a||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}class i{static{this.__symbol__=s}static assertZonePatched(){if(e.Promise!==N.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=i.current;for(;e.parent;)e=e.parent;return e}static get current(){return R.zone}static get currentTask(){return M}static __load_patch(t,r,s=!1){if(N.hasOwnProperty(t)){if(!s&&a)throw Error("Already loaded patch: "+t)}else if(!e["__Zone_disable_"+t]){const s="Zone:"+t;n(s),N[t]=r(e,i,z),o(s,s)}}get parent(){return this._parent}get name(){return this._name}constructor(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new l(this,this._parent&&this._parent._zoneDelegate,t)}get(e){const t=this.getZoneWith(e);if(t)return t._properties[e]}getZoneWith(e){let t=this;for(;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const n=this._zoneDelegate.intercept(this,e,t),o=this;return function(){return o.runGuarded(n,this,arguments,t)}}run(e,t,n,o){R={parent:R,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,o)}finally{R=R.parent}}runGuarded(e,t=null,n,o){R={parent:R,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,o)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{R=R.parent}}runTask(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");if(e.state===m&&(e.type===D||e.type===Z))return;const o=e.state!=v;o&&e._transitionTo(v,S),e.runCount++;const r=M;M=e,R={parent:R,zone:this};try{e.type==Z&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{e.state!==m&&e.state!==w&&(e.type==D||e.data&&e.data.isPeriodic?o&&e._transitionTo(S,v):(e.runCount=0,this._updateTaskCount(e,-1),o&&e._transitionTo(m,v,m))),R=R.parent,M=r}}scheduleTask(e){if(e.zone&&e.zone!==this){let t=this;for(;t;){if(t===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);t=t.parent}}e._transitionTo(b,m);const t=[];e._zoneDelegates=t,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(w,b,m),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===t&&this._updateTaskCount(e,1),e.state==b&&e._transitionTo(S,b),e}scheduleMicroTask(e,t,n,o){return this.scheduleTask(new h(P,e,t,n,o,void 0))}scheduleMacroTask(e,t,n,o,r){return this.scheduleTask(new h(Z,e,t,n,o,r))}scheduleEventTask(e,t,n,o,r){return this.scheduleTask(new h(D,e,t,n,o,r))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");if(e.state===S||e.state===v){e._transitionTo(O,S,v);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(w,O),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(m,O),e.runCount=0,e}}_updateTaskCount(e,t){const n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(let o=0;o<n.length;o++)n[o]._updateTaskCount(e.type,t)}}const c={name:"",onHasTask:(e,t,n,o)=>e.hasTask(n,o),onScheduleTask:(e,t,n,o)=>e.scheduleTask(n,o),onInvokeTask:(e,t,n,o,r,s)=>e.invokeTask(n,o,r,s),onCancelTask:(e,t,n,o)=>e.cancelTask(n,o)};class l{constructor(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this.zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const o=n&&n.onHasTask;(o||t&&t._hasTaskZS)&&(this._hasTaskZS=o?n:c,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=e,n.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),n.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),n.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}fork(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new i(e,t)}intercept(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t}invoke(e,t,n,o,r){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,o,r):t.apply(n,o)}handleError(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)}scheduleTask(e,t){let n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t),n||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=P)throw new Error("Task is missing scheduleFn.");y(t)}return n}invokeTask(e,t,n,o){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,o):t.callback.apply(n,o)}cancelTask(e,t){let n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n}hasTask(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}}_updateTaskCount(e,t){const n=this._taskCounts,o=n[e],r=n[e]=o+t;if(r<0)throw new Error("More tasks executed then were scheduled.");0!=o&&0!=r||this.hasTask(this.zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})}}class h{constructor(t,n,o,r,s,a){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=t,this.source=n,this.data=r,this.scheduleFn=s,this.cancelFn=a,!o)throw new Error("callback is not defined");this.callback=o;const i=this;this.invoke=t===D&&r&&r.useG?h.invokeTask:function(){return h.invokeTask.call(e,i,this,arguments)}}static invokeTask(e,t,n){e||(e=this),I++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==I&&T(),I--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(m,b)}_transitionTo(e,t,n){if(this._state!==t&&this._state!==n)throw new Error(`${this.type} '${this.source}': can not transition to '${e}', expecting state '${t}'${n?" or '"+n+"'":""}, was '${this._state}'.`);this._state=e,e==m&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const u=s("setTimeout"),p=s("Promise"),f=s("then");let _,d=[],E=!1;function g(t){if(_||e[p]&&(_=e[p].resolve(0)),_){let e=_[f];e||(e=_.then),e.call(_,t)}else e[u](t,0)}function y(e){0===I&&0===d.length&&g(T),e&&d.push(e)}function T(){if(!E){for(E=!0;d.length;){const e=d;d=[];for(let t=0;t<e.length;t++){const n=e[t];try{n.zone.runTask(n,null,null)}catch(e){z.onUnhandledError(e)}}}z.microtaskDrainDone(),E=!1}}const k={name:"NO ZONE"},m="notScheduled",b="scheduling",S="scheduled",v="running",O="canceling",w="unknown",P="microTask",Z="macroTask",D="eventTask",N={},z={symbol:s,currentZoneFrame:()=>R,onUnhandledError:C,microtaskDrainDone:C,scheduleMicroTask:y,showUncaughtError:()=>!i[s("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:C,patchMethod:()=>C,bindArguments:()=>[],patchThen:()=>C,patchMacroTask:()=>C,patchEventPrototype:()=>C,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>C,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>C,wrapWithCurrentZone:()=>C,filterProperties:()=>[],attachOriginToPatched:()=>C,_redefineProperty:()=>C,patchCallbacks:()=>C,nativeScheduleMicroTask:g};let R={parent:null,zone:new i(null,null)},M=null,I=0;function C(){}o("Zone","Zone"),e.Zone=i}("undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global);const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,ObjectGetPrototypeOf=Object.getPrototypeOf,ObjectCreate=Object.create,ArraySlice=Array.prototype.slice,ADD_EVENT_LISTENER_STR="addEventListener",REMOVE_EVENT_LISTENER_STR="removeEventListener",ZONE_SYMBOL_ADD_EVENT_LISTENER=Zone.__symbol__("addEventListener"),ZONE_SYMBOL_REMOVE_EVENT_LISTENER=Zone.__symbol__("removeEventListener"),TRUE_STR="true",FALSE_STR="false",ZONE_SYMBOL_PREFIX=Zone.__symbol__("");function wrapWithCurrentZone(e,t){return Zone.current.wrap(e,t)}function scheduleMacroTaskWithCurrentZone(e,t,n,o,r){return Zone.current.scheduleMacroTask(e,t,n,o,r)}const zoneSymbol=Zone.__symbol__,isWindowExists="undefined"!=typeof window,internalWindow=isWindowExists?window:void 0,_global=isWindowExists&&internalWindow||"object"==typeof self&&self||global,REMOVE_ATTRIBUTE="removeAttribute";function bindArguments(e,t){for(let n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=wrapWithCurrentZone(e[n],t+"_"+n));return e}function patchPrototype(e,t){const n=e.constructor.name;for(let o=0;o<t.length;o++){const r=t[o],s=e[r];if(s){if(!isPropertyWritable(ObjectGetOwnPropertyDescriptor(e,r)))continue;e[r]=(e=>{const t=function(){return e.apply(this,bindArguments(arguments,n+"."+r))};return attachOriginToPatched(t,e),t})(s)}}}function isPropertyWritable(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const isWebWorker="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,isNode=!("nw"in _global)&&void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process),isBrowser=!isNode&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),isMix=void 0!==_global.process&&"[object process]"==={}.toString.call(_global.process)&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),zoneSymbolEventNames$1={},wrapFn=function(e){if(!(e=e||_global.event))return;let t=zoneSymbolEventNames$1[e.type];t||(t=zoneSymbolEventNames$1[e.type]=zoneSymbol("ON_PROPERTY"+e.type));const n=this||e.target||_global,o=n[t];let r;return isBrowser&&n===internalWindow&&"error"===e.type?(r=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===r&&e.preventDefault()):(r=o&&o.apply(this,arguments),null==r||r||e.preventDefault()),r};function patchProperty(e,t,n){let o=ObjectGetOwnPropertyDescriptor(e,t);if(!o&&n&&ObjectGetOwnPropertyDescriptor(n,t)&&(o={enumerable:!0,configurable:!0}),!o||!o.configurable)return;const r=zoneSymbol("on"+t+"patched");if(e.hasOwnProperty(r)&&e[r])return;delete o.writable,delete o.value;const s=o.get,a=o.set,i=t.slice(2);let c=zoneSymbolEventNames$1[i];c||(c=zoneSymbolEventNames$1[i]=zoneSymbol("ON_PROPERTY"+i)),o.set=function(t){let n=this;n||e!==_global||(n=_global),n&&("function"==typeof n[c]&&n.removeEventListener(i,wrapFn),a&&a.call(n,null),n[c]=t,"function"==typeof t&&n.addEventListener(i,wrapFn,!1))},o.get=function(){let n=this;if(n||e!==_global||(n=_global),!n)return null;const r=n[c];if(r)return r;if(s){let e=s.call(this);if(e)return o.set.call(this,e),"function"==typeof n[REMOVE_ATTRIBUTE]&&n.removeAttribute(t),e}return null},ObjectDefineProperty(e,t,o),e[r]=!0}function patchOnProperties(e,t,n){if(t)for(let o=0;o<t.length;o++)patchProperty(e,"on"+t[o],n);else{const t=[];for(const n in e)"on"==n.slice(0,2)&&t.push(n);for(let o=0;o<t.length;o++)patchProperty(e,t[o],n)}}const originalInstanceKey=zoneSymbol("originalInstance");function patchClass(e){const t=_global[e];if(!t)return;_global[zoneSymbol(e)]=t,_global[e]=function(){const n=bindArguments(arguments,e);switch(n.length){case 0:this[originalInstanceKey]=new t;break;case 1:this[originalInstanceKey]=new t(n[0]);break;case 2:this[originalInstanceKey]=new t(n[0],n[1]);break;case 3:this[originalInstanceKey]=new t(n[0],n[1],n[2]);break;case 4:this[originalInstanceKey]=new t(n[0],n[1],n[2],n[3]);break;default:throw new Error("Arg list too long.")}},attachOriginToPatched(_global[e],t);const n=new t((function(){}));let o;for(o in n)"XMLHttpRequest"===e&&"responseBlob"===o||function(t){"function"==typeof n[t]?_global[e].prototype[t]=function(){return this[originalInstanceKey][t].apply(this[originalInstanceKey],arguments)}:ObjectDefineProperty(_global[e].prototype,t,{set:function(n){"function"==typeof n?(this[originalInstanceKey][t]=wrapWithCurrentZone(n,e+"."+t),attachOriginToPatched(this[originalInstanceKey][t],n)):this[originalInstanceKey][t]=n},get:function(){return this[originalInstanceKey][t]}})}(o);for(o in t)"prototype"!==o&&t.hasOwnProperty(o)&&(_global[e][o]=t[o])}function patchMethod(e,t,n){let o=e;for(;o&&!o.hasOwnProperty(t);)o=ObjectGetPrototypeOf(o);!o&&e[t]&&(o=e);const r=zoneSymbol(t);let s=null;if(o&&(!(s=o[r])||!o.hasOwnProperty(r))&&(s=o[r]=o[t],isPropertyWritable(o&&ObjectGetOwnPropertyDescriptor(o,t)))){const e=n(s,r,t);o[t]=function(){return e(this,arguments)},attachOriginToPatched(o[t],s)}return s}function patchMacroTask(e,t,n){let o=null;function r(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},o.apply(t.target,t.args),e}o=patchMethod(e,t,(e=>function(t,o){const s=n(t,o);return s.cbIdx>=0&&"function"==typeof o[s.cbIdx]?scheduleMacroTaskWithCurrentZone(s.name,o[s.cbIdx],s,r):e.apply(t,o)}))}function attachOriginToPatched(e,t){e[zoneSymbol("OriginalDelegate")]=t}let isDetectedIEOrEdge=!1,ieOrEdge=!1;function isIE(){try{const e=internalWindow.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch(e){}return!1}function isIEOrEdge(){if(isDetectedIEOrEdge)return ieOrEdge;isDetectedIEOrEdge=!0;try{const e=internalWindow.navigator.userAgent;-1===e.indexOf("MSIE ")&&-1===e.indexOf("Trident/")&&-1===e.indexOf("Edge/")||(ieOrEdge=!0)}catch(e){}return ieOrEdge}Zone.__load_patch("ZoneAwarePromise",((e,t,n)=>{const o=Object.getOwnPropertyDescriptor,r=Object.defineProperty,s=n.symbol,a=[],i=!0===e[s("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=s("Promise"),l=s("then"),h="__creationTrace__";n.onUnhandledError=e=>{if(n.showUncaughtError()){const t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=()=>{for(;a.length;){const e=a.shift();try{e.zone.runGuarded((()=>{if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){p(e)}}};const u=s("unhandledPromiseRejectionHandler");function p(e){n.onUnhandledError(e);try{const n=t[u];"function"==typeof n&&n.call(this,e)}catch(e){}}function f(e){return e&&e.then}function _(e){return e}function d(e){return j.reject(e)}const E=s("state"),g=s("value"),y=s("finally"),T=s("parentPromiseValue"),k=s("parentPromiseState"),m="Promise.then",b=null,S=!0,v=!1,O=0;function w(e,t){return n=>{try{N(e,t,n)}catch(t){N(e,!1,t)}}}const P=function(){let e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},Z="Promise resolved with itself",D=s("currentTaskTrace");function N(e,o,s){const c=P();if(e===s)throw new TypeError(Z);if(e[E]===b){let l=null;try{"object"!=typeof s&&"function"!=typeof s||(l=s&&s.then)}catch(t){return c((()=>{N(e,!1,t)}))(),e}if(o!==v&&s instanceof j&&s.hasOwnProperty(E)&&s.hasOwnProperty(g)&&s[E]!==b)R(s),N(e,s[E],s[g]);else if(o!==v&&"function"==typeof l)try{l.call(s,c(w(e,o)),c(w(e,!1)))}catch(t){c((()=>{N(e,!1,t)}))()}else{e[E]=o;const c=e[g];if(e[g]=s,e[y]===y&&o===S&&(e[E]=e[k],e[g]=e[T]),o===v&&s instanceof Error){const e=t.currentTask&&t.currentTask.data&&t.currentTask.data[h];e&&r(s,D,{configurable:!0,enumerable:!1,writable:!0,value:e})}for(let t=0;t<c.length;)M(e,c[t++],c[t++],c[t++],c[t++]);if(0==c.length&&o==v){e[E]=O;let o=s;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(e){o=e}i&&(o.throwOriginal=!0),o.rejection=s,o.promise=e,o.zone=t.current,o.task=t.currentTask,a.push(o),n.scheduleMicroTask()}}}return e}const z=s("rejectionHandledHandler");function R(e){if(e[E]===O){try{const n=t[z];n&&"function"==typeof n&&n.call(this,{rejection:e[g],promise:e})}catch(e){}e[E]=v;for(let t=0;t<a.length;t++)e===a[t].promise&&a.splice(t,1)}}function M(e,t,n,o,r){R(e);const s=e[E],a=s?"function"==typeof o?o:_:"function"==typeof r?r:d;t.scheduleMicroTask(m,(()=>{try{const o=e[g],r=!!n&&y===n[y];r&&(n[T]=o,n[k]=s);const i=t.run(a,void 0,r&&a!==d&&a!==_?[]:[o]);N(n,!0,i)}catch(e){N(n,!1,e)}}),n)}const I=function(){},C=e.AggregateError;class j{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(e){return N(new this(null),S,e)}static reject(e){return N(new this(null),v,e)}static any(e){if(!e||"function"!=typeof e[Symbol.iterator])return Promise.reject(new C([],"All promises were rejected"));const t=[];let n=0;try{for(let o of e)n++,t.push(j.resolve(o))}catch(e){return Promise.reject(new C([],"All promises were rejected"))}if(0===n)return Promise.reject(new C([],"All promises were rejected"));let o=!1;const r=[];return new j(((e,s)=>{for(let a=0;a<t.length;a++)t[a].then((t=>{o||(o=!0,e(t))}),(e=>{r.push(e),n--,0===n&&(o=!0,s(new C(r,"All promises were rejected")))}))}))}static race(e){let t,n,o=new this(((e,o)=>{t=e,n=o}));function r(e){t(e)}function s(e){n(e)}for(let t of e)f(t)||(t=this.resolve(t)),t.then(r,s);return o}static all(e){return j.allWithCallback(e)}static allSettled(e){return(this&&this.prototype instanceof j?this:j).allWithCallback(e,{thenCallback:e=>({status:"fulfilled",value:e}),errorCallback:e=>({status:"rejected",reason:e})})}static allWithCallback(e,t){let n,o,r=new this(((e,t)=>{n=e,o=t})),s=2,a=0;const i=[];for(let r of e){f(r)||(r=this.resolve(r));const e=a;try{r.then((o=>{i[e]=t?t.thenCallback(o):o,s--,0===s&&n(i)}),(r=>{t?(i[e]=t.errorCallback(r),s--,0===s&&n(i)):o(r)}))}catch(e){o(e)}s++,a++}return s-=2,0===s&&n(i),r}constructor(e){const t=this;if(!(t instanceof j))throw new Error("Must be an instanceof Promise.");t[E]=b,t[g]=[];try{const n=P();e&&e(n(w(t,S)),n(w(t,v)))}catch(e){N(t,!1,e)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return j}then(e,n){let o=this.constructor?.[Symbol.species];o&&"function"==typeof o||(o=this.constructor||j);const r=new o(I),s=t.current;return this[E]==b?this[g].push(s,r,e,n):M(this,s,r,e,n),r}catch(e){return this.then(null,e)}finally(e){let n=this.constructor?.[Symbol.species];n&&"function"==typeof n||(n=j);const o=new n(I);o[y]=y;const r=t.current;return this[E]==b?this[g].push(r,o,e,e):M(this,r,o,e,e),o}}j.resolve=j.resolve,j.reject=j.reject,j.race=j.race,j.all=j.all;const L=e[c]=e.Promise;e.Promise=j;const A=s("thenPatched");function F(e){const t=e.prototype,n=o(t,"then");if(n&&(!1===n.writable||!n.configurable))return;const r=t.then;t[l]=r,e.prototype.then=function(e,t){return new j(((e,t)=>{r.call(this,e,t)})).then(e,t)},e[A]=!0}return n.patchThen=F,L&&(F(L),patchMethod(e,"fetch",(e=>function t(e){return function(t,n){let o=e.apply(t,n);if(o instanceof j)return o;let r=o.constructor;return r[A]||F(r),o}}(e)))),Promise[t.__symbol__("uncaughtPromiseErrors")]=a,j})),Zone.__load_patch("toString",(e=>{const t=Function.prototype.toString,n=zoneSymbol("OriginalDelegate"),o=zoneSymbol("Promise"),r=zoneSymbol("Error"),s=function s(){if("function"==typeof this){const s=this[n];if(s)return"function"==typeof s?t.call(s):Object.prototype.toString.call(s);if(this===Promise){const n=e[o];if(n)return t.call(n)}if(this===Error){const n=e[r];if(n)return t.call(n)}}return t.call(this)};s[n]=t,Function.prototype.toString=s;const a=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":a.call(this)}}));let passiveSupported=!1;if("undefined"!=typeof window)try{const e=Object.defineProperty({},"passive",{get:function(){passiveSupported=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){passiveSupported=!1}const OPTIMIZED_ZONE_EVENT_TASK_DATA={useG:!0},zoneSymbolEventNames={},globalSources={},EVENT_NAME_SYMBOL_REGX=new RegExp("^"+ZONE_SYMBOL_PREFIX+"(\\w+)(true|false)$"),IMMEDIATE_PROPAGATION_SYMBOL=zoneSymbol("propagationStopped");function prepareEventNames(e,t){const n=(t?t(e):e)+FALSE_STR,o=(t?t(e):e)+TRUE_STR,r=ZONE_SYMBOL_PREFIX+n,s=ZONE_SYMBOL_PREFIX+o;zoneSymbolEventNames[e]={},zoneSymbolEventNames[e][FALSE_STR]=r,zoneSymbolEventNames[e][TRUE_STR]=s}function patchEventTarget(e,t,n,o){const r=o&&o.add||"addEventListener",s=o&&o.rm||"removeEventListener",a=o&&o.listeners||"eventListeners",i=o&&o.rmAll||"removeAllListeners",c=zoneSymbol(r),l="."+r+":",h="prependListener",u="."+h+":",p=function(e,t,n){if(e.isRemoved)return;const o=e.callback;let r;"object"==typeof o&&o.handleEvent&&(e.callback=e=>o.handleEvent(e),e.originalDelegate=o);try{e.invoke(e,t,[n])}catch(e){r=e}const a=e.options;return a&&"object"==typeof a&&a.once&&t[s].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,a),r};function f(n,o,r){if(!(o=o||e.event))return;const s=n||o.target||e,a=s[zoneSymbolEventNames[o.type][r?TRUE_STR:FALSE_STR]];if(a){const e=[];if(1===a.length){const t=p(a[0],s,o);t&&e.push(t)}else{const t=a.slice();for(let n=0;n<t.length&&(!o||!0!==o[IMMEDIATE_PROPAGATION_SYMBOL]);n++){const r=p(t[n],s,o);r&&e.push(r)}}if(1===e.length)throw e[0];for(let n=0;n<e.length;n++){const o=e[n];t.nativeScheduleMicroTask((()=>{throw o}))}}}const _=function(e){return f(this,e,!1)},d=function(e){return f(this,e,!0)};function E(t,n){if(!t)return!1;let o=!0;n&&void 0!==n.useG&&(o=n.useG);const p=n&&n.vh;let f=!0;n&&void 0!==n.chkDup&&(f=n.chkDup);let E=!1;n&&void 0!==n.rt&&(E=n.rt);let g=t;for(;g&&!g.hasOwnProperty(r);)g=ObjectGetPrototypeOf(g);if(!g&&t[r]&&(g=t),!g)return!1;if(g[c])return!1;const y=n&&n.eventNameToString,T={},k=g[c]=g[r],m=g[zoneSymbol(s)]=g[s],b=g[zoneSymbol(a)]=g[a],S=g[zoneSymbol(i)]=g[i];let v;n&&n.prepend&&(v=g[zoneSymbol(n.prepend)]=g[n.prepend]);const O=o?function(e){if(!T.isExisting)return k.call(T.target,T.eventName,T.capture?d:_,T.options)}:function(e){return k.call(T.target,T.eventName,e.invoke,T.options)},w=o?function(e){if(!e.isRemoved){const t=zoneSymbolEventNames[e.eventName];let n;t&&(n=t[e.capture?TRUE_STR:FALSE_STR]);const o=n&&e.target[n];if(o)for(let t=0;t<o.length;t++)if(o[t]===e){o.splice(t,1),e.isRemoved=!0,0===o.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return m.call(e.target,e.eventName,e.capture?d:_,e.options)}:function(e){return m.call(e.target,e.eventName,e.invoke,e.options)},P=n&&n.diff?n.diff:function(e,t){const n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},Z=Zone[zoneSymbol("UNPATCHED_EVENTS")],D=e[zoneSymbol("PASSIVE_EVENTS")],N=function(t,r,s,a,i=!1,c=!1){return function(){const l=this||e;let h=arguments[0];n&&n.transferEventName&&(h=n.transferEventName(h));let u=arguments[1];if(!u)return t.apply(this,arguments);if(isNode&&"uncaughtException"===h)return t.apply(this,arguments);let _=!1;if("function"!=typeof u){if(!u.handleEvent)return t.apply(this,arguments);_=!0}if(p&&!p(t,u,l,arguments))return;const d=passiveSupported&&!!D&&-1!==D.indexOf(h),E=function g(e,t){return!passiveSupported&&"object"==typeof e&&e?!!e.capture:passiveSupported&&t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?{...e,passive:!0}:e:{passive:!0}:e}(arguments[2],d);if(Z)for(let e=0;e<Z.length;e++)if(h===Z[e])return d?t.call(l,h,u,E):t.apply(this,arguments);const k=!!E&&("boolean"==typeof E||E.capture),m=!(!E||"object"!=typeof E)&&E.once,b=Zone.current;let S=zoneSymbolEventNames[h];S||(prepareEventNames(h,y),S=zoneSymbolEventNames[h]);const v=S[k?TRUE_STR:FALSE_STR];let O,w=l[v],N=!1;if(w){if(N=!0,f)for(let e=0;e<w.length;e++)if(P(w[e],u))return}else w=l[v]=[];const z=l.constructor.name,R=globalSources[z];R&&(O=R[h]),O||(O=z+r+(y?y(h):h)),T.options=E,m&&(T.options.once=!1),T.target=l,T.capture=k,T.eventName=h,T.isExisting=N;const M=o?OPTIMIZED_ZONE_EVENT_TASK_DATA:void 0;M&&(M.taskData=T);const I=b.scheduleEventTask(O,u,M,s,a);return T.target=null,M&&(M.taskData=null),m&&(E.once=!0),(passiveSupported||"boolean"!=typeof I.options)&&(I.options=E),I.target=l,I.capture=k,I.eventName=h,_&&(I.originalDelegate=u),c?w.unshift(I):w.push(I),i?l:void 0}};return g[r]=N(k,l,O,w,E),v&&(g[h]=N(v,u,(function(e){return v.call(T.target,T.eventName,e.invoke,T.options)}),w,E,!0)),g[s]=function(){const t=this||e;let o=arguments[0];n&&n.transferEventName&&(o=n.transferEventName(o));const r=arguments[2],s=!!r&&("boolean"==typeof r||r.capture),a=arguments[1];if(!a)return m.apply(this,arguments);if(p&&!p(m,a,t,arguments))return;const i=zoneSymbolEventNames[o];let c;i&&(c=i[s?TRUE_STR:FALSE_STR]);const l=c&&t[c];if(l)for(let e=0;e<l.length;e++){const n=l[e];if(P(n,a))return l.splice(e,1),n.isRemoved=!0,0===l.length&&(n.allRemoved=!0,t[c]=null,"string"==typeof o)&&(t[ZONE_SYMBOL_PREFIX+"ON_PROPERTY"+o]=null),n.zone.cancelTask(n),E?t:void 0}return m.apply(this,arguments)},g[a]=function(){const t=this||e;let o=arguments[0];n&&n.transferEventName&&(o=n.transferEventName(o));const r=[],s=findEventTasks(t,y?y(o):o);for(let e=0;e<s.length;e++){const t=s[e];r.push(t.originalDelegate?t.originalDelegate:t.callback)}return r},g[i]=function(){const t=this||e;let o=arguments[0];if(o){n&&n.transferEventName&&(o=n.transferEventName(o));const e=zoneSymbolEventNames[o];if(e){const n=t[e[FALSE_STR]],r=t[e[TRUE_STR]];if(n){const e=n.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,o,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}if(r){const e=r.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,o,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}}}else{const e=Object.keys(t);for(let t=0;t<e.length;t++){const n=EVENT_NAME_SYMBOL_REGX.exec(e[t]);let o=n&&n[1];o&&"removeListener"!==o&&this[i].call(this,o)}this[i].call(this,"removeListener")}if(E)return this},attachOriginToPatched(g[r],k),attachOriginToPatched(g[s],m),S&&attachOriginToPatched(g[i],S),b&&attachOriginToPatched(g[a],b),!0}let g=[];for(let e=0;e<n.length;e++)g[e]=E(n[e],o);return g}function findEventTasks(e,t){if(!t){const n=[];for(let o in e){const r=EVENT_NAME_SYMBOL_REGX.exec(o);let s=r&&r[1];if(s&&(!t||s===t)){const t=e[o];if(t)for(let e=0;e<t.length;e++)n.push(t[e])}}return n}let n=zoneSymbolEventNames[t];n||(prepareEventNames(t),n=zoneSymbolEventNames[t]);const o=e[n[FALSE_STR]],r=e[n[TRUE_STR]];return o?r?o.concat(r):o.slice():r?r.slice():[]}function patchEventPrototype(e,t){const n=e.Event;n&&n.prototype&&t.patchMethod(n.prototype,"stopImmediatePropagation",(e=>function(t,n){t[IMMEDIATE_PROPAGATION_SYMBOL]=!0,e&&e.apply(t,n)}))}function patchCallbacks(e,t,n,o,r){const s=Zone.__symbol__(o);if(t[s])return;const a=t[s]=t[o];t[o]=function(s,i,c){return i&&i.prototype&&r.forEach((function(t){const r=`${n}.${o}::`+t,s=i.prototype;try{if(s.hasOwnProperty(t)){const n=e.ObjectGetOwnPropertyDescriptor(s,t);n&&n.value?(n.value=e.wrapWithCurrentZone(n.value,r),e._redefineProperty(i.prototype,t,n)):s[t]&&(s[t]=e.wrapWithCurrentZone(s[t],r))}else s[t]&&(s[t]=e.wrapWithCurrentZone(s[t],r))}catch{}})),a.call(t,s,i,c)},e.attachOriginToPatched(t[o],a)}function filterProperties(e,t,n){if(!n||0===n.length)return t;const o=n.filter((t=>t.target===e));if(!o||0===o.length)return t;const r=o[0].ignoreProperties;return t.filter((e=>-1===r.indexOf(e)))}function patchFilteredProperties(e,t,n,o){e&&patchOnProperties(e,filterProperties(e,t,n),o)}function getOnEventNames(e){return Object.getOwnPropertyNames(e).filter((e=>e.startsWith("on")&&e.length>2)).map((e=>e.substring(2)))}function propertyDescriptorPatch(e,t){if(isNode&&!isMix)return;if(Zone[e.symbol("patchEvents")])return;const n=t.__Zone_ignore_on_properties;let o=[];if(isBrowser){const e=window;o=o.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const t=isIE()?[{target:e,ignoreProperties:["error"]}]:[];patchFilteredProperties(e,getOnEventNames(e),n?n.concat(t):n,ObjectGetPrototypeOf(e))}o=o.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let e=0;e<o.length;e++){const r=t[o[e]];r&&r.prototype&&patchFilteredProperties(r.prototype,getOnEventNames(r.prototype),n)}}function patchQueueMicrotask(e,t){t.patchMethod(e,"queueMicrotask",(e=>function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}))}Zone.__load_patch("util",((e,t,n)=>{const o=getOnEventNames(e);n.patchOnProperties=patchOnProperties,n.patchMethod=patchMethod,n.bindArguments=bindArguments,n.patchMacroTask=patchMacroTask;const r=t.__symbol__("BLACK_LISTED_EVENTS"),s=t.__symbol__("UNPATCHED_EVENTS");e[s]&&(e[r]=e[s]),e[r]&&(t[r]=t[s]=e[r]),n.patchEventPrototype=patchEventPrototype,n.patchEventTarget=patchEventTarget,n.isIEOrEdge=isIEOrEdge,n.ObjectDefineProperty=ObjectDefineProperty,n.ObjectGetOwnPropertyDescriptor=ObjectGetOwnPropertyDescriptor,n.ObjectCreate=ObjectCreate,n.ArraySlice=ArraySlice,n.patchClass=patchClass,n.wrapWithCurrentZone=wrapWithCurrentZone,n.filterProperties=filterProperties,n.attachOriginToPatched=attachOriginToPatched,n._redefineProperty=Object.defineProperty,n.patchCallbacks=patchCallbacks,n.getGlobalObjects=()=>({globalSources:globalSources,zoneSymbolEventNames:zoneSymbolEventNames,eventNames:o,isBrowser:isBrowser,isMix:isMix,isNode:isNode,TRUE_STR:TRUE_STR,FALSE_STR:FALSE_STR,ZONE_SYMBOL_PREFIX:ZONE_SYMBOL_PREFIX,ADD_EVENT_LISTENER_STR:"addEventListener",REMOVE_EVENT_LISTENER_STR:"removeEventListener"})}));const taskSymbol=zoneSymbol("zoneTask");function patchTimer(e,t,n,o){let r=null,s=null;n+=o;const a={};function i(t){const n=t.data;return n.args[0]=function(){return t.invoke.apply(this,arguments)},n.handleId=r.apply(e,n.args),t}function c(t){return s.call(e,t.data.handleId)}r=patchMethod(e,t+=o,(n=>function(r,s){if("function"==typeof s[0]){const e={isPeriodic:"Interval"===o,delay:"Timeout"===o||"Interval"===o?s[1]||0:void 0,args:s},n=s[0];s[0]=function t(){try{return n.apply(this,arguments)}finally{e.isPeriodic||("number"==typeof e.handleId?delete a[e.handleId]:e.handleId&&(e.handleId[taskSymbol]=null))}};const r=scheduleMacroTaskWithCurrentZone(t,s[0],e,i,c);if(!r)return r;const l=r.data.handleId;return"number"==typeof l?a[l]=r:l&&(l[taskSymbol]=r),l&&l.ref&&l.unref&&"function"==typeof l.ref&&"function"==typeof l.unref&&(r.ref=l.ref.bind(l),r.unref=l.unref.bind(l)),"number"==typeof l||l?l:r}return n.apply(e,s)})),s=patchMethod(e,n,(t=>function(n,o){const r=o[0];let s;"number"==typeof r?s=a[r]:(s=r&&r[taskSymbol],s||(s=r)),s&&"string"==typeof s.type?"notScheduled"!==s.state&&(s.cancelFn&&s.data.isPeriodic||0===s.runCount)&&("number"==typeof r?delete a[r]:r&&(r[taskSymbol]=null),s.zone.cancelTask(s)):t.apply(e,o)}))}function patchCustomElements(e,t){const{isBrowser:n,isMix:o}=t.getGlobalObjects();(n||o)&&e.customElements&&"customElements"in e&&t.patchCallbacks(t,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}function eventTargetPatch(e,t){if(Zone[t.symbol("patchEventTarget")])return;const{eventNames:n,zoneSymbolEventNames:o,TRUE_STR:r,FALSE_STR:s,ZONE_SYMBOL_PREFIX:a}=t.getGlobalObjects();for(let e=0;e<n.length;e++){const t=n[e],i=a+(t+s),c=a+(t+r);o[t]={},o[t][s]=i,o[t][r]=c}const i=e.EventTarget;return i&&i.prototype?(t.patchEventTarget(e,t,[i&&i.prototype]),!0):void 0}function patchEvent(e,t){t.patchEventPrototype(e,t)}Zone.__load_patch("legacy",(e=>{const t=e[Zone.__symbol__("legacyPatch")];t&&t()})),Zone.__load_patch("timers",(e=>{const t="set",n="clear";patchTimer(e,t,n,"Timeout"),patchTimer(e,t,n,"Interval"),patchTimer(e,t,n,"Immediate")})),Zone.__load_patch("requestAnimationFrame",(e=>{patchTimer(e,"request","cancel","AnimationFrame"),patchTimer(e,"mozRequest","mozCancel","AnimationFrame"),patchTimer(e,"webkitRequest","webkitCancel","AnimationFrame")})),Zone.__load_patch("blocking",((e,t)=>{const n=["alert","prompt","confirm"];for(let o=0;o<n.length;o++)patchMethod(e,n[o],((n,o,r)=>function(o,s){return t.current.run(n,e,s,r)}))})),Zone.__load_patch("EventTarget",((e,t,n)=>{patchEvent(e,n),eventTargetPatch(e,n);const o=e.XMLHttpRequestEventTarget;o&&o.prototype&&n.patchEventTarget(e,n,[o.prototype])})),Zone.__load_patch("MutationObserver",((e,t,n)=>{patchClass("MutationObserver"),patchClass("WebKitMutationObserver")})),Zone.__load_patch("IntersectionObserver",((e,t,n)=>{patchClass("IntersectionObserver")})),Zone.__load_patch("FileReader",((e,t,n)=>{patchClass("FileReader")})),Zone.__load_patch("on_property",((e,t,n)=>{propertyDescriptorPatch(n,e)})),Zone.__load_patch("customElements",((e,t,n)=>{patchCustomElements(e,n)})),Zone.__load_patch("XHR",((e,t)=>{!function n(e){const n=e.XMLHttpRequest;if(!n)return;const l=n.prototype;let h=l[ZONE_SYMBOL_ADD_EVENT_LISTENER],u=l[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];if(!h){const t=e.XMLHttpRequestEventTarget;if(t){const e=t.prototype;h=e[ZONE_SYMBOL_ADD_EVENT_LISTENER],u=e[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]}}const p="readystatechange",f="scheduled";function _(e){const n=e.data,r=n.target;r[a]=!1,r[c]=!1;const i=r[s];h||(h=r[ZONE_SYMBOL_ADD_EVENT_LISTENER],u=r[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]),i&&u.call(r,p,i);const l=r[s]=()=>{if(r.readyState===r.DONE)if(!n.aborted&&r[a]&&e.state===f){const o=r[t.__symbol__("loadfalse")];if(0!==r.status&&o&&o.length>0){const s=e.invoke;e.invoke=function(){const o=r[t.__symbol__("loadfalse")];for(let t=0;t<o.length;t++)o[t]===e&&o.splice(t,1);n.aborted||e.state!==f||s.call(e)},o.push(e)}else e.invoke()}else n.aborted||!1!==r[a]||(r[c]=!0)};return h.call(r,p,l),r[o]||(r[o]=e),k.apply(r,n.args),r[a]=!0,e}function d(){}function E(e){const t=e.data;return t.aborted=!0,m.apply(t.target,t.args)}const g=patchMethod(l,"open",(()=>function(e,t){return e[r]=0==t[2],e[i]=t[1],g.apply(e,t)})),y=zoneSymbol("fetchTaskAborting"),T=zoneSymbol("fetchTaskScheduling"),k=patchMethod(l,"send",(()=>function(e,n){if(!0===t.current[T])return k.apply(e,n);if(e[r])return k.apply(e,n);{const t={target:e,url:e[i],isPeriodic:!1,args:n,aborted:!1},o=scheduleMacroTaskWithCurrentZone("XMLHttpRequest.send",d,t,_,E);e&&!0===e[c]&&!t.aborted&&o.state===f&&o.invoke()}})),m=patchMethod(l,"abort",(()=>function(e,n){const r=function s(e){return e[o]}(e);if(r&&"string"==typeof r.type){if(null==r.cancelFn||r.data&&r.data.aborted)return;r.zone.cancelTask(r)}else if(!0===t.current[y])return m.apply(e,n)}))}(e);const o=zoneSymbol("xhrTask"),r=zoneSymbol("xhrSync"),s=zoneSymbol("xhrListener"),a=zoneSymbol("xhrScheduled"),i=zoneSymbol("xhrURL"),c=zoneSymbol("xhrErrorBeforeScheduled")})),Zone.__load_patch("geolocation",(e=>{e.navigator&&e.navigator.geolocation&&patchPrototype(e.navigator.geolocation,["getCurrentPosition","watchPosition"])})),Zone.__load_patch("PromiseRejectionEvent",((e,t)=>{function n(t){return function(n){findEventTasks(e,t).forEach((o=>{const r=e.PromiseRejectionEvent;if(r){const e=new r(t,{promise:n.promise,reason:n.rejection});o.invoke(e)}}))}}e.PromiseRejectionEvent&&(t[zoneSymbol("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),t[zoneSymbol("rejectionHandledHandler")]=n("rejectionhandled"))})),Zone.__load_patch("queueMicrotask",((e,t,n)=>{patchQueueMicrotask(e,n)}));