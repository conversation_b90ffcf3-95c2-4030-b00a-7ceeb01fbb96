import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-therapists',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-dumbbell me-2"></i>Therapist Management</h2>
        <button class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add New Therapist
        </button>
      </div>
      
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>License #</th>
                  <th>Specialization</th>
                  <th>Treatment Specialties</th>
                  <th>Max Patients/Day</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>TH001</td>
                  <td>Dr. <PERSON></td>
                  <td>TH123456</td>
                  <td>Physical Therapy</td>
                  <td>Orthopedic, Sports</td>
                  <td>12</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <tr>
                  <td>TH002</td>
                  <td>Dr. Kevin Martinez</td>
                  <td>TH789012</td>
                  <td>Occupational Therapy</td>
                  <td>Neurological, Pediatric</td>
                  <td>10</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <!-- More therapist rows would be loaded dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class TherapistsComponent {}
