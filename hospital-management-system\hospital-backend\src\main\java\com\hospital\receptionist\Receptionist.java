package com.hospital.receptionist;

import com.hospital.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "receptionist")
public class Receptionist extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String department; // Emergency, Outpatient, Admissions, etc.
    private Integer yearsOfExperience;
    private String shift; // DAY, NIGHT, ROTATING
    
    @Enumerated(EnumType.STRING)
    private ReceptionistStatus status;
    
    // Reception-specific fields
    private String workStation; // Front Desk, Registration, Discharge, etc.
    private String languagesSpoken; // For patient communication
    private Boolean canHandleInsurance;
    private Boolean canScheduleAppointments;
    private Boolean canProcessPayments;
    private String supervisorId; // Reference to supervising staff
    private String accessLevel; // Basic, Advanced, Administrative

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum ReceptionistStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    SUSPENDED,
    RETIRED
}
