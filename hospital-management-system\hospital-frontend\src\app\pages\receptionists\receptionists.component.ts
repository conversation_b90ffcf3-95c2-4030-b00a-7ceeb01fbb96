import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-receptionists',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-desk me-2"></i>Receptionist Management</h2>
        <button class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add New Receptionist
        </button>
      </div>
      
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Department</th>
                  <th>Work Station</th>
                  <th>Shift</th>
                  <th>Languages</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>RC001</td>
                  <td><PERSON></td>
                  <td>Emergency</td>
                  <td>Front Desk</td>
                  <td>Day</td>
                  <td>English, Spanish</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <tr>
                  <td>RC002</td>
                  <td>David Thompson</td>
                  <td>Outpatient</td>
                  <td>Registration</td>
                  <td>Night</td>
                  <td>English</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <!-- More receptionist rows would be loaded dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class ReceptionistsComponent {}
