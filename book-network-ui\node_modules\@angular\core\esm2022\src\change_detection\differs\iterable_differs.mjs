/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵɵdefineInjectable } from '../../di/interface/defs';
import { Optional, SkipSelf } from '../../di/metadata';
import { RuntimeError } from '../../errors';
import { DefaultIterableDifferFactory } from '../differs/default_iterable_differ';
export function defaultIterableDiffersFactory() {
    return new IterableDiffers([new DefaultIterableDifferFactory()]);
}
/**
 * A repository of different iterable diffing strategies used by NgFor, NgClass, and others.
 *
 * @publicApi
 */
export class IterableDiffers {
    /** @nocollapse */
    static { this.ɵprov = ɵɵdefineInjectable({ token: IterableDiffers, providedIn: 'root', factory: defaultIterableDiffersFactory }); }
    constructor(factories) {
        this.factories = factories;
    }
    static create(factories, parent) {
        if (parent != null) {
            const copied = parent.factories.slice();
            factories = factories.concat(copied);
        }
        return new IterableDiffers(factories);
    }
    /**
     * Takes an array of {@link IterableDifferFactory} and returns a provider used to extend the
     * inherited {@link IterableDiffers} instance with the provided factories and return a new
     * {@link IterableDiffers} instance.
     *
     * @usageNotes
     * ### Example
     *
     * The following example shows how to extend an existing list of factories,
     * which will only be applied to the injector for this component and its children.
     * This step is all that's required to make a new {@link IterableDiffer} available.
     *
     * ```
     * @Component({
     *   viewProviders: [
     *     IterableDiffers.extend([new ImmutableListDiffer()])
     *   ]
     * })
     * ```
     */
    static extend(factories) {
        return {
            provide: IterableDiffers,
            useFactory: (parent) => {
                // if parent is null, it means that we are in the root injector and we have just overridden
                // the default injection mechanism for IterableDiffers, in such a case just assume
                // `defaultIterableDiffersFactory`.
                return IterableDiffers.create(factories, parent || defaultIterableDiffersFactory());
            },
            // Dependency technically isn't optional, but we can provide a better error message this way.
            deps: [[IterableDiffers, new SkipSelf(), new Optional()]]
        };
    }
    find(iterable) {
        const factory = this.factories.find(f => f.supports(iterable));
        if (factory != null) {
            return factory;
        }
        else {
            throw new RuntimeError(901 /* RuntimeErrorCode.NO_SUPPORTING_DIFFER_FACTORY */, ngDevMode &&
                `Cannot find a differ supporting object '${iterable}' of type '${getTypeNameForDebugging(iterable)}'`);
        }
    }
}
export function getTypeNameForDebugging(type) {
    return type['name'] || typeof type;
}
//# sourceMappingURL=data:application/json;base64,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