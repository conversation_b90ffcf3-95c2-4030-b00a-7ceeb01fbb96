{"id": "hospital-management", "realm": "hospital-management", "displayName": "Hospital Management System", "displayNameHtml": "Hospital Management System", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": true, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "admin-role-id", "name": "ADMIN", "description": "Administrator role with full system access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "doctor-role-id", "name": "DOCTOR", "description": "Doctor role with patient management access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "nurse-role-id", "name": "NURSE", "description": "Nurse role with patient care access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "patient-role-id", "name": "PATIENT", "description": "Patient role with limited access to own records", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "pharmacist-role-id", "name": "PHARMACIST", "description": "Pharmacist role with medication management access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "lab-technician-role-id", "name": "LAB_TECHNICIAN", "description": "Lab Technician role with laboratory test access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "radiologist-role-id", "name": "RADIOLOGIST", "description": "Radiologist role with imaging and diagnostic access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "receptionist-role-id", "name": "RECEPTIONIST", "description": "Receptionist role with patient registration and scheduling access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "therapist-role-id", "name": "THERAPIST", "description": "Therapist role with rehabilitation and therapy access", "composite": false, "clientRole": false, "containerId": "hospital-management"}, {"id": "technician-role-id", "name": "TECHNICIAN", "description": "Technician role with equipment and procedure support access", "composite": false, "clientRole": false, "containerId": "hospital-management"}]}, "groups": [{"id": "doctors-group-id", "name": "Doctors", "path": "/Doctors", "realmRoles": ["DOCTOR"]}, {"id": "nurses-group-id", "name": "Nurses", "path": "/Nurses", "realmRoles": ["NURSE"]}, {"id": "patients-group-id", "name": "Patients", "path": "/Patients", "realmRoles": ["PATIENT"]}, {"id": "admins-group-id", "name": "Administrators", "path": "/Administrators", "realmRoles": ["ADMIN"]}, {"id": "pharmacists-group-id", "name": "Pharmacists", "path": "/Pharmacists", "realmRoles": ["PHARMACIST"]}, {"id": "lab-technicians-group-id", "name": "Lab Technicians", "path": "/LabTechnicians", "realmRoles": ["LAB_TECHNICIAN"]}, {"id": "radiologists-group-id", "name": "Radiologists", "path": "/Radiologists", "realmRoles": ["RADIOLOGIST"]}, {"id": "receptionists-group-id", "name": "Receptionists", "path": "/Receptionists", "realmRoles": ["RECEPTIONIST"]}, {"id": "therapists-group-id", "name": "Therapists", "path": "/Therapists", "realmRoles": ["THERAPIST"]}, {"id": "technicians-group-id", "name": "Technicians", "path": "/Technicians", "realmRoles": ["TECHNICIAN"]}], "defaultRoles": ["PATIENT"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "admin-user-id", "username": "admin", "enabled": true, "totp": false, "emailVerified": true, "firstName": "System", "lastName": "Administrator", "email": "<EMAIL>", "credentials": [{"id": "admin-password-id", "type": "password", "userLabel": "My password", "createdDate": 1640995200000, "secretData": "{\"value\":\"$2a$10$example.hash.for.admin.password\",\"salt\":\"example.salt\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\"}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["ADMIN"], "clientRoles": {}, "notBefore": 0, "groups": ["/Administrators"]}], "scopeMappings": [], "clientScopeMappings": {}, "clients": [{"id": "hospital-frontend-client-id", "clientId": "hospital-frontend", "name": "Hospital Management Frontend", "description": "Frontend client for Hospital Management System", "rootUrl": "http://localhost:4200", "adminUrl": "http://localhost:4200", "baseUrl": "http://localhost:4200", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["http://localhost:4200/*"], "webOrigins": ["http://localhost:4200"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [], "authenticatorConfig": [], "requiredActions": [], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {}, "keycloakVersion": "24.0.2", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}