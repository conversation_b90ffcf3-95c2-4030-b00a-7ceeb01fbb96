package com.hospital;

import com.hospital.role.Role;
import com.hospital.role.RoleRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableJpaAuditing(auditorAwareRef = "auditorAware")
@EnableAsync
@SpringBootApplication
public class HospitalManagementApiApplication {

	public static void main(String[] args) {
		SpringApplication.run(HospitalManagementApiApplication.class, args);
	}

	@Bean
	public CommandLineRunner runner(RoleRepository roleRepository) {
		return args -> {
			if (roleRepository.findByName("ADMIN").isEmpty()) {
				roleRepository.save(Role.builder().name("ADMIN").build());
			}
			if (roleRepository.findByName("DOCTOR").isEmpty()) {
				roleRepository.save(Role.builder().name("DOCTOR").build());
			}
			if (roleRepository.findByName("NURSE").isEmpty()) {
				roleRepository.save(Role.builder().name("NURSE").build());
			}
			if (roleRepository.findByName("PATIENT").isEmpty()) {
				roleRepository.save(Role.builder().name("PATIENT").build());
			}
		};
	}
}
