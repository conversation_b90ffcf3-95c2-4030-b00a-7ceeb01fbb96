import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-radiologists',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-x-ray me-2"></i>Radiologist Management</h2>
        <button class="btn btn-primary">
          <i class="fas fa-plus me-2"></i>Add New Radiologist
        </button>
      </div>
      
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>License #</th>
                  <th>Specialization</th>
                  <th>Subspecialties</th>
                  <th>Modalities</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>RD001</td>
                  <td>Dr. <PERSON></td>
                  <td>RD123456</td>
                  <td>Diagnostic Radiology</td>
                  <td>Neuroradiology</td>
                  <td>CT, MRI</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <tr>
                  <td>RD002</td>
                  <td>Dr. James Anderson</td>
                  <td>RD789012</td>
                  <td>Interventional Radiology</td>
                  <td>Cardiac Imaging</td>
                  <td>X-Ray, Ultrasound</td>
                  <td><span class="badge bg-success">Active</span></td>
                  <td>
                    <button class="btn btn-sm btn-outline-primary me-1">View</button>
                    <button class="btn btn-sm btn-outline-warning">Edit</button>
                  </td>
                </tr>
                <!-- More radiologist rows would be loaded dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class RadiologistsComponent {}
