/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '@angular/core';
/** Whether test modules should be torn down by default. */
export const TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;
/** Whether unknown elements in templates should throw by default. */
export const THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;
/** Whether unknown properties in templates should throw by default. */
export const THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;
/**
 * An abstract class for inserting the root test component element in a platform independent way.
 *
 * @publicApi
 */
export class TestComponentRenderer {
    insertRootElement(rootElementId) { }
    removeAllRootElements() { }
}
/**
 * @publicApi
 */
export const ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');
/**
 * @publicApi
 */
export const ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');
//# sourceMappingURL=data:application/json;base64,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