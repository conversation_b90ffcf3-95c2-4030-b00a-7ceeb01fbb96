/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { APP_ID, PLATFORM_ID } from './application_tokens';
import { inject } from './di/injector_compatibility';
import { ɵɵdefineInjectable } from './di/interface/defs';
import { getDocument } from './render3/interfaces/document';
/**
 * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.
 *
 * Example:
 *
 * ```
 * const COUNTER_KEY = makeStateKey<number>('counter');
 * let value = 10;
 *
 * transferState.set(COUNTER_KEY, value);
 * ```
 *
 * @publicApi
 */
export function makeStateKey(key) {
    return key;
}
function initTransferState() {
    const transferState = new TransferState();
    if (inject(PLATFORM_ID) === 'browser') {
        transferState.store = retrieveTransferredState(getDocument(), inject(APP_ID));
    }
    return transferState;
}
/**
 * A key value store that is transferred from the application on the server side to the application
 * on the client side.
 *
 * The `TransferState` is available as an injectable token.
 * On the client, just inject this token using DI and use it, it will be lazily initialized.
 * On the server it's already included if `renderApplication` function is used. Otherwise, import
 * the `ServerTransferStateModule` module to make the `TransferState` available.
 *
 * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only
 * boolean, number, string, null and non-class objects will be serialized and deserialized in a
 * non-lossy manner.
 *
 * @publicApi
 */
export class TransferState {
    constructor() {
        /** @internal */
        this.store = {};
        this.onSerializeCallbacks = {};
    }
    /** @nocollapse */
    static { this.ɵprov = 
    /** @pureOrBreakMyCode */ ɵɵdefineInjectable({
        token: TransferState,
        providedIn: 'root',
        factory: initTransferState,
    }); }
    /**
     * Get the value corresponding to a key. Return `defaultValue` if key is not found.
     */
    get(key, defaultValue) {
        return this.store[key] !== undefined ? this.store[key] : defaultValue;
    }
    /**
     * Set the value corresponding to a key.
     */
    set(key, value) {
        this.store[key] = value;
    }
    /**
     * Remove a key from the store.
     */
    remove(key) {
        delete this.store[key];
    }
    /**
     * Test whether a key exists in the store.
     */
    hasKey(key) {
        return this.store.hasOwnProperty(key);
    }
    /**
     * Indicates whether the state is empty.
     */
    get isEmpty() {
        return Object.keys(this.store).length === 0;
    }
    /**
     * Register a callback to provide the value for a key when `toJson` is called.
     */
    onSerialize(key, callback) {
        this.onSerializeCallbacks[key] = callback;
    }
    /**
     * Serialize the current state of the store to JSON.
     */
    toJson() {
        // Call the onSerialize callbacks and put those values into the store.
        for (const key in this.onSerializeCallbacks) {
            if (this.onSerializeCallbacks.hasOwnProperty(key)) {
                try {
                    this.store[key] = this.onSerializeCallbacks[key]();
                }
                catch (e) {
                    console.warn('Exception in onSerialize callback: ', e);
                }
            }
        }
        // Escape script tag to avoid break out of <script> tag in serialized output.
        // Encoding of `<` is the same behaviour as G3 script_builders.
        return JSON.stringify(this.store).replace(/</g, '\\u003C');
    }
}
function retrieveTransferredState(doc, appId) {
    // Locate the script tag with the JSON data transferred from the server.
    // The id of the script tag is set to the Angular appId + 'state'.
    const script = doc.getElementById(appId + '-state');
    if (script?.textContent) {
        try {
            // Avoid using any here as it triggers lint errors in google3 (any is not allowed).
            // Decoding of `<` is done of the box by browsers and node.js, same behaviour as G3
            // script_builders.
            return JSON.parse(script.textContent);
        }
        catch (e) {
            console.warn('Exception while restoring TransferState for app ' + appId, e);
        }
    }
    return {};
}
//# sourceMappingURL=data:application/json;base64,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