package com.hospital.therapist;

import com.hospital.common.BaseEntity;
import com.hospital.patient.Patient;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "therapist")
public class Therapist extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String licenseNumber;
    private String specialization; // Physical Therapy, Occupational Therapy, Speech Therapy, etc.
    private String department;
    private Integer yearsOfExperience;
    
    @Enumerated(EnumType.STRING)
    private TherapistStatus status;
    
    // Therapy-specific fields
    private String certifications; // Board certifications
    private String treatmentSpecialties; // Orthopedic, Neurological, Pediatric, etc.
    private Boolean canProvideHomeVisits;
    private String equipmentCertifications;
    private Integer maxPatientsPerDay;
    
    @ManyToMany
    @JoinTable(
        name = "therapist_patient_assignment",
        joinColumns = @JoinColumn(name = "therapist_id"),
        inverseJoinColumns = @JoinColumn(name = "patient_id")
    )
    private List<Patient> assignedPatients;

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum TherapistStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    SUSPENDED,
    RETIRED
}
