/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export function isEnvironmentProviders(value) {
    return value && !!value.ɵproviders;
}
//# sourceMappingURL=data:application/json;base64,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