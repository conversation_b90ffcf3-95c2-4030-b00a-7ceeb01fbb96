/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertDefined, assertEqual, assertNumber, throwError } from '../util/assert';
import { getComponentDef, getNgModuleDef } from './definition';
import { isLContainer, isLView } from './interfaces/type_checks';
import { DECLARATION_COMPONENT_VIEW, HEADER_OFFSET, T_HOST, TVIEW } from './interfaces/view';
// [Assert functions do not constraint type when they are guarded by a truthy
// expression.](https://github.com/microsoft/TypeScript/issues/37295)
export function assertTNodeForLView(tNode, lView) {
    assertTNodeForTView(tNode, lView[TVIEW]);
}
export function assertTNodeForTView(tNode, tView) {
    assertTNode(tNode);
    const tData = tView.data;
    for (let i = HEADER_OFFSET; i < tData.length; i++) {
        if (tData[i] === tNode) {
            return;
        }
    }
    throwError('This TNode does not belong to this TView.');
}
export function assertTNode(tNode) {
    assertDefined(tNode, 'TNode must be defined');
    if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {
        throwError('Not of type TNode, got: ' + tNode);
    }
}
export function assertTIcu(tIcu) {
    assertDefined(tIcu, 'Expected TIcu to be defined');
    if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {
        throwError('Object is not of TIcu type.');
    }
}
export function assertComponentType(actual, msg = 'Type passed in is not ComponentType, it does not have \'ɵcmp\' property.') {
    if (!getComponentDef(actual)) {
        throwError(msg);
    }
}
export function assertNgModuleType(actual, msg = 'Type passed in is not NgModuleType, it does not have \'ɵmod\' property.') {
    if (!getNgModuleDef(actual)) {
        throwError(msg);
    }
}
export function assertCurrentTNodeIsParent(isParent) {
    assertEqual(isParent, true, 'currentTNode should be a parent');
}
export function assertHasParent(tNode) {
    assertDefined(tNode, 'currentTNode should exist!');
    assertDefined(tNode.parent, 'currentTNode should have a parent');
}
export function assertLContainer(value) {
    assertDefined(value, 'LContainer must be defined');
    assertEqual(isLContainer(value), true, 'Expecting LContainer');
}
export function assertLViewOrUndefined(value) {
    value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');
}
export function assertLView(value) {
    assertDefined(value, 'LView must be defined');
    assertEqual(isLView(value), true, 'Expecting LView');
}
export function assertFirstCreatePass(tView, errMessage) {
    assertEqual(tView.firstCreatePass, true, errMessage || 'Should only be called in first create pass.');
}
export function assertFirstUpdatePass(tView, errMessage) {
    assertEqual(tView.firstUpdatePass, true, errMessage || 'Should only be called in first update pass.');
}
/**
 * This is a basic sanity check that an object is probably a directive def. DirectiveDef is
 * an interface, so we can't do a direct instanceof check.
 */
export function assertDirectiveDef(obj) {
    if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {
        throwError(`Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`);
    }
}
export function assertIndexInDeclRange(lView, index) {
    const tView = lView[1];
    assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);
}
export function assertIndexInExpandoRange(lView, index) {
    const tView = lView[1];
    assertBetween(tView.expandoStartIndex, lView.length, index);
}
export function assertBetween(lower, upper, index) {
    if (!(lower <= index && index < upper)) {
        throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);
    }
}
export function assertProjectionSlots(lView, errMessage) {
    assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');
    assertDefined(lView[DECLARATION_COMPONENT_VIEW][T_HOST].projection, errMessage ||
        'Components with projection nodes (<ng-content>) must have projection slots defined.');
}
export function assertParentView(lView, errMessage) {
    assertDefined(lView, errMessage || 'Component views should always have a parent view (component\'s host view)');
}
/**
 * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a
 * NodeInjector data structure.
 *
 * @param lView `LView` which should be checked.
 * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.
 */
export function assertNodeInjector(lView, injectorIndex) {
    assertIndexInExpandoRange(lView, injectorIndex);
    assertIndexInExpandoRange(lView, injectorIndex + 8 /* NodeInjectorOffset.PARENT */);
    assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 8 /* NodeInjectorOffset.PARENT */], 'injectorIndex should point to parent injector');
}
//# sourceMappingURL=data:application/json;base64,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