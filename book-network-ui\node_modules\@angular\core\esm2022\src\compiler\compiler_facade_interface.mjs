/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export var FactoryTarget;
(function (FactoryTarget) {
    FactoryTarget[FactoryTarget["Directive"] = 0] = "Directive";
    FactoryTarget[FactoryTarget["Component"] = 1] = "Component";
    FactoryTarget[FactoryTarget["Injectable"] = 2] = "Injectable";
    FactoryTarget[FactoryTarget["Pipe"] = 3] = "Pipe";
    FactoryTarget[FactoryTarget["NgModule"] = 4] = "NgModule";
})(FactoryTarget || (FactoryTarget = {}));
export var R3TemplateDependencyKind;
(function (R3TemplateDependencyKind) {
    R3TemplateDependencyKind[R3TemplateDependencyKind["Directive"] = 0] = "Directive";
    R3TemplateDependencyKind[R3TemplateDependencyKind["Pipe"] = 1] = "Pipe";
    R3TemplateDependencyKind[R3TemplateDependencyKind["NgModule"] = 2] = "NgModule";
})(R3TemplateDependencyKind || (R3TemplateDependencyKind = {}));
export var ViewEncapsulation;
(function (ViewEncapsulation) {
    ViewEncapsulation[ViewEncapsulation["Emulated"] = 0] = "Emulated";
    // Historically the 1 value was for `Native` encapsulation which has been removed as of v11.
    ViewEncapsulation[ViewEncapsulation["None"] = 2] = "None";
    ViewEncapsulation[ViewEncapsulation["ShadowDom"] = 3] = "ShadowDom";
})(ViewEncapsulation || (ViewEncapsulation = {}));
//# sourceMappingURL=data:application/json;base64,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