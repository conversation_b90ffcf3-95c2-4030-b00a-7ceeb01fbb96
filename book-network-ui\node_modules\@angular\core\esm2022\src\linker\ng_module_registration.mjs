/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { stringify } from '../util/stringify';
/**
 * Map of module-id to the corresponding NgModule.
 */
const modules = new Map();
/**
 * Whether to check for duplicate NgModule registrations.
 *
 * This can be disabled for testing.
 */
let checkForDuplicateNgModules = true;
function assertSameOrNotExisting(id, type, incoming) {
    if (type && type !== incoming && checkForDuplicateNgModules) {
        throw new Error(`Duplicate module registered for ${id} - ${stringify(type)} vs ${stringify(type.name)}`);
    }
}
/**
 * Adds the given NgModule type to Angular's NgModule registry.
 *
 * This is generated as a side-effect of NgModule compilation. Note that the `id` is passed in
 * explicitly and not read from the NgModule definition. This is for two reasons: it avoids a
 * megamorphic read, and in JIT there's a chicken-and-egg problem where the NgModule may not be
 * fully resolved when it's registered.
 *
 * @codeGenApi
 */
export function registerNgModuleType(ngModuleType, id) {
    const existing = modules.get(id) || null;
    assertSameOrNotExisting(id, existing, ngModuleType);
    modules.set(id, ngModuleType);
}
export function clearModulesForTest() {
    modules.clear();
}
export function getRegisteredNgModuleType(id) {
    return modules.get(id);
}
/**
 * Control whether the NgModule registration system enforces that each NgModule type registered has
 * a unique id.
 *
 * This is useful for testing as the NgModule registry cannot be properly reset between tests with
 * Angular's current API.
 */
export function setAllowDuplicateNgModuleIdsForTest(allowDuplicates) {
    checkForDuplicateNgModules = !allowDuplicates;
}
//# sourceMappingURL=data:application/json;base64,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