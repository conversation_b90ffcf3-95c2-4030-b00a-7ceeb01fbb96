/**
 * Converts `TNodeType` into human readable text.
 * Make sure this matches with `TNodeType`
 */
export function toTNodeTypeAsString(tNodeType) {
    let text = '';
    (tNodeType & 1 /* TNodeType.Text */) && (text += '|Text');
    (tNodeType & 2 /* TNodeType.Element */) && (text += '|Element');
    (tNodeType & 4 /* TNodeType.Container */) && (text += '|Container');
    (tNodeType & 8 /* TNodeType.ElementContainer */) && (text += '|ElementContainer');
    (tNodeType & 16 /* TNodeType.Projection */) && (text += '|Projection');
    (tNodeType & 32 /* TNodeType.Icu */) && (text += '|IcuContainer');
    (tNodeType & 64 /* TNodeType.Placeholder */) && (text += '|Placeholder');
    return text.length > 0 ? text.substring(1) : text;
}
// Note: This hack is necessary so we don't erroneously get a circular dependency
// failure based on types.
export const unusedValueExportToPlacateAjd = 1;
/**
 * Returns `true` if the `TNode` has a directive which has `@Input()` for `class` binding.
 *
 * ```
 * <div my-dir [class]="exp"></div>
 * ```
 * and
 * ```
 * @Directive({
 * })
 * class MyDirective {
 *   @Input()
 *   class: string;
 * }
 * ```
 *
 * In the above case it is necessary to write the reconciled styling information into the
 * directive's input.
 *
 * @param tNode
 */
export function hasClassInput(tNode) {
    return (tNode.flags & 8 /* TNodeFlags.hasClassInput */) !== 0;
}
/**
 * Returns `true` if the `TNode` has a directive which has `@Input()` for `style` binding.
 *
 * ```
 * <div my-dir [style]="exp"></div>
 * ```
 * and
 * ```
 * @Directive({
 * })
 * class MyDirective {
 *   @Input()
 *   class: string;
 * }
 * ```
 *
 * In the above case it is necessary to write the reconciled styling information into the
 * directive's input.
 *
 * @param tNode
 */
export function hasStyleInput(tNode) {
    return (tNode.flags & 16 /* TNodeFlags.hasStyleInput */) !== 0;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibm9kZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL3JlbmRlcjMvaW50ZXJmYWNlcy9ub2RlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQWtGQTs7O0dBR0c7QUFDSCxNQUFNLFVBQVUsbUJBQW1CLENBQUMsU0FBb0I7SUFDdEQsSUFBSSxJQUFJLEdBQUcsRUFBRSxDQUFDO0lBQ2QsQ0FBQyxTQUFTLHlCQUFpQixDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksT0FBTyxDQUFDLENBQUM7SUFDbEQsQ0FBQyxTQUFTLDRCQUFvQixDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksVUFBVSxDQUFDLENBQUM7SUFDeEQsQ0FBQyxTQUFTLDhCQUFzQixDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksWUFBWSxDQUFDLENBQUM7SUFDNUQsQ0FBQyxTQUFTLHFDQUE2QixDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksbUJBQW1CLENBQUMsQ0FBQztJQUMxRSxDQUFDLFNBQVMsZ0NBQXVCLENBQUMsSUFBSSxDQUFDLElBQUksSUFBSSxhQUFhLENBQUMsQ0FBQztJQUM5RCxDQUFDLFNBQVMseUJBQWdCLENBQUMsSUFBSSxDQUFDLElBQUksSUFBSSxlQUFlLENBQUMsQ0FBQztJQUN6RCxDQUFDLFNBQVMsaUNBQXdCLENBQUMsSUFBSSxDQUFDLElBQUksSUFBSSxjQUFjLENBQUMsQ0FBQztJQUNoRSxPQUFPLElBQUksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUM7QUFDcEQsQ0FBQztBQTB6QkQsaUZBQWlGO0FBQ2pGLDBCQUEwQjtBQUMxQixNQUFNLENBQUMsTUFBTSw2QkFBNkIsR0FBRyxDQUFDLENBQUM7QUFlL0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0dBb0JHO0FBQ0gsTUFBTSxVQUFVLGFBQWEsQ0FBQyxLQUFZO0lBQ3hDLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxtQ0FBMkIsQ0FBQyxLQUFLLENBQUMsQ0FBQztBQUN4RCxDQUFDO0FBRUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0dBb0JHO0FBQ0gsTUFBTSxVQUFVLGFBQWEsQ0FBQyxLQUFZO0lBQ3hDLE9BQU8sQ0FBQyxLQUFLLENBQUMsS0FBSyxvQ0FBMkIsQ0FBQyxLQUFLLENBQUMsQ0FBQztBQUN4RCxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5pbXBvcnQge0tleVZhbHVlQXJyYXl9IGZyb20gJy4uLy4uL3V0aWwvYXJyYXlfdXRpbHMnO1xuaW1wb3J0IHtUU3R5bGluZ1JhbmdlfSBmcm9tICcuLi9pbnRlcmZhY2VzL3N0eWxpbmcnO1xuXG5pbXBvcnQge1RJY3V9IGZyb20gJy4vaTE4bic7XG5pbXBvcnQge0Nzc1NlbGVjdG9yfSBmcm9tICcuL3Byb2plY3Rpb24nO1xuaW1wb3J0IHtSTm9kZX0gZnJvbSAnLi9yZW5kZXJlcl9kb20nO1xuaW1wb3J0IHtMVmlldywgVFZpZXd9IGZyb20gJy4vdmlldyc7XG5cblxuLyoqXG4gKiBUTm9kZVR5cGUgY29ycmVzcG9uZHMgdG8gdGhlIHtAbGluayBUTm9kZX0gYHR5cGVgIHByb3BlcnR5LlxuICpcbiAqIE5PVEU6IHR5cGUgSURzIGFyZSBzdWNoIHRoYXQgd2UgdXNlIGVhY2ggYml0IHRvIGRlbm90ZSBhIHR5cGUuIFRoaXMgaXMgZG9uZSBzbyB0aGF0IHdlIGNhbiBlYXNpbHlcbiAqIGNoZWNrIGlmIHRoZSBgVE5vZGVgIGlzIG9mIG1vcmUgdGhhbiBvbmUgdHlwZS5cbiAqXG4gKiBgaWYgKHROb2RlLnR5cGUgPT09IFROb2RlVHlwZS5UZXh0IHx8IHROb2RlLnR5cGUgPT09IFROb2RlLkVsZW1lbnQpYFxuICogY2FuIGJlIHdyaXR0ZW4gYXM6XG4gKiBgaWYgKHROb2RlLnR5cGUgJiAoVE5vZGVUeXBlLlRleHQgfCBUTm9kZVR5cGUuRWxlbWVudCkpYFxuICpcbiAqIEhvd2V2ZXIgYW55IGdpdmVuIGBUTm9kZWAgY2FuIG9ubHkgYmUgb2Ygb25lIHR5cGUuXG4gKi9cbmV4cG9ydCBjb25zdCBlbnVtIFROb2RlVHlwZSB7XG4gIC8qKlxuICAgKiBUaGUgVE5vZGUgY29udGFpbnMgaW5mb3JtYXRpb24gYWJvdXQgYSBET00gZWxlbWVudCBha2Ege0BsaW5rIFJUZXh0fS5cbiAgICovXG4gIFRleHQgPSAwYjEsXG5cbiAgLyoqXG4gICAqIFRoZSBUTm9kZSBjb250YWlucyBpbmZvcm1hdGlvbiBhYm91dCBhIERPTSBlbGVtZW50IGFrYSB7QGxpbmsgUkVsZW1lbnR9LlxuICAgKi9cbiAgRWxlbWVudCA9IDBiMTAsXG5cbiAgLyoqXG4gICAqIFRoZSBUTm9kZSBjb250YWlucyBpbmZvcm1hdGlvbiBhYm91dCBhbiB7QGxpbmsgTENvbnRhaW5lcn0gZm9yIGVtYmVkZGVkIHZpZXdzLlxuICAgKi9cbiAgQ29udGFpbmVyID0gMGIxMDAsXG5cbiAgLyoqXG4gICAqIFRoZSBUTm9kZSBjb250YWlucyBpbmZvcm1hdGlvbiBhYm91dCBhbiBgPG5nLWNvbnRhaW5lcj5gIGVsZW1lbnQge0BsaW5rIFJOb2RlfS5cbiAgICovXG4gIEVsZW1lbnRDb250YWluZXIgPSAwYjEwMDAsXG5cbiAgLyoqXG4gICAqIFRoZSBUTm9kZSBjb250YWlucyBpbmZvcm1hdGlvbiBhYm91dCBhbiBgPG5nLWNvbnRlbnQ+YCBwcm9qZWN0aW9uXG4gICAqL1xuICBQcm9qZWN0aW9uID0gMGIxMDAwMCxcblxuICAvKipcbiAgICogVGhlIFROb2RlIGNvbnRhaW5zIGluZm9ybWF0aW9uIGFib3V0IGFuIElDVSBjb21tZW50IHVzZWQgaW4gYGkxOG5gLlxuICAgKi9cbiAgSWN1ID0gMGIxMDAwMDAsXG5cbiAgLyoqXG4gICAqIFNwZWNpYWwgbm9kZSB0eXBlIHJlcHJlc2VudGluZyBhIHBsYWNlaG9sZGVyIGZvciBmdXR1cmUgYFROb2RlYCBhdCB0aGlzIGxvY2F0aW9uLlxuICAgKlxuICAgKiBJMThuIHRyYW5zbGF0aW9uIGJsb2NrcyBhcmUgY3JlYXRlZCBiZWZvcmUgdGhlIGVsZW1lbnQgbm9kZXMgd2hpY2ggdGhleSBjb250YWluLiAoSTE4biBibG9ja3NcbiAgICogY2FuIHNwYW4gb3ZlciBtYW55IGVsZW1lbnRzLikgQmVjYXVzZSBpMThuIGBUTm9kZWBzIChyZXByZXNlbnRpbmcgdGV4dCkgYXJlIGNyZWF0ZWQgZmlyc3QgdGhleVxuICAgKiBvZnRlbiBtYXkgbmVlZCB0byBwb2ludCB0byBlbGVtZW50IGBUTm9kZWBzIHdoaWNoIGFyZSBub3QgeWV0IGNyZWF0ZWQuIEluIHN1Y2ggYSBjYXNlIHdlIGNyZWF0ZVxuICAgKiBhIGBQbGFjZWhvbGRlcmAgYFROb2RlYC4gVGhpcyBhbGxvd3MgdGhlIGkxOG4gdG8gc3RydWN0dXJhbGx5IGxpbmsgdGhlIGBUTm9kZWBzIHRvZ2V0aGVyXG4gICAqIHdpdGhvdXQga25vd2luZyBhbnkgaW5mb3JtYXRpb24gYWJvdXQgdGhlIGZ1dHVyZSBub2RlcyB3aGljaCB3aWxsIGJlIGF0IHRoYXQgbG9jYXRpb24uXG4gICAqXG4gICAqIE9uIGBmaXJzdENyZWF0ZVBhc3NgIFdoZW4gZWxlbWVudCBpbnN0cnVjdGlvbiBleGVjdXRlcyBpdCB3aWxsIHRyeSB0byBjcmVhdGUgYSBgVE5vZGVgIGF0IHRoYXRcbiAgICogbG9jYXRpb24uIFNlZWluZyBhIGBQbGFjZWhvbGRlcmAgYFROb2RlYCBhbHJlYWR5IHRoZXJlIHRlbGxzIHRoZSBzeXN0ZW0gdGhhdCBpdCBzaG91bGQgcmV1c2VcbiAgICogZXhpc3RpbmcgYFROb2RlYCAocmF0aGVyIHRoYW4gY3JlYXRlIGEgbmV3IG9uZSkgYW5kIGp1c3QgdXBkYXRlIHRoZSBtaXNzaW5nIGluZm9ybWF0aW9uLlxuICAgKi9cbiAgUGxhY2Vob2xkZXIgPSAwYjEwMDAwMDAsXG5cbiAgLy8gQ29tYmluZWQgVHlwZXMgVGhlc2Ugc2hvdWxkIG5ldmVyIGJlIHVzZWQgZm9yIGBUTm9kZS50eXBlYCBvbmx5IGFzIGEgdXNlZnVsIHdheSB0byBjaGVja1xuICAvLyBpZiBgVE5vZGUudHlwZWAgaXMgb25lIG9mIHNldmVyYWwgY2hvaWNlcy5cblxuICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb3NvZnQvVHlwZVNjcmlwdC9pc3N1ZXMvMzU4NzUgd2h5IHdlIGNhbid0IHJlZmVyIHRvIGV4aXN0aW5nIGVudW0uXG4gIEFueVJOb2RlID0gMGIxMSwgICAgICAgIC8vIFRleHQgfCBFbGVtZW50LFxuICBBbnlDb250YWluZXIgPSAwYjExMDAsICAvLyBDb250YWluZXIgfCBFbGVtZW50Q29udGFpbmVyLCAvLyBTZWU6XG59XG5cbi8qKlxuICogQ29udmVydHMgYFROb2RlVHlwZWAgaW50byBodW1hbiByZWFkYWJsZSB0ZXh0LlxuICogTWFrZSBzdXJlIHRoaXMgbWF0Y2hlcyB3aXRoIGBUTm9kZVR5cGVgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0b1ROb2RlVHlwZUFzU3RyaW5nKHROb2RlVHlwZTogVE5vZGVUeXBlKTogc3RyaW5nIHtcbiAgbGV0IHRleHQgPSAnJztcbiAgKHROb2RlVHlwZSAmIFROb2RlVHlwZS5UZXh0KSAmJiAodGV4dCArPSAnfFRleHQnKTtcbiAgKHROb2RlVHlwZSAmIFROb2RlVHlwZS5FbGVtZW50KSAmJiAodGV4dCArPSAnfEVsZW1lbnQnKTtcbiAgKHROb2RlVHlwZSAmIFROb2RlVHlwZS5Db250YWluZXIpICYmICh0ZXh0ICs9ICd8Q29udGFpbmVyJyk7XG4gICh0Tm9kZVR5cGUgJiBUTm9kZVR5cGUuRWxlbWVudENvbnRhaW5lcikgJiYgKHRleHQgKz0gJ3xFbGVtZW50Q29udGFpbmVyJyk7XG4gICh0Tm9kZVR5cGUgJiBUTm9kZVR5cGUuUHJvamVjdGlvbikgJiYgKHRleHQgKz0gJ3xQcm9qZWN0aW9uJyk7XG4gICh0Tm9kZVR5cGUgJiBUTm9kZVR5cGUuSWN1KSAmJiAodGV4dCArPSAnfEljdUNvbnRhaW5lcicpO1xuICAodE5vZGVUeXBlICYgVE5vZGVUeXBlLlBsYWNlaG9sZGVyKSAmJiAodGV4dCArPSAnfFBsYWNlaG9sZGVyJyk7XG4gIHJldHVybiB0ZXh0Lmxlbmd0aCA+IDAgPyB0ZXh0LnN1YnN0cmluZygxKSA6IHRleHQ7XG59XG5cbi8qKlxuICogQ29ycmVzcG9uZHMgdG8gdGhlIFROb2RlLmZsYWdzIHByb3BlcnR5LlxuICovXG5leHBvcnQgY29uc3QgZW51bSBUTm9kZUZsYWdzIHtcbiAgLyoqIEJpdCAjMSAtIFRoaXMgYml0IGlzIHNldCBpZiB0aGUgbm9kZSBpcyBhIGhvc3QgZm9yIGFueSBkaXJlY3RpdmUgKGluY2x1ZGluZyBhIGNvbXBvbmVudCkgKi9cbiAgaXNEaXJlY3RpdmVIb3N0ID0gMHgxLFxuXG4gIC8qKiBCaXQgIzIgLSBUaGlzIGJpdCBpcyBzZXQgaWYgdGhlIG5vZGUgaGFzIGJlZW4gcHJvamVjdGVkICovXG4gIGlzUHJvamVjdGVkID0gMHgyLFxuXG4gIC8qKiBCaXQgIzMgLSBUaGlzIGJpdCBpcyBzZXQgaWYgYW55IGRpcmVjdGl2ZSBvbiB0aGlzIG5vZGUgaGFzIGNvbnRlbnQgcXVlcmllcyAqL1xuICBoYXNDb250ZW50UXVlcnkgPSAweDQsXG5cbiAgLyoqIEJpdCAjNCAtIFRoaXMgYml0IGlzIHNldCBpZiB0aGUgbm9kZSBoYXMgYW55IFwiY2xhc3NcIiBpbnB1dHMgKi9cbiAgaGFzQ2xhc3NJbnB1dCA9IDB4OCxcblxuICAvKiogQml0ICM1IC0gVGhpcyBiaXQgaXMgc2V0IGlmIHRoZSBub2RlIGhhcyBhbnkgXCJzdHlsZVwiIGlucHV0cyAqL1xuICBoYXNTdHlsZUlucHV0ID0gMHgxMCxcblxuICAvKiogQml0ICM2IC0gVGhpcyBiaXQgaXMgc2V0IGlmIHRoZSBub2RlIGhhcyBiZWVuIGRldGFjaGVkIGJ5IGkxOG4gKi9cbiAgaXNEZXRhY2hlZCA9IDB4MjAsXG5cbiAgLyoqXG4gICAqIEJpdCAjNyAtIFRoaXMgYml0IGlzIHNldCBpZiB0aGUgbm9kZSBoYXMgZGlyZWN0aXZlcyB3aXRoIGhvc3QgYmluZGluZ3MuXG4gICAqXG4gICAqIFRoaXMgZmxhZ3MgYWxsb3dzIHVzIHRvIGd1YXJkIGhvc3QtYmluZGluZyBsb2dpYyBhbmQgaW52b2tlIGl0IG9ubHkgb24gbm9kZXNcbiAgICogdGhhdCBhY3R1YWxseSBoYXZlIGRpcmVjdGl2ZXMgd2l0aCBob3N0IGJpbmRpbmdzLlxuICAgKi9cbiAgaGFzSG9zdEJpbmRpbmdzID0gMHg0MCxcblxuICAvKipcbiAgICogQml0ICM4IC0gVGhpcyBiaXQgaXMgc2V0IGlmIHRoZSBub2RlIGlzIGEgbG9jYXRlZCBpbnNpZGUgc2tpcCBoeWRyYXRpb24gYmxvY2suXG4gICAqL1xuICBpblNraXBIeWRyYXRpb25CbG9jayA9IDB4ODAsXG59XG5cbi8qKlxuICogQ29ycmVzcG9uZHMgdG8gdGhlIFROb2RlLnByb3ZpZGVySW5kZXhlcyBwcm9wZXJ0eS5cbiAqL1xuZXhwb3J0IGNvbnN0IGVudW0gVE5vZGVQcm92aWRlckluZGV4ZXMge1xuICAvKiogVGhlIGluZGV4IG9mIHRoZSBmaXJzdCBwcm92aWRlciBvbiB0aGlzIG5vZGUgaXMgZW5jb2RlZCBvbiB0aGUgbGVhc3Qgc2lnbmlmaWNhbnQgYml0cy4gKi9cbiAgUHJvdmlkZXJzU3RhcnRJbmRleE1hc2sgPSAwYjAwMDAwMDAwMDAwMDExMTExMTExMTExMTExMTExMTExLFxuXG4gIC8qKlxuICAgKiBUaGUgY291bnQgb2YgdmlldyBwcm92aWRlcnMgZnJvbSB0aGUgY29tcG9uZW50IG9uIHRoaXMgbm9kZSBpc1xuICAgKiBlbmNvZGVkIG9uIHRoZSAyMCBtb3N0IHNpZ25pZmljYW50IGJpdHMuXG4gICAqL1xuICBDcHRWaWV3UHJvdmlkZXJzQ291bnRTaGlmdCA9IDIwLFxuICBDcHRWaWV3UHJvdmlkZXJzQ291bnRTaGlmdGVyID0gMGIwMDAwMDAwMDAwMDEwMDAwMDAwMDAwMDAwMDAwMDAwMCxcbn1cblxuLyoqXG4gKiBBIHNldCBvZiBtYXJrZXIgdmFsdWVzIHRvIGJlIHVzZWQgaW4gdGhlIGF0dHJpYnV0ZXMgYXJyYXlzLiBUaGVzZSBtYXJrZXJzIGluZGljYXRlIHRoYXQgc29tZVxuICogaXRlbXMgYXJlIG5vdCByZWd1bGFyIGF0dHJpYnV0ZXMgYW5kIHRoZSBwcm9jZXNzaW5nIHNob3VsZCBiZSBhZGFwdGVkIGFjY29yZGluZ2x5LlxuICovXG5leHBvcnQgY29uc3QgZW51bSBBdHRyaWJ1dGVNYXJrZXIge1xuICAvKipcbiAgICogQW4gaW1wbGljaXQgbWFya2VyIHdoaWNoIGluZGljYXRlcyB0aGF0IHRoZSB2YWx1ZSBpbiB0aGUgYXJyYXkgYXJlIG9mIGBhdHRyaWJ1dGVLZXlgLFxuICAgKiBgYXR0cmlidXRlVmFsdWVgIGZvcm1hdC5cbiAgICpcbiAgICogTk9URTogVGhpcyBpcyBpbXBsaWNpdCBhcyBpdCBpcyB0aGUgdHlwZSB3aGVuIG5vIG1hcmtlciBpcyBwcmVzZW50IGluIGFycmF5LiBXZSBpbmRpY2F0ZSB0aGF0XG4gICAqIGl0IHNob3VsZCBub3QgYmUgcHJlc2VudCBhdCBydW50aW1lIGJ5IHRoZSBuZWdhdGl2ZSBudW1iZXIuXG4gICAqL1xuICBJbXBsaWNpdEF0dHJpYnV0ZXMgPSAtMSxcblxuICAvKipcbiAgICogTWFya2VyIGluZGljYXRlcyB0aGF0IHRoZSBmb2xsb3dpbmcgMyB2YWx1ZXMgaW4gdGhlIGF0dHJpYnV0ZXMgYXJyYXkgYXJlOlxuICAgKiBuYW1lc3BhY2VVcmksIGF0dHJpYnV0ZU5hbWUsIGF0dHJpYnV0ZVZhbHVlXG4gICAqIGluIHRoYXQgb3JkZXIuXG4gICAqL1xuICBOYW1lc3BhY2VVUkkgPSAwLFxuXG4gIC8qKlxuICAgKiBTaWduYWxzIGNsYXNzIGRlY2xhcmF0aW9uLlxuICAgKlxuICAgKiBFYWNoIHZhbHVlIGZvbGxvd2luZyBgQ2xhc3Nlc2AgZGVzaWduYXRlcyBhIGNsYXNzIG5hbWUgdG8gaW5jbHVkZSBvbiB0aGUgZWxlbWVudC5cbiAgICogIyMgRXhhbXBsZTpcbiAgICpcbiAgICogR2l2ZW46XG4gICAqIGBgYFxuICAgKiA8ZGl2IGNsYXNzPVwiZm9vIGJhciBiYXpcIj4uLi48ZC92aT5cbiAgICogYGBgXG4gICAqXG4gICAqIHRoZSBnZW5lcmF0ZWQgY29kZSBpczpcbiAgICogYGBgXG4gICAqIHZhciBfYzEgPSBbQXR0cmlidXRlTWFya2VyLkNsYXNzZXMsICdmb28nLCAnYmFyJywgJ2JheiddO1xuICAgKiBgYGBcbiAgICovXG4gIENsYXNzZXMgPSAxLFxuXG4gIC8qKlxuICAgKiBTaWduYWxzIHN0eWxlIGRlY2xhcmF0aW9uLlxuICAgKlxuICAgKiBFYWNoIHBhaXIgb2YgdmFsdWVzIGZvbGxvd2luZyBgU3R5bGVzYCBkZXNpZ25hdGVzIGEgc3R5bGUgbmFtZSBhbmQgdmFsdWUgdG8gaW5jbHVkZSBvbiB0aGVcbiAgICogZWxlbWVudC5cbiAgICogIyMgRXhhbXBsZTpcbiAgICpcbiAgICogR2l2ZW46XG4gICAqIGBgYFxuICAgKiA8ZGl2IHN0eWxlPVwid2lkdGg6MTAwcHg7IGhlaWdodDoyMDBweDsgY29sb3I6cmVkXCI+Li4uPC9kaXY+XG4gICAqIGBgYFxuICAgKlxuICAgKiB0aGUgZ2VuZXJhdGVkIGNvZGUgaXM6XG4gICAqIGBgYFxuICAgKiB2YXIgX2MxID0gW0F0dHJpYnV0ZU1hcmtlci5TdHlsZXMsICd3aWR0aCcsICcxMDBweCcsICdoZWlnaHQnLiAnMjAwcHgnLCAnY29sb3InLCAncmVkJ107XG4gICAqIGBgYFxuICAgKi9cbiAgU3R5bGVzID0gMixcblxuICAvKipcbiAgICogU2lnbmFscyB0aGF0IHRoZSBmb2xsb3dpbmcgYXR0cmlidXRlIG5hbWVzIHdlcmUgZXh0cmFjdGVkIGZyb20gaW5wdXQgb3Igb3V0cHV0IGJpbmRpbmdzLlxuICAgKlxuICAgKiBGb3IgZXhhbXBsZSwgZ2l2ZW4gdGhlIGZvbGxvd2luZyBIVE1MOlxuICAgKlxuICAgKiBgYGBcbiAgICogPGRpdiBtb289XCJjYXJcIiBbZm9vXT1cImV4cFwiIChiYXIpPVwiZG9TdGgoKVwiPlxuICAgKiBgYGBcbiAgICpcbiAgICogdGhlIGdlbmVyYXRlZCBjb2RlIGlzOlxuICAgKlxuICAgKiBgYGBcbiAgICogdmFyIF9jMSA9IFsnbW9vJywgJ2NhcicsIEF0dHJpYnV0ZU1hcmtlci5CaW5kaW5ncywgJ2ZvbycsICdiYXInXTtcbiAgICogYGBgXG4gICAqL1xuICBCaW5kaW5ncyA9IDMsXG5cbiAgLyoqXG4gICAqIFNpZ25hbHMgdGhhdCB0aGUgZm9sbG93aW5nIGF0dHJpYnV0ZSBuYW1lcyB3ZXJlIGhvaXN0ZWQgZnJvbSBhbiBpbmxpbmUtdGVtcGxhdGUgZGVjbGFyYXRpb24uXG4gICAqXG4gICAqIEZvciBleGFtcGxlLCBnaXZlbiB0aGUgZm9sbG93aW5nIEhUTUw6XG4gICAqXG4gICAqIGBgYFxuICAgKiA8ZGl2ICpuZ0Zvcj1cImxldCB2YWx1ZSBvZiB2YWx1ZXM7IHRyYWNrQnk6dHJhY2tCeVwiIGRpckEgW2RpckJdPVwidmFsdWVcIj5cbiAgICogYGBgXG4gICAqXG4gICAqIHRoZSBnZW5lcmF0ZWQgY29kZSBmb3IgdGhlIGB0ZW1wbGF0ZSgpYCBpbnN0cnVjdGlvbiB3b3VsZCBpbmNsdWRlOlxuICAgKlxuICAgKiBgYGBcbiAgICogWydkaXJBJywgJycsIEF0dHJpYnV0ZU1hcmtlci5CaW5kaW5ncywgJ2RpckInLCBBdHRyaWJ1dGVNYXJrZXIuVGVtcGxhdGUsICduZ0ZvcicsICduZ0Zvck9mJyxcbiAgICogJ25nRm9yVHJhY2tCeScsICdsZXQtdmFsdWUnXVxuICAgKiBgYGBcbiAgICpcbiAgICogd2hpbGUgdGhlIGdlbmVyYXRlZCBjb2RlIGZvciB0aGUgYGVsZW1lbnQoKWAgaW5zdHJ1Y3Rpb24gaW5zaWRlIHRoZSB0ZW1wbGF0ZSBmdW5jdGlvbiB3b3VsZFxuICAgKiBpbmNsdWRlOlxuICAgKlxuICAgKiBgYGBcbiAgICogWydkaXJBJywgJycsIEF0dHJpYnV0ZU1hcmtlci5CaW5kaW5ncywgJ2RpckInXVxuICAgKiBgYGBcbiAgICovXG4gIFRlbXBsYXRlID0gNCxcblxuICAvKipcbiAgICogU2lnbmFscyB0aGF0IHRoZSBmb2xsb3dpbmcgYXR0cmlidXRlIGlzIGBuZ1Byb2plY3RBc2AgYW5kIGl0cyB2YWx1ZSBpcyBhIHBhcnNlZFxuICAgKiBgQ3NzU2VsZWN0b3JgLlxuICAgKlxuICAgKiBGb3IgZXhhbXBsZSwgZ2l2ZW4gdGhlIGZvbGxvd2luZyBIVE1MOlxuICAgKlxuICAgKiBgYGBcbiAgICogPGgxIGF0dHI9XCJ2YWx1ZVwiIG5nUHJvamVjdEFzPVwiW3RpdGxlXVwiPlxuICAgKiBgYGBcbiAgICpcbiAgICogdGhlIGdlbmVyYXRlZCBjb2RlIGZvciB0aGUgYGVsZW1lbnQoKWAgaW5zdHJ1Y3Rpb24gd291bGQgaW5jbHVkZTpcbiAgICpcbiAgICogYGBgXG4gICAqIFsnYXR0cicsICd2YWx1ZScsIEF0dHJpYnV0ZU1hcmtlci5Qcm9qZWN0QXMsIFsnJywgJ3RpdGxlJywgJyddXVxuICAgKiBgYGBcbiAgICovXG4gIFByb2plY3RBcyA9IDUsXG5cbiAgLyoqXG4gICAqIFNpZ25hbHMgdGhhdCB0aGUgZm9sbG93aW5nIGF0dHJpYnV0ZSB3aWxsIGJlIHRyYW5zbGF0ZWQgYnkgcnVudGltZSBpMThuXG4gICAqXG4gICAqIEZvciBleGFtcGxlLCBnaXZlbiB0aGUgZm9sbG93aW5nIEhUTUw6XG4gICAqXG4gICAqIGBgYFxuICAgKiA8ZGl2IG1vbz1cImNhclwiIGZvbz1cInZhbHVlXCIgaTE4bi1mb28gW2Jhcl09XCJiaW5kaW5nXCIgaTE4bi1iYXI+XG4gICAqIGBgYFxuICAgKlxuICAgKiB0aGUgZ2VuZXJhdGVkIGNvZGUgaXM6XG4gICAqXG4gICAqIGBgYFxuICAgKiB2YXIgX2MxID0gWydtb28nLCAnY2FyJywgQXR0cmlidXRlTWFya2VyLkkxOG4sICdmb28nLCAnYmFyJ107XG4gICAqL1xuICBJMThuID0gNixcbn1cblxuLyoqXG4gKiBBIGNvbWJpbmF0aW9uIG9mOlxuICogLSBBdHRyaWJ1dGUgbmFtZXMgYW5kIHZhbHVlcy5cbiAqIC0gU3BlY2lhbCBtYXJrZXJzIGFjdGluZyBhcyBmbGFncyB0byBhbHRlciBhdHRyaWJ1dGVzIHByb2Nlc3NpbmcuXG4gKiAtIFBhcnNlZCBuZ1Byb2plY3RBcyBzZWxlY3RvcnMuXG4gKi9cbmV4cG9ydCB0eXBlIFRBdHRyaWJ1dGVzID0gKHN0cmluZ3xBdHRyaWJ1dGVNYXJrZXJ8Q3NzU2VsZWN0b3IpW107XG5cbi8qKlxuICogQ29uc3RhbnRzIHRoYXQgYXJlIGFzc29jaWF0ZWQgd2l0aCBhIHZpZXcuIEluY2x1ZGVzOlxuICogLSBBdHRyaWJ1dGUgYXJyYXlzLlxuICogLSBMb2NhbCBkZWZpbml0aW9uIGFycmF5cy5cbiAqIC0gVHJhbnNsYXRlZCBtZXNzYWdlcyAoaTE4bikuXG4gKi9cbmV4cG9ydCB0eXBlIFRDb25zdGFudHMgPSAoVEF0dHJpYnV0ZXN8c3RyaW5nKVtdO1xuXG4vKipcbiAqIEZhY3RvcnkgZnVuY3Rpb24gdGhhdCByZXR1cm5zIGFuIGFycmF5IG9mIGNvbnN0cy4gQ29uc3RzIGNhbiBiZSByZXByZXNlbnRlZCBhcyBhIGZ1bmN0aW9uIGluXG4gKiBjYXNlIGFueSBhZGRpdGlvbmFsIHN0YXRlbWVudHMgYXJlIHJlcXVpcmVkIHRvIGRlZmluZSBjb25zdHMgaW4gdGhlIGxpc3QuIEFuIGV4YW1wbGUgaXMgaTE4blxuICogd2hlcmUgYWRkaXRpb25hbCBpMThuIGNhbGxzIGFyZSBnZW5lcmF0ZWQsIHdoaWNoIHNob3VsZCBiZSBleGVjdXRlZCB3aGVuIGNvbnN0cyBhcmUgcmVxdWVzdGVkXG4gKiBmb3IgdGhlIGZpcnN0IHRpbWUuXG4gKi9cbmV4cG9ydCB0eXBlIFRDb25zdGFudHNGYWN0b3J5ID0gKCkgPT4gVENvbnN0YW50cztcblxuLyoqXG4gKiBUQ29uc3RhbnRzIHR5cGUgdGhhdCBkZXNjcmliZXMgaG93IHRoZSBgY29uc3RzYCBmaWVsZCBpcyBnZW5lcmF0ZWQgb24gQ29tcG9uZW50RGVmOiBpdCBjYW4gYmVcbiAqIGVpdGhlciBhbiBhcnJheSBvciBhIGZhY3RvcnkgZnVuY3Rpb24gdGhhdCByZXR1cm5zIHRoYXQgYXJyYXkuXG4gKi9cbmV4cG9ydCB0eXBlIFRDb25zdGFudHNPckZhY3RvcnkgPSBUQ29uc3RhbnRzfFRDb25zdGFudHNGYWN0b3J5O1xuXG4vKipcbiAqIEJpbmRpbmcgZGF0YSAoZmx5d2VpZ2h0KSBmb3IgYSBwYXJ0aWN1bGFyIG5vZGUgdGhhdCBpcyBzaGFyZWQgYmV0d2VlbiBhbGwgdGVtcGxhdGVzXG4gKiBvZiBhIHNwZWNpZmljIHR5cGUuXG4gKlxuICogSWYgYSBwcm9wZXJ0eSBpczpcbiAqICAgIC0gUHJvcGVydHlBbGlhc2VzOiB0aGF0IHByb3BlcnR5J3MgZGF0YSB3YXMgZ2VuZXJhdGVkIGFuZCB0aGlzIGlzIGl0XG4gKiAgICAtIE51bGw6IHRoYXQgcHJvcGVydHkncyBkYXRhIHdhcyBhbHJlYWR5IGdlbmVyYXRlZCBhbmQgbm90aGluZyB3YXMgZm91bmQuXG4gKiAgICAtIFVuZGVmaW5lZDogdGhhdCBwcm9wZXJ0eSdzIGRhdGEgaGFzIG5vdCB5ZXQgYmVlbiBnZW5lcmF0ZWRcbiAqXG4gKiBzZWU6IGh0dHBzOi8vZW4ud2lraXBlZGlhLm9yZy93aWtpL0ZseXdlaWdodF9wYXR0ZXJuIGZvciBtb3JlIG9uIHRoZSBGbHl3ZWlnaHQgcGF0dGVyblxuICovXG5leHBvcnQgaW50ZXJmYWNlIFROb2RlIHtcbiAgLyoqIFRoZSB0eXBlIG9mIHRoZSBUTm9kZS4gU2VlIFROb2RlVHlwZS4gKi9cbiAgdHlwZTogVE5vZGVUeXBlO1xuXG4gIC8qKlxuICAgKiBJbmRleCBvZiB0aGUgVE5vZGUgaW4gVFZpZXcuZGF0YSBhbmQgY29ycmVzcG9uZGluZyBuYXRpdmUgZWxlbWVudCBpbiBMVmlldy5cbiAgICpcbiAgICogVGhpcyBpcyBuZWNlc3NhcnkgdG8gZ2V0IGZyb20gYW55IFROb2RlIHRvIGl0cyBjb3JyZXNwb25kaW5nIG5hdGl2ZSBlbGVtZW50IHdoZW5cbiAgICogdHJhdmVyc2luZyB0aGUgbm9kZSB0cmVlLlxuICAgKlxuICAgKiBJZiBpbmRleCBpcyAtMSwgdGhpcyBpcyBhIGR5bmFtaWNhbGx5IGNyZWF0ZWQgY29udGFpbmVyIG5vZGUgb3IgZW1iZWRkZWQgdmlldyBub2RlLlxuICAgKi9cbiAgaW5kZXg6IG51bWJlcjtcblxuICAvKipcbiAgICogSW5zZXJ0IGJlZm9yZSBleGlzdGluZyBET00gbm9kZSBpbmRleC5cbiAgICpcbiAgICogV2hlbiBET00gbm9kZXMgYXJlIGJlaW5nIGluc2VydGVkLCBub3JtYWxseSB0aGV5IGFyZSBiZWluZyBhcHBlbmRlZCBhcyB0aGV5IGFyZSBjcmVhdGVkLlxuICAgKiBVbmRlciBpMThuIGNhc2UsIHRoZSB0cmFuc2xhdGVkIHRleHQgbm9kZXMgYXJlIGNyZWF0ZWQgYWhlYWQgb2YgdGltZSBhcyBwYXJ0IG9mIHRoZVxuICAgKiBgybXJtWkxOG5TdGFydGAgaW5zdHJ1Y3Rpb24gd2hpY2ggbWVhbnMgdGhhdCB0aGlzIGBUTm9kZWAgY2FuJ3QganVzdCBiZSBhcHBlbmRlZCBhbmQgaW5zdGVhZFxuICAgKiBuZWVkcyB0byBiZSBpbnNlcnRlZCB1c2luZyBgaW5zZXJ0QmVmb3JlSW5kZXhgIHNlbWFudGljcy5cbiAgICpcbiAgICogQWRkaXRpb25hbGx5IHNvbWV0aW1lcyBpdCBpcyBuZWNlc3NhcnkgdG8gaW5zZXJ0IG5ldyB0ZXh0IG5vZGVzIGFzIGEgY2hpbGQgb2YgdGhpcyBgVE5vZGVgLiBJblxuICAgKiBzdWNoIGEgY2FzZSB0aGUgdmFsdWUgc3RvcmVzIGFuIGFycmF5IG9mIHRleHQgbm9kZXMgdG8gaW5zZXJ0LlxuICAgKlxuICAgKiBFeGFtcGxlOlxuICAgKiBgYGBcbiAgICogPGRpdiBpMThuPlxuICAgKiAgIEhlbGxvIDxzcGFuPldvcmxkPC9zcGFuPiFcbiAgICogPC9kaXY+XG4gICAqIGBgYFxuICAgKiBJbiB0aGUgYWJvdmUgZXhhbXBsZSB0aGUgYMm1ybVpMThuU3RhcnRgIGluc3RydWN0aW9uIGNhbiBjcmVhdGUgYEhlbGxvIGAsIGBXb3JsZGAgYW5kIGAhYCB0ZXh0XG4gICAqIG5vZGVzLiBJdCBjYW4gYWxzbyBpbnNlcnQgYEhlbGxvIGAgYW5kIGAhYCB0ZXh0IG5vZGUgYXMgYSBjaGlsZCBvZiBgPGRpdj5gLCBidXQgaXQgY2FuJ3RcbiAgICogaW5zZXJ0IGBXb3JsZGAgYmVjYXVzZSB0aGUgYDxzcGFuPmAgbm9kZSBoYXMgbm90IHlldCBiZWVuIGNyZWF0ZWQuIEluIHN1Y2ggYSBjYXNlIHRoZVxuICAgKiBgPHNwYW4+YCBgVE5vZGVgIHdpbGwgaGF2ZSBhbiBhcnJheSB3aGljaCB3aWxsIGRpcmVjdCB0aGUgYDxzcGFuPmAgdG8gbm90IG9ubHkgaW5zZXJ0XG4gICAqIGl0c2VsZiBpbiBmcm9udCBvZiBgIWAgYnV0IGFsc28gdG8gaW5zZXJ0IHRoZSBgV29ybGRgIChjcmVhdGVkIGJ5IGDJtcm1aTE4blN0YXJ0YCkgaW50b1xuICAgKiBgPHNwYW4+YCBpdHNlbGYuXG4gICAqXG4gICAqIFBzZXVkbyBjb2RlOlxuICAgKiBgYGBcbiAgICogICBpZiAoaW5zZXJ0QmVmb3JlSW5kZXggPT09IG51bGwpIHtcbiAgICogICAgIC8vIGFwcGVuZCBhcyBub3JtYWxcbiAgICogICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoaW5zZXJ0QmVmb3JlSW5kZXgpKSB7XG4gICAqICAgICAvLyBGaXJzdCBpbnNlcnQgY3VycmVudCBgVE5vZGVgIGF0IGNvcnJlY3QgbG9jYXRpb25cbiAgICogICAgIGNvbnN0IGN1cnJlbnROb2RlID0gbFZpZXdbdGhpcy5pbmRleF07XG4gICAqICAgICBwYXJlbnROb2RlLmluc2VydEJlZm9yZShjdXJyZW50Tm9kZSwgbFZpZXdbdGhpcy5pbnNlcnRCZWZvcmVJbmRleFswXV0pO1xuICAgKiAgICAgLy8gTm93IGFwcGVuZCBhbGwgb2YgdGhlIGNoaWxkcmVuXG4gICAqICAgICBmb3IobGV0IGk9MTsgaTx0aGlzLmluc2VydEJlZm9yZUluZGV4OyBpKyspIHtcbiAgICogICAgICAgY3VycmVudE5vZGUuYXBwZW5kQ2hpbGQobFZpZXdbdGhpcy5pbnNlcnRCZWZvcmVJbmRleFtpXV0pO1xuICAgKiAgICAgfVxuICAgKiAgIH0gZWxzZSB7XG4gICAqICAgICBwYXJlbnROb2RlLmluc2VydEJlZm9yZShsVmlld1t0aGlzLmluZGV4XSwgbFZpZXdbdGhpcy5pbnNlcnRCZWZvcmVJbmRleF0pXG4gICAqICAgfVxuICAgKiBgYGBcbiAgICogLSBudWxsOiBBcHBlbmQgYXMgbm9ybWFsIHVzaW5nIGBwYXJlbnROb2RlLmFwcGVuZENoaWxkYFxuICAgKiAtIGBudW1iZXJgOiBBcHBlbmQgdXNpbmdcbiAgICogICAgICBgcGFyZW50Tm9kZS5pbnNlcnRCZWZvcmUobFZpZXdbdGhpcy5pbmRleF0sIGxWaWV3W3RoaXMuaW5zZXJ0QmVmb3JlSW5kZXhdKWBcbiAgICpcbiAgICogKkluaXRpYWxpemF0aW9uKlxuICAgKlxuICAgKiBCZWNhdXNlIGDJtcm1aTE4blN0YXJ0YCBleGVjdXRlcyBiZWZvcmUgbm9kZXMgYXJlIGNyZWF0ZWQsIG9uIGBUVmlldy5maXJzdENyZWF0ZVBhc3NgIGl0IGlzIG5vdFxuICAgKiBwb3NzaWJsZSBmb3IgYMm1ybVpMThuU3RhcnRgIHRvIHNldCB0aGUgYGluc2VydEJlZm9yZUluZGV4YCB2YWx1ZSBhcyB0aGUgY29ycmVzcG9uZGluZyBgVE5vZGVgXG4gICAqIGhhcyBub3QgeWV0IGJlZW4gY3JlYXRlZC4gRm9yIHRoaXMgcmVhc29uIHRoZSBgybXJtWkxOG5TdGFydGAgY3JlYXRlcyBhIGBUTm9kZVR5cGUuUGxhY2Vob2xkZXJgXG4gICAqIGBUTm9kZWAgYXQgdGhhdCBsb2NhdGlvbi4gU2VlIGBUTm9kZVR5cGUuUGxhY2Vob2xkZXJgIGZvciBtb3JlIGluZm9ybWF0aW9uLlxuICAgKi9cbiAgaW5zZXJ0QmVmb3JlSW5kZXg6IEluc2VydEJlZm9yZUluZGV4O1xuXG4gIC8qKlxuICAgKiBUaGUgaW5kZXggb2YgdGhlIGNsb3Nlc3QgaW5qZWN0b3IgaW4gdGhpcyBub2RlJ3MgTFZpZXcuXG4gICAqXG4gICAqIElmIHRoZSBpbmRleCA9PT0gLTEsIHRoZXJlIGlzIG5vIGluamVjdG9yIG9uIHRoaXMgbm9kZSBvciBhbnkgYW5jZXN0b3Igbm9kZSBpbiB0aGlzIHZpZXcuXG4gICAqXG4gICAqIElmIHRoZSBpbmRleCAhPT0gLTEsIGl0IGlzIHRoZSBpbmRleCBvZiB0aGlzIG5vZGUncyBpbmplY3RvciBPUiB0aGUgaW5kZXggb2YgYSBwYXJlbnRcbiAgICogaW5qZWN0b3IgaW4gdGhlIHNhbWUgdmlldy4gV2UgcGFzcyB0aGUgcGFyZW50IGluamVjdG9yIGluZGV4IGRvd24gdGhlIG5vZGUgdHJlZSBvZiBhIHZpZXcgc29cbiAgICogaXQncyBwb3NzaWJsZSB0byBmaW5kIHRoZSBwYXJlbnQgaW5qZWN0b3Igd2l0aG91dCB3YWxraW5nIGEgcG90ZW50aWFsbHkgZGVlcCBub2RlIHRyZWUuXG4gICAqIEluamVjdG9yIGluZGljZXMgYXJlIG5vdCBzZXQgYWNyb3NzIHZpZXcgYm91bmRhcmllcyBiZWNhdXNlIHRoZXJlIGNvdWxkIGJlIG11bHRpcGxlIGNvbXBvbmVudFxuICAgKiBob3N0cy5cbiAgICpcbiAgICogSWYgdE5vZGUuaW5qZWN0b3JJbmRleCA9PT0gdE5vZGUucGFyZW50LmluamVjdG9ySW5kZXgsIHRoZW4gdGhlIGluZGV4IGJlbG9uZ3MgdG8gYSBwYXJlbnRcbiAgICogaW5qZWN0b3IuXG4gICAqL1xuICBpbmplY3RvckluZGV4OiBudW1iZXI7XG5cbiAgLyoqIFN0b3JlcyBzdGFydGluZyBpbmRleCBvZiB0aGUgZGlyZWN0aXZlcy4gKi9cbiAgZGlyZWN0aXZlU3RhcnQ6IG51bWJlcjtcblxuICAvKipcbiAgICogU3RvcmVzIGZpbmFsIGV4Y2x1c2l2ZSBpbmRleCBvZiB0aGUgZGlyZWN0aXZlcy5cbiAgICpcbiAgICogVGhlIGFyZWEgcmlnaHQgYmVoaW5kIHRoZSBgZGlyZWN0aXZlU3RhcnQtZGlyZWN0aXZlRW5kYCByYW5nZSBpcyB1c2VkIHRvIGFsbG9jYXRlIHRoZVxuICAgKiBgSG9zdEJpbmRpbmdGdW5jdGlvbmAgYHZhcnNgIChvciBudWxsIGlmIG5vIGJpbmRpbmdzLikgVGhlcmVmb3JlIGBkaXJlY3RpdmVFbmRgIGlzIHVzZWQgdG8gc2V0XG4gICAqIGBMRnJhbWUuYmluZGluZ1Jvb3RJbmRleGAgYmVmb3JlIGBIb3N0QmluZGluZ0Z1bmN0aW9uYCBpcyBleGVjdXRlZC5cbiAgICovXG4gIGRpcmVjdGl2ZUVuZDogbnVtYmVyO1xuXG4gIC8qKlxuICAgKiBPZmZzZXQgZnJvbSB0aGUgYGRpcmVjdGl2ZVN0YXJ0YCBhdCB3aGljaCB0aGUgY29tcG9uZW50IChvbmUgYXQgbW9zdCkgb2YgdGhlIG5vZGUgaXMgc3RvcmVkLlxuICAgKiBTZXQgdG8gLTEgaWYgbm8gY29tcG9uZW50cyBoYXZlIGJlZW4gYXBwbGllZCB0byB0aGUgbm9kZS4gQ29tcG9uZW50IGluZGV4IGNhbiBiZSBmb3VuZCB1c2luZ1xuICAgKiBgZGlyZWN0aXZlU3RhcnQgKyBjb21wb25lbnRPZmZzZXRgLlxuICAgKi9cbiAgY29tcG9uZW50T2Zmc2V0OiBudW1iZXI7XG5cbiAgLyoqXG4gICAqIFN0b3JlcyB0aGUgbGFzdCBkaXJlY3RpdmUgd2hpY2ggaGFkIGEgc3R5bGluZyBpbnN0cnVjdGlvbi5cbiAgICpcbiAgICogSW5pdGlhbCB2YWx1ZSBvZiB0aGlzIGlzIGAtMWAgd2hpY2ggbWVhbnMgdGhhdCBubyBgaG9zdEJpbmRpbmdzYCBzdHlsaW5nIGluc3RydWN0aW9uIGhhc1xuICAgKiBleGVjdXRlZC4gQXMgYGhvc3RCaW5kaW5nc2AgaW5zdHJ1Y3Rpb25zIGV4ZWN1dGUgdGhleSBzZXQgdGhlIHZhbHVlIHRvIHRoZSBpbmRleCBvZiB0aGVcbiAgICogYERpcmVjdGl2ZURlZmAgd2hpY2ggY29udGFpbmVkIHRoZSBsYXN0IGBob3N0QmluZGluZ3NgIHN0eWxpbmcgaW5zdHJ1Y3Rpb24uXG4gICAqXG4gICAqIFZhbGlkIHZhbHVlcyBhcmU6XG4gICAqIC0gYC0xYCBObyBgaG9zdEJpbmRpbmdzYCBpbnN0cnVjdGlvbiBoYXMgZXhlY3V0ZWQuXG4gICAqIC0gYGRpcmVjdGl2ZVN0YXJ0IDw9IGRpcmVjdGl2ZVN0eWxpbmdMYXN0IDwgZGlyZWN0aXZlRW5kYDogUG9pbnRzIHRvIHRoZSBgRGlyZWN0aXZlRGVmYCBvZlxuICAgKiB0aGUgbGFzdCBzdHlsaW5nIGluc3RydWN0aW9uIHdoaWNoIGV4ZWN1dGVkIGluIHRoZSBgaG9zdEJpbmRpbmdzYC5cbiAgICpcbiAgICogVGhpcyBkYXRhIGlzIG5lZWRlZCBzbyB0aGF0IHN0eWxpbmcgaW5zdHJ1Y3Rpb25zIGtub3cgd2hpY2ggc3RhdGljIHN0eWxpbmcgZGF0YSBuZWVkcyB0byBiZVxuICAgKiBjb2xsZWN0ZWQgZnJvbSB0aGUgYERpcmVjdGl2ZURlZi5ob3N0QXR0cnNgLiBBIHN0eWxpbmcgaW5zdHJ1Y3Rpb24gbmVlZHMgdG8gY29sbGVjdCBhbGwgZGF0YVxuICAgKiBzaW5jZSBsYXN0IHN0eWxpbmcgaW5zdHJ1Y3Rpb24uXG4gICAqL1xuICBkaXJlY3RpdmVTdHlsaW5nTGFzdDogbnVtYmVyO1xuXG4gIC8qKlxuICAgKiBTdG9yZXMgaW5kZXhlcyBvZiBwcm9wZXJ0eSBiaW5kaW5ncy4gVGhpcyBmaWVsZCBpcyBvbmx5IHNldCBpbiB0aGUgbmdEZXZNb2RlIGFuZCBob2xkc1xuICAgKiBpbmRleGVzIG9mIHByb3BlcnR5IGJpbmRpbmdzIHNvIFRlc3RCZWQgY2FuIGdldCBib3VuZCBwcm9wZXJ0eSBtZXRhZGF0YSBmb3IgYSBnaXZlbiBub2RlLlxuICAgKi9cbiAgcHJvcGVydHlCaW5kaW5nczogbnVtYmVyW118bnVsbDtcblxuICAvKipcbiAgICogU3RvcmVzIGlmIE5vZGUgaXNDb21wb25lbnQsIGlzUHJvamVjdGVkLCBoYXNDb250ZW50UXVlcnksIGhhc0NsYXNzSW5wdXQgYW5kIGhhc1N0eWxlSW5wdXRcbiAgICogZXRjLlxuICAgKi9cbiAgZmxhZ3M6IFROb2RlRmxhZ3M7XG5cbiAgLyoqXG4gICAqIFRoaXMgbnVtYmVyIHN0b3JlcyB0d28gdmFsdWVzIHVzaW5nIGl0cyBiaXRzOlxuICAgKlxuICAgKiAtIHRoZSBpbmRleCBvZiB0aGUgZmlyc3QgcHJvdmlkZXIgb24gdGhhdCBub2RlIChmaXJzdCAxNiBiaXRzKVxuICAgKiAtIHRoZSBjb3VudCBvZiB2aWV3IHByb3ZpZGVycyBmcm9tIHRoZSBjb21wb25lbnQgb24gdGhpcyBub2RlIChsYXN0IDE2IGJpdHMpXG4gICAqL1xuICAvLyBUT0RPKG1pc2tvKTogYnJlYWsgdGhpcyBpbnRvIGFjdHVhbCB2YXJzLlxuICBwcm92aWRlckluZGV4ZXM6IFROb2RlUHJvdmlkZXJJbmRleGVzO1xuXG4gIC8qKlxuICAgKiBUaGUgdmFsdWUgbmFtZSBhc3NvY2lhdGVkIHdpdGggdGhpcyBub2RlLlxuICAgKiBpZiB0eXBlOlxuICAgKiAgIGBUTm9kZVR5cGUuVGV4dGA6IHRleHQgdmFsdWVcbiAgICogICBgVE5vZGVUeXBlLkVsZW1lbnRgOiB0YWcgbmFtZVxuICAgKiAgIGBUTm9kZVR5cGUuSUNVQ29udGFpbmVyYDogYFRJY3VgXG4gICAqL1xuICB2YWx1ZTogYW55O1xuXG4gIC8qKlxuICAgKiBBdHRyaWJ1dGVzIGFzc29jaWF0ZWQgd2l0aCBhbiBlbGVtZW50LiBXZSBuZWVkIHRvIHN0b3JlIGF0dHJpYnV0ZXMgdG8gc3VwcG9ydCB2YXJpb3VzXG4gICAqIHVzZS1jYXNlcyAoYXR0cmlidXRlIGluamVjdGlvbiwgY29udGVudCBwcm9qZWN0aW9uIHdpdGggc2VsZWN0b3JzLCBkaXJlY3RpdmVzIG1hdGNoaW5nKS5cbiAgICogQXR0cmlidXRlcyBhcmUgc3RvcmVkIHN0YXRpY2FsbHkgYmVjYXVzZSByZWFkaW5nIHRoZW0gZnJvbSB0aGUgRE9NIHdvdWxkIGJlIHdheSB0b28gc2xvdyBmb3JcbiAgICogY29udGVudCBwcm9qZWN0aW9uIGFuZCBxdWVyaWVzLlxuICAgKlxuICAgKiBTaW5jZSBhdHRycyB3aWxsIGFsd2F5cyBiZSBjYWxjdWxhdGVkIGZpcnN0LCB0aGV5IHdpbGwgbmV2ZXIgbmVlZCB0byBiZSBtYXJrZWQgdW5kZWZpbmVkIGJ5XG4gICAqIG90aGVyIGluc3RydWN0aW9ucy5cbiAgICpcbiAgICogRm9yIHJlZ3VsYXIgYXR0cmlidXRlcyBhIG5hbWUgb2YgYW4gYXR0cmlidXRlIGFuZCBpdHMgdmFsdWUgYWx0ZXJuYXRlIGluIHRoZSBhcnJheS5cbiAgICogZS5nLiBbJ3JvbGUnLCAnY2hlY2tib3gnXVxuICAgKiBUaGlzIGFycmF5IGNhbiBjb250YWluIGZsYWdzIHRoYXQgd2lsbCBpbmRpY2F0ZSBcInNwZWNpYWwgYXR0cmlidXRlc1wiIChhdHRyaWJ1dGVzIHdpdGhcbiAgICogbmFtZXNwYWNlcywgYXR0cmlidXRlcyBleHRyYWN0ZWQgZnJvbSBiaW5kaW5ncyBhbmQgb3V0cHV0cykuXG4gICAqL1xuICBhdHRyczogVEF0dHJpYnV0ZXN8bnVsbDtcblxuICAvKipcbiAgICogU2FtZSBhcyBgVE5vZGUuYXR0cnNgIGJ1dCBjb250YWlucyBtZXJnZWQgZGF0YSBhY3Jvc3MgYWxsIGRpcmVjdGl2ZSBob3N0IGJpbmRpbmdzLlxuICAgKlxuICAgKiBXZSBuZWVkIHRvIGtlZXAgYGF0dHJzYCBhcyB1bm1lcmdlZCBzbyB0aGF0IGl0IGNhbiBiZSB1c2VkIGZvciBhdHRyaWJ1dGUgc2VsZWN0b3JzLlxuICAgKiBXZSBtZXJnZSBhdHRycyBoZXJlIHNvIHRoYXQgaXQgY2FuIGJlIHVzZWQgaW4gYSBwZXJmb3JtYW50IHdheSBmb3IgaW5pdGlhbCByZW5kZXJpbmcuXG4gICAqXG4gICAqIFRoZSBgYXR0cnNgIGFyZSBtZXJnZWQgaW4gZmlyc3QgcGFzcyBpbiBmb2xsb3dpbmcgb3JkZXI6XG4gICAqIC0gQ29tcG9uZW50J3MgYGhvc3RBdHRyc2BcbiAgICogLSBEaXJlY3RpdmVzJyBgaG9zdEF0dHJzYFxuICAgKiAtIFRlbXBsYXRlIGBUTm9kZS5hdHRyc2AgYXNzb2NpYXRlZCB3aXRoIHRoZSBjdXJyZW50IGBUTm9kZWAuXG4gICAqL1xuICBtZXJnZWRBdHRyczogVEF0dHJpYnV0ZXN8bnVsbDtcblxuICAvKipcbiAgICogQSBzZXQgb2YgbG9jYWwgbmFtZXMgdW5kZXIgd2hpY2ggYSBnaXZlbiBlbGVtZW50IGlzIGV4cG9ydGVkIGluIGEgdGVtcGxhdGUgYW5kXG4gICAqIHZpc2libGUgdG8gcXVlcmllcy4gQW4gZW50cnkgaW4gdGhpcyBhcnJheSBjYW4gYmUgY3JlYXRlZCBmb3IgZGlmZmVyZW50IHJlYXNvbnM6XG4gICAqIC0gYW4gZWxlbWVudCBpdHNlbGYgaXMgcmVmZXJlbmNlZCwgZXguOiBgPGRpdiAjZm9vPmBcbiAgICogLSBhIGNvbXBvbmVudCBpcyByZWZlcmVuY2VkLCBleC46IGA8bXktY21wdCAjZm9vPmBcbiAgICogLSBhIGRpcmVjdGl2ZSBpcyByZWZlcmVuY2VkLCBleC46IGA8bXktY21wdCAjZm9vPVwiZGlyZWN0aXZlRXhwb3J0QXNcIj5gLlxuICAgKlxuICAgKiBBIGdpdmVuIGVsZW1lbnQgbWlnaHQgaGF2ZSBkaWZmZXJlbnQgbG9jYWwgbmFtZXMgYW5kIHRob3NlIG5hbWVzIGNhbiBiZSBhc3NvY2lhdGVkXG4gICAqIHdpdGggYSBkaXJlY3RpdmUuIFdlIHN0b3JlIGxvY2FsIG5hbWVzIGF0IGV2ZW4gaW5kZXhlcyB3aGlsZSBvZGQgaW5kZXhlcyBhcmUgcmVzZXJ2ZWRcbiAgICogZm9yIGRpcmVjdGl2ZSBpbmRleCBpbiBhIHZpZXcgKG9yIGAtMWAgaWYgdGhlcmUgaXMgbm8gYXNzb2NpYXRlZCBkaXJlY3RpdmUpLlxuICAgKlxuICAgKiBTb21lIGV4YW1wbGVzOlxuICAgKiAtIGA8ZGl2ICNmb28+YCA9PiBgW1wiZm9vXCIsIC0xXWBcbiAgICogLSBgPG15LWNtcHQgI2Zvbz5gID0+IGBbXCJmb29cIiwgbXlDbXB0SWR4XWBcbiAgICogLSBgPG15LWNtcHQgI2ZvbyAjYmFyPVwiZGlyZWN0aXZlRXhwb3J0QXNcIj5gID0+IGBbXCJmb29cIiwgbXlDbXB0SWR4LCBcImJhclwiLCBkaXJlY3RpdmVJZHhdYFxuICAgKiAtIGA8ZGl2ICNmb28gI2Jhcj1cImRpcmVjdGl2ZUV4cG9ydEFzXCI+YCA9PiBgW1wiZm9vXCIsIC0xLCBcImJhclwiLCBkaXJlY3RpdmVJZHhdYFxuICAgKi9cbiAgbG9jYWxOYW1lczogKHN0cmluZ3xudW1iZXIpW118bnVsbDtcblxuICAvKiogSW5mb3JtYXRpb24gYWJvdXQgaW5wdXQgcHJvcGVydGllcyB0aGF0IG5lZWQgdG8gYmUgc2V0IG9uY2UgZnJvbSBhdHRyaWJ1dGUgZGF0YS4gKi9cbiAgaW5pdGlhbElucHV0czogSW5pdGlhbElucHV0RGF0YXxudWxsfHVuZGVmaW5lZDtcblxuICAvKipcbiAgICogSW5wdXQgZGF0YSBmb3IgYWxsIGRpcmVjdGl2ZXMgb24gdGhpcyBub2RlLiBgbnVsbGAgbWVhbnMgdGhhdCB0aGVyZSBhcmUgbm8gZGlyZWN0aXZlcyB3aXRoXG4gICAqIGlucHV0cyBvbiB0aGlzIG5vZGUuXG4gICAqL1xuICBpbnB1dHM6IFByb3BlcnR5QWxpYXNlc3xudWxsO1xuXG4gIC8qKlxuICAgKiBPdXRwdXQgZGF0YSBmb3IgYWxsIGRpcmVjdGl2ZXMgb24gdGhpcyBub2RlLiBgbnVsbGAgbWVhbnMgdGhhdCB0aGVyZSBhcmUgbm8gZGlyZWN0aXZlcyB3aXRoXG4gICAqIG91dHB1dHMgb24gdGhpcyBub2RlLlxuICAgKi9cbiAgb3V0cHV0czogUHJvcGVydHlBbGlhc2VzfG51bGw7XG5cbiAgLyoqXG4gICAqIFRoZSBUVmlldyBhdHRhY2hlZCB0byB0aGlzIG5vZGUuXG4gICAqXG4gICAqIElmIHRoaXMgVE5vZGUgY29ycmVzcG9uZHMgdG8gYW4gTENvbnRhaW5lciB3aXRoIGEgdGVtcGxhdGUgKGUuZy4gc3RydWN0dXJhbFxuICAgKiBkaXJlY3RpdmUpLCB0aGUgdGVtcGxhdGUncyBUVmlldyB3aWxsIGJlIHN0b3JlZCBoZXJlLlxuICAgKlxuICAgKiBJZiB0aGlzIFROb2RlIGNvcnJlc3BvbmRzIHRvIGFuIGVsZW1lbnQsIHRWaWV3IHdpbGwgYmUgYG51bGxgLlxuICAgKi9cbiAgdFZpZXc6IFRWaWV3fG51bGw7XG5cbiAgLyoqXG4gICAqIFRoZSBuZXh0IHNpYmxpbmcgbm9kZS4gTmVjZXNzYXJ5IHNvIHdlIGNhbiBwcm9wYWdhdGUgdGhyb3VnaCB0aGUgcm9vdCBub2RlcyBvZiBhIHZpZXdcbiAgICogdG8gaW5zZXJ0IHRoZW0gb3IgcmVtb3ZlIHRoZW0gZnJvbSB0aGUgRE9NLlxuICAgKi9cbiAgbmV4dDogVE5vZGV8bnVsbDtcblxuICAvKipcbiAgICogVGhlIHByZXZpb3VzIHNpYmxpbmcgbm9kZS5cbiAgICogVGhpcyBzaW1wbGlmaWVzIG9wZXJhdGlvbnMgd2hlbiB3ZSBuZWVkIGEgcG9pbnRlciB0byB0aGUgcHJldmlvdXMgbm9kZS5cbiAgICovXG4gIHByZXY6IFROb2RlfG51bGw7XG5cbiAgLyoqXG4gICAqIFRoZSBuZXh0IHByb2plY3RlZCBzaWJsaW5nLiBTaW5jZSBpbiBBbmd1bGFyIGNvbnRlbnQgcHJvamVjdGlvbiB3b3JrcyBvbiB0aGUgbm9kZS1ieS1ub2RlXG4gICAqIGJhc2lzIHRoZSBhY3Qgb2YgcHJvamVjdGluZyBub2RlcyBtaWdodCBjaGFuZ2Ugbm9kZXMgcmVsYXRpb25zaGlwIGF0IHRoZSBpbnNlcnRpb24gcG9pbnRcbiAgICogKHRhcmdldCB2aWV3KS4gQXQgdGhlIHNhbWUgdGltZSB3ZSBuZWVkIHRvIGtlZXAgaW5pdGlhbCByZWxhdGlvbnNoaXAgYmV0d2VlbiBub2RlcyBhc1xuICAgKiBleHByZXNzZWQgaW4gY29udGVudCB2aWV3LlxuICAgKi9cbiAgcHJvamVjdGlvbk5leHQ6IFROb2RlfG51bGw7XG5cbiAgLyoqXG4gICAqIEZpcnN0IGNoaWxkIG9mIHRoZSBjdXJyZW50IG5vZGUuXG4gICAqXG4gICAqIEZvciBjb21wb25lbnQgbm9kZXMsIHRoZSBjaGlsZCB3aWxsIGFsd2F5cyBiZSBhIENvbnRlbnRDaGlsZCAoaW4gc2FtZSB2aWV3KS5cbiAgICogRm9yIGVtYmVkZGVkIHZpZXcgbm9kZXMsIHRoZSBjaGlsZCB3aWxsIGJlIGluIHRoZWlyIGNoaWxkIHZpZXcuXG4gICAqL1xuICBjaGlsZDogVE5vZGV8bnVsbDtcblxuICAvKipcbiAgICogUGFyZW50IG5vZGUgKGluIHRoZSBzYW1lIHZpZXcgb25seSkuXG4gICAqXG4gICAqIFdlIG5lZWQgYSByZWZlcmVuY2UgdG8gYSBub2RlJ3MgcGFyZW50IHNvIHdlIGNhbiBhcHBlbmQgdGhlIG5vZGUgdG8gaXRzIHBhcmVudCdzIG5hdGl2ZVxuICAgKiBlbGVtZW50IGF0IHRoZSBhcHByb3ByaWF0ZSB0aW1lLlxuICAgKlxuICAgKiBJZiB0aGUgcGFyZW50IHdvdWxkIGJlIGluIGEgZGlmZmVyZW50IHZpZXcgKGUuZy4gY29tcG9uZW50IGhvc3QpLCB0aGlzIHByb3BlcnR5IHdpbGwgYmUgbnVsbC5cbiAgICogSXQncyBpbXBvcnRhbnQgdGhhdCB3ZSBkb24ndCB0cnkgdG8gY3Jvc3MgY29tcG9uZW50IGJvdW5kYXJpZXMgd2hlbiByZXRyaWV2aW5nIHRoZSBwYXJlbnRcbiAgICogYmVjYXVzZSB0aGUgcGFyZW50IHdpbGwgY2hhbmdlIChlLmcuIGluZGV4LCBhdHRycykgZGVwZW5kaW5nIG9uIHdoZXJlIHRoZSBjb21wb25lbnQgd2FzXG4gICAqIHVzZWQgKGFuZCB0aHVzIHNob3VsZG4ndCBiZSBzdG9yZWQgb24gVE5vZGUpLiBJbiB0aGVzZSBjYXNlcywgd2UgcmV0cmlldmUgdGhlIHBhcmVudCB0aHJvdWdoXG4gICAqIExWaWV3Lm5vZGUgaW5zdGVhZCAod2hpY2ggd2lsbCBiZSBpbnN0YW5jZS1zcGVjaWZpYykuXG4gICAqXG4gICAqIElmIHRoaXMgaXMgYW4gaW5saW5lIHZpZXcgbm9kZSAoViksIHRoZSBwYXJlbnQgd2lsbCBiZSBpdHMgY29udGFpbmVyLlxuICAgKi9cbiAgcGFyZW50OiBURWxlbWVudE5vZGV8VENvbnRhaW5lck5vZGV8bnVsbDtcblxuICAvKipcbiAgICogTGlzdCBvZiBwcm9qZWN0ZWQgVE5vZGVzIGZvciBhIGdpdmVuIGNvbXBvbmVudCBob3N0IGVsZW1lbnQgT1IgaW5kZXggaW50byB0aGUgc2FpZCBub2Rlcy5cbiAgICpcbiAgICogRm9yIGVhc2llciBkaXNjdXNzaW9uIGFzc3VtZSB0aGlzIGV4YW1wbGU6XG4gICAqIGA8cGFyZW50PmAncyB2aWV3IGRlZmluaXRpb246XG4gICAqIGBgYFxuICAgKiA8Y2hpbGQgaWQ9XCJjMVwiPmNvbnRlbnQxPC9jaGlsZD5cbiAgICogPGNoaWxkIGlkPVwiYzJcIj48c3Bhbj5jb250ZW50Mjwvc3Bhbj48L2NoaWxkPlxuICAgKiBgYGBcbiAgICogYDxjaGlsZD5gJ3MgdmlldyBkZWZpbml0aW9uOlxuICAgKiBgYGBcbiAgICogPG5nLWNvbnRlbnQgaWQ9XCJjb250MVwiPjwvbmctY29udGVudD5cbiAgICogYGBgXG4gICAqXG4gICAqIElmIGBBcnJheS5pc0FycmF5KHByb2plY3Rpb24pYCB0aGVuIGBUTm9kZWAgaXMgYSBob3N0IGVsZW1lbnQ6XG4gICAqIC0gYHByb2plY3Rpb25gIHN0b3JlcyB0aGUgY29udGVudCBub2RlcyB3aGljaCBhcmUgdG8gYmUgcHJvamVjdGVkLlxuICAgKiAgICAtIFRoZSBub2RlcyByZXByZXNlbnQgY2F0ZWdvcmllcyBkZWZpbmVkIGJ5IHRoZSBzZWxlY3RvcjogRm9yIGV4YW1wbGU6XG4gICAqICAgICAgYDxuZy1jb250ZW50Lz48bmctY29udGVudCBzZWxlY3Q9XCJhYmNcIi8+YCB3b3VsZCByZXByZXNlbnQgdGhlIGhlYWRzIGZvciBgPG5nLWNvbnRlbnQvPmBcbiAgICogICAgICBhbmQgYDxuZy1jb250ZW50IHNlbGVjdD1cImFiY1wiLz5gIHJlc3BlY3RpdmVseS5cbiAgICogICAgLSBUaGUgbm9kZXMgd2Ugc3RvcmUgaW4gYHByb2plY3Rpb25gIGFyZSBoZWFkcyBvbmx5LCB3ZSB1c2VkIGAubmV4dGAgdG8gZ2V0IHRoZWlyXG4gICAqICAgICAgc2libGluZ3MuXG4gICAqICAgIC0gVGhlIG5vZGVzIGAubmV4dGAgaXMgc29ydGVkL3Jld3JpdHRlbiBhcyBwYXJ0IG9mIHRoZSBwcm9qZWN0aW9uIHNldHVwLlxuICAgKiAgICAtIGBwcm9qZWN0aW9uYCBzaXplIGlzIGVxdWFsIHRvIHRoZSBudW1iZXIgb2YgcHJvamVjdGlvbnMgYDxuZy1jb250ZW50PmAuIFRoZSBzaXplIG9mXG4gICAqICAgICAgYGMxYCB3aWxsIGJlIGAxYCBiZWNhdXNlIGA8Y2hpbGQ+YCBoYXMgb25seSBvbmUgYDxuZy1jb250ZW50PmAuXG4gICAqIC0gd2Ugc3RvcmUgYHByb2plY3Rpb25gIHdpdGggdGhlIGhvc3QgKGBjMWAsIGBjMmApIHJhdGhlciB0aGFuIHRoZSBgPG5nLWNvbnRlbnQ+YCAoYGNvbnQxYClcbiAgICogICBiZWNhdXNlIHRoZSBzYW1lIGNvbXBvbmVudCAoYDxjaGlsZD5gKSBjYW4gYmUgdXNlZCBpbiBtdWx0aXBsZSBsb2NhdGlvbnMgKGBjMWAsIGBjMmApIGFuZFxuICAgKiBhcyBhIHJlc3VsdCBoYXZlIGRpZmZlcmVudCBzZXQgb2Ygbm9kZXMgdG8gcHJvamVjdC5cbiAgICogLSB3aXRob3V0IGBwcm9qZWN0aW9uYCBpdCB3b3VsZCBiZSBkaWZmaWN1bHQgdG8gZWZmaWNpZW50bHkgdHJhdmVyc2Ugbm9kZXMgdG8gYmUgcHJvamVjdGVkLlxuICAgKlxuICAgKiBJZiBgdHlwZW9mIHByb2plY3Rpb24gPT0gJ251bWJlcidgIHRoZW4gYFROb2RlYCBpcyBhIGA8bmctY29udGVudD5gIGVsZW1lbnQ6XG4gICAqIC0gYHByb2plY3Rpb25gIGlzIGFuIGluZGV4IG9mIHRoZSBob3N0J3MgYHByb2plY3Rpb25gTm9kZXMuXG4gICAqICAgLSBUaGlzIHdvdWxkIHJldHVybiB0aGUgZmlyc3QgaGVhZCBub2RlIHRvIHByb2plY3Q6XG4gICAqICAgICBgZ2V0SG9zdChjdXJyZW50VE5vZGUpLnByb2plY3Rpb25bY3VycmVudFROb2RlLnByb2plY3Rpb25dYC5cbiAgICogLSBXaGVuIHByb2plY3Rpbmcgbm9kZXMgdGhlIHBhcmVudCBub2RlIHJldHJpZXZlZCBtYXkgYmUgYSBgPG5nLWNvbnRlbnQ+YCBub2RlLCBpbiB3aGljaCBjYXNlXG4gICAqICAgdGhlIHByb2Nlc3MgaXMgcmVjdXJzaXZlIGluIG5hdHVyZS5cbiAgICpcbiAgICogSWYgYHByb2plY3Rpb25gIGlzIG9mIHR5cGUgYFJOb2RlW11bXWAgdGhhbiB3ZSBoYXZlIGEgY29sbGVjdGlvbiBvZiBuYXRpdmUgbm9kZXMgcGFzc2VkIGFzXG4gICAqIHByb2plY3RhYmxlIG5vZGVzIGR1cmluZyBkeW5hbWljIGNvbXBvbmVudCBjcmVhdGlvbi5cbiAgICovXG4gIHByb2plY3Rpb246IChUTm9kZXxSTm9kZVtdKVtdfG51bWJlcnxudWxsO1xuXG4gIC8qKlxuICAgKiBBIGNvbGxlY3Rpb24gb2YgYWxsIGBzdHlsZWAgc3RhdGljIHZhbHVlcyBmb3IgYW4gZWxlbWVudCAoaW5jbHVkaW5nIGZyb20gaG9zdCkuXG4gICAqXG4gICAqIFRoaXMgZmllbGQgd2lsbCBiZSBwb3B1bGF0ZWQgaWYgYW5kIHdoZW46XG4gICAqXG4gICAqIC0gVGhlcmUgYXJlIG9uZSBvciBtb3JlIGluaXRpYWwgYHN0eWxlYHMgb24gYW4gZWxlbWVudCAoZS5nLiBgPGRpdiBzdHlsZT1cIndpZHRoOjIwMHB4O1wiPmApXG4gICAqIC0gVGhlcmUgYXJlIG9uZSBvciBtb3JlIGluaXRpYWwgYHN0eWxlYHMgb24gYSBkaXJlY3RpdmUvY29tcG9uZW50IGhvc3RcbiAgICogICAoZS5nLiBgQERpcmVjdGl2ZSh7aG9zdDoge3N0eWxlOiBcIndpZHRoOjIwMHB4O1wiIH0gfWApXG4gICAqL1xuICBzdHlsZXM6IHN0cmluZ3xudWxsO1xuXG5cbiAgLyoqXG4gICAqIEEgY29sbGVjdGlvbiBvZiBhbGwgYHN0eWxlYCBzdGF0aWMgdmFsdWVzIGZvciBhbiBlbGVtZW50IGV4Y2x1ZGluZyBob3N0IHNvdXJjZXMuXG4gICAqXG4gICAqIFBvcHVsYXRlZCB3aGVuIHRoZXJlIGFyZSBvbmUgb3IgbW9yZSBpbml0aWFsIGBzdHlsZWBzIG9uIGFuIGVsZW1lbnRcbiAgICogKGUuZy4gYDxkaXYgc3R5bGU9XCJ3aWR0aDoyMDBweDtcIj5gKVxuICAgKiBNdXN0IGJlIHN0b3JlZCBzZXBhcmF0ZWx5IGZyb20gYHROb2RlLnN0eWxlc2AgdG8gZmFjaWxpdGF0ZSBzZXR0aW5nIGRpcmVjdGl2ZVxuICAgKiBpbnB1dHMgdGhhdCBzaGFkb3cgdGhlIGBzdHlsZWAgcHJvcGVydHkuIElmIHdlIHVzZWQgYHROb2RlLnN0eWxlc2AgYXMgaXMgZm9yIHNoYWRvd2VkIGlucHV0cyxcbiAgICogd2Ugd291bGQgZmVlZCBob3N0IHN0eWxlcyBiYWNrIGludG8gZGlyZWN0aXZlcyBhcyBcImlucHV0c1wiLiBJZiB3ZSB1c2VkIGB0Tm9kZS5hdHRyc2AsIHdlXG4gICAqIHdvdWxkIGhhdmUgdG8gY29uY2F0ZW5hdGUgdGhlIGF0dHJpYnV0ZXMgb24gZXZlcnkgdGVtcGxhdGUgcGFzcy4gSW5zdGVhZCwgd2UgcHJvY2VzcyBvbmNlIG9uXG4gICAqIGZpcnN0IGNyZWF0ZSBwYXNzIGFuZCBzdG9yZSBoZXJlLlxuICAgKi9cbiAgc3R5bGVzV2l0aG91dEhvc3Q6IHN0cmluZ3xudWxsO1xuXG4gIC8qKlxuICAgKiBBIGBLZXlWYWx1ZUFycmF5YCB2ZXJzaW9uIG9mIHJlc2lkdWFsIGBzdHlsZXNgLlxuICAgKlxuICAgKiBXaGVuIHRoZXJlIGFyZSBzdHlsaW5nIGluc3RydWN0aW9ucyB0aGFuIGVhY2ggaW5zdHJ1Y3Rpb24gc3RvcmVzIHRoZSBzdGF0aWMgc3R5bGluZ1xuICAgKiB3aGljaCBpcyBvZiBsb3dlciBwcmlvcml0eSB0aGFuIGl0c2VsZi4gVGhpcyBtZWFucyB0aGF0IHRoZXJlIG1heSBiZSBhIGhpZ2hlciBwcmlvcml0eVxuICAgKiBzdHlsaW5nIHRoYW4gdGhlIGluc3RydWN0aW9uLlxuICAgKlxuICAgKiBJbWFnaW5lOlxuICAgKiBgYGBcbiAgICogPGRpdiBzdHlsZT1cImNvbG9yOiBoaWdoZXN0O1wiIG15LWRpcj5cbiAgICpcbiAgICogQERpcmVjdGl2ZSh7XG4gICAqICAgaG9zdDoge1xuICAgKiAgICAgc3R5bGU6ICdjb2xvcjogbG93ZXN0OyAnLFxuICAgKiAgICAgJ1tzdHlsZXMuY29sb3JdJzogJ2V4cCcgLy8gybXJtXN0eWxlUHJvcCgnY29sb3InLCBjdHguZXhwKTtcbiAgICogICB9XG4gICAqIH0pXG4gICAqIGBgYFxuICAgKlxuICAgKiBJbiB0aGUgYWJvdmUgY2FzZTpcbiAgICogLSBgY29sb3I6IGxvd2VzdGAgaXMgc3RvcmVkIHdpdGggYMm1ybVzdHlsZVByb3AoJ2NvbG9yJywgY3R4LmV4cCk7YCBpbnN0cnVjdGlvblxuICAgKiAtICBgY29sb3I6IGhpZ2hlc3RgIGlzIHRoZSByZXNpZHVhbCBhbmQgaXMgc3RvcmVkIGhlcmUuXG4gICAqXG4gICAqIC0gYHVuZGVmaW5lZCc6IG5vdCBpbml0aWFsaXplZC5cbiAgICogLSBgbnVsbGA6IGluaXRpYWxpemVkIGJ1dCBgc3R5bGVzYCBpcyBgbnVsbGBcbiAgICogLSBgS2V5VmFsdWVBcnJheWA6IHBhcnNlZCB2ZXJzaW9uIG9mIGBzdHlsZXNgLlxuICAgKi9cbiAgcmVzaWR1YWxTdHlsZXM6IEtleVZhbHVlQXJyYXk8YW55Pnx1bmRlZmluZWR8bnVsbDtcblxuICAvKipcbiAgICogQSBjb2xsZWN0aW9uIG9mIGFsbCBjbGFzcyBzdGF0aWMgdmFsdWVzIGZvciBhbiBlbGVtZW50IChpbmNsdWRpbmcgZnJvbSBob3N0KS5cbiAgICpcbiAgICogVGhpcyBmaWVsZCB3aWxsIGJlIHBvcHVsYXRlZCBpZiBhbmQgd2hlbjpcbiAgICpcbiAgICogLSBUaGVyZSBhcmUgb25lIG9yIG1vcmUgaW5pdGlhbCBjbGFzc2VzIG9uIGFuIGVsZW1lbnQgKGUuZy4gYDxkaXYgY2xhc3M9XCJvbmUgdHdvIHRocmVlXCI+YClcbiAgICogLSBUaGVyZSBhcmUgb25lIG9yIG1vcmUgaW5pdGlhbCBjbGFzc2VzIG9uIGFuIGRpcmVjdGl2ZS9jb21wb25lbnQgaG9zdFxuICAgKiAgIChlLmcuIGBARGlyZWN0aXZlKHtob3N0OiB7Y2xhc3M6IFwiU09NRV9DTEFTU1wiIH0gfWApXG4gICAqL1xuICBjbGFzc2VzOiBzdHJpbmd8bnVsbDtcblxuICAvKipcbiAgICogQSBjb2xsZWN0aW9uIG9mIGFsbCBjbGFzcyBzdGF0aWMgdmFsdWVzIGZvciBhbiBlbGVtZW50IGV4Y2x1ZGluZyBob3N0IHNvdXJjZXMuXG4gICAqXG4gICAqIFBvcHVsYXRlZCB3aGVuIHRoZXJlIGFyZSBvbmUgb3IgbW9yZSBpbml0aWFsIGNsYXNzZXMgb24gYW4gZWxlbWVudFxuICAgKiAoZS5nLiBgPGRpdiBjbGFzcz1cIlNPTUVfQ0xBU1NcIj5gKVxuICAgKiBNdXN0IGJlIHN0b3JlZCBzZXBhcmF0ZWx5IGZyb20gYHROb2RlLmNsYXNzZXNgIHRvIGZhY2lsaXRhdGUgc2V0dGluZyBkaXJlY3RpdmVcbiAgICogaW5wdXRzIHRoYXQgc2hhZG93IHRoZSBgY2xhc3NgIHByb3BlcnR5LiBJZiB3ZSB1c2VkIGB0Tm9kZS5jbGFzc2VzYCBhcyBpcyBmb3Igc2hhZG93ZWRcbiAgICogaW5wdXRzLCB3ZSB3b3VsZCBmZWVkIGhvc3QgY2xhc3NlcyBiYWNrIGludG8gZGlyZWN0aXZlcyBhcyBcImlucHV0c1wiLiBJZiB3ZSB1c2VkXG4gICAqIGB0Tm9kZS5hdHRyc2AsIHdlIHdvdWxkIGhhdmUgdG8gY29uY2F0ZW5hdGUgdGhlIGF0dHJpYnV0ZXMgb24gZXZlcnkgdGVtcGxhdGUgcGFzcy4gSW5zdGVhZCxcbiAgICogd2UgcHJvY2VzcyBvbmNlIG9uIGZpcnN0IGNyZWF0ZSBwYXNzIGFuZCBzdG9yZSBoZXJlLlxuICAgKi9cbiAgY2xhc3Nlc1dpdGhvdXRIb3N0OiBzdHJpbmd8bnVsbDtcblxuICAvKipcbiAgICogQSBgS2V5VmFsdWVBcnJheWAgdmVyc2lvbiBvZiByZXNpZHVhbCBgY2xhc3Nlc2AuXG4gICAqXG4gICAqIFNhbWUgYXMgYFROb2RlLnJlc2lkdWFsU3R5bGVzYCBidXQgZm9yIGNsYXNzZXMuXG4gICAqXG4gICAqIC0gYHVuZGVmaW5lZCc6IG5vdCBpbml0aWFsaXplZC5cbiAgICogLSBgbnVsbGA6IGluaXRpYWxpemVkIGJ1dCBgY2xhc3Nlc2AgaXMgYG51bGxgXG4gICAqIC0gYEtleVZhbHVlQXJyYXlgOiBwYXJzZWQgdmVyc2lvbiBvZiBgY2xhc3Nlc2AuXG4gICAqL1xuICByZXNpZHVhbENsYXNzZXM6IEtleVZhbHVlQXJyYXk8YW55Pnx1bmRlZmluZWR8bnVsbDtcblxuICAvKipcbiAgICogU3RvcmVzIHRoZSBoZWFkL3RhaWwgaW5kZXggb2YgdGhlIGNsYXNzIGJpbmRpbmdzLlxuICAgKlxuICAgKiAtIElmIG5vIGJpbmRpbmdzLCB0aGUgaGVhZCBhbmQgdGFpbCB3aWxsIGJvdGggYmUgMC5cbiAgICogLSBJZiB0aGVyZSBhcmUgdGVtcGxhdGUgYmluZGluZ3MsIHN0b3JlcyB0aGUgaGVhZC90YWlsIG9mIHRoZSBjbGFzcyBiaW5kaW5ncyBpbiB0aGUgdGVtcGxhdGUuXG4gICAqIC0gSWYgbm8gdGVtcGxhdGUgYmluZGluZ3MgYnV0IHRoZXJlIGFyZSBob3N0IGJpbmRpbmdzLCB0aGUgaGVhZCB2YWx1ZSB3aWxsIHBvaW50IHRvIHRoZSBsYXN0XG4gICAqICAgaG9zdCBiaW5kaW5nIGZvciBcImNsYXNzXCIgKG5vdCB0aGUgaGVhZCBvZiB0aGUgbGlua2VkIGxpc3QpLCB0YWlsIHdpbGwgYmUgMC5cbiAgICpcbiAgICogU2VlOiBgc3R5bGVfYmluZGluZ19saXN0LnRzYCBmb3IgZGV0YWlscy5cbiAgICpcbiAgICogVGhpcyBpcyB1c2VkIGJ5IGBpbnNlcnRUU3R5bGluZ0JpbmRpbmdgIHRvIGtub3cgd2hlcmUgdGhlIG5leHQgc3R5bGluZyBiaW5kaW5nIHNob3VsZCBiZVxuICAgKiBpbnNlcnRlZCBzbyB0aGF0IHRoZXkgY2FuIGJlIHNvcnRlZCBpbiBwcmlvcml0eSBvcmRlci5cbiAgICovXG4gIGNsYXNzQmluZGluZ3M6IFRTdHlsaW5nUmFuZ2U7XG5cbiAgLyoqXG4gICAqIFN0b3JlcyB0aGUgaGVhZC90YWlsIGluZGV4IG9mIHRoZSBjbGFzcyBiaW5kaW5ncy5cbiAgICpcbiAgICogLSBJZiBubyBiaW5kaW5ncywgdGhlIGhlYWQgYW5kIHRhaWwgd2lsbCBib3RoIGJlIDAuXG4gICAqIC0gSWYgdGhlcmUgYXJlIHRlbXBsYXRlIGJpbmRpbmdzLCBzdG9yZXMgdGhlIGhlYWQvdGFpbCBvZiB0aGUgc3R5bGUgYmluZGluZ3MgaW4gdGhlIHRlbXBsYXRlLlxuICAgKiAtIElmIG5vIHRlbXBsYXRlIGJpbmRpbmdzIGJ1dCB0aGVyZSBhcmUgaG9zdCBiaW5kaW5ncywgdGhlIGhlYWQgdmFsdWUgd2lsbCBwb2ludCB0byB0aGUgbGFzdFxuICAgKiAgIGhvc3QgYmluZGluZyBmb3IgXCJzdHlsZVwiIChub3QgdGhlIGhlYWQgb2YgdGhlIGxpbmtlZCBsaXN0KSwgdGFpbCB3aWxsIGJlIDAuXG4gICAqXG4gICAqIFNlZTogYHN0eWxlX2JpbmRpbmdfbGlzdC50c2AgZm9yIGRldGFpbHMuXG4gICAqXG4gICAqIFRoaXMgaXMgdXNlZCBieSBgaW5zZXJ0VFN0eWxpbmdCaW5kaW5nYCB0byBrbm93IHdoZXJlIHRoZSBuZXh0IHN0eWxpbmcgYmluZGluZyBzaG91bGQgYmVcbiAgICogaW5zZXJ0ZWQgc28gdGhhdCB0aGV5IGNhbiBiZSBzb3J0ZWQgaW4gcHJpb3JpdHkgb3JkZXIuXG4gICAqL1xuICBzdHlsZUJpbmRpbmdzOiBUU3R5bGluZ1JhbmdlO1xufVxuXG4vKipcbiAqIFNlZSBgVE5vZGUuaW5zZXJ0QmVmb3JlSW5kZXhgXG4gKi9cbmV4cG9ydCB0eXBlIEluc2VydEJlZm9yZUluZGV4ID0gbnVsbHxudW1iZXJ8bnVtYmVyW107XG5cbi8qKiBTdGF0aWMgZGF0YSBmb3IgYW4gZWxlbWVudCAgKi9cbmV4cG9ydCBpbnRlcmZhY2UgVEVsZW1lbnROb2RlIGV4dGVuZHMgVE5vZGUge1xuICAvKiogSW5kZXggaW4gdGhlIGRhdGFbXSBhcnJheSAqL1xuICBpbmRleDogbnVtYmVyO1xuICBjaGlsZDogVEVsZW1lbnROb2RlfFRUZXh0Tm9kZXxURWxlbWVudENvbnRhaW5lck5vZGV8VENvbnRhaW5lck5vZGV8VFByb2plY3Rpb25Ob2RlfG51bGw7XG4gIC8qKlxuICAgKiBFbGVtZW50IG5vZGVzIHdpbGwgaGF2ZSBwYXJlbnRzIHVubGVzcyB0aGV5IGFyZSB0aGUgZmlyc3Qgbm9kZSBvZiBhIGNvbXBvbmVudCBvclxuICAgKiBlbWJlZGRlZCB2aWV3ICh3aGljaCBtZWFucyB0aGVpciBwYXJlbnQgaXMgaW4gYSBkaWZmZXJlbnQgdmlldyBhbmQgbXVzdCBiZVxuICAgKiByZXRyaWV2ZWQgdXNpbmcgdmlld0RhdGFbSE9TVF9OT0RFXSkuXG4gICAqL1xuICBwYXJlbnQ6IFRFbGVtZW50Tm9kZXxURWxlbWVudENvbnRhaW5lck5vZGV8bnVsbDtcbiAgdFZpZXc6IG51bGw7XG5cbiAgLyoqXG4gICAqIElmIHRoaXMgaXMgYSBjb21wb25lbnQgVE5vZGUgd2l0aCBwcm9qZWN0aW9uLCB0aGlzIHdpbGwgYmUgYW4gYXJyYXkgb2YgcHJvamVjdGVkXG4gICAqIFROb2RlcyBvciBuYXRpdmUgbm9kZXMgKHNlZSBUTm9kZS5wcm9qZWN0aW9uIGZvciBtb3JlIGluZm8pLiBJZiBpdCdzIGEgcmVndWxhciBlbGVtZW50IG5vZGVcbiAgICogb3IgYSBjb21wb25lbnQgd2l0aG91dCBwcm9qZWN0aW9uLCBpdCB3aWxsIGJlIG51bGwuXG4gICAqL1xuICBwcm9qZWN0aW9uOiAoVE5vZGV8Uk5vZGVbXSlbXXxudWxsO1xuXG4gIC8qKlxuICAgKiBTdG9yZXMgVGFnTmFtZVxuICAgKi9cbiAgdmFsdWU6IHN0cmluZztcbn1cblxuLyoqIFN0YXRpYyBkYXRhIGZvciBhIHRleHQgbm9kZSAqL1xuZXhwb3J0IGludGVyZmFjZSBUVGV4dE5vZGUgZXh0ZW5kcyBUTm9kZSB7XG4gIC8qKiBJbmRleCBpbiB0aGUgZGF0YVtdIGFycmF5ICovXG4gIGluZGV4OiBudW1iZXI7XG4gIGNoaWxkOiBudWxsO1xuICAvKipcbiAgICogVGV4dCBub2RlcyB3aWxsIGhhdmUgcGFyZW50cyB1bmxlc3MgdGhleSBhcmUgdGhlIGZpcnN0IG5vZGUgb2YgYSBjb21wb25lbnQgb3JcbiAgICogZW1iZWRkZWQgdmlldyAod2hpY2ggbWVhbnMgdGhlaXIgcGFyZW50IGlzIGluIGEgZGlmZmVyZW50IHZpZXcgYW5kIG11c3QgYmVcbiAgICogcmV0cmlldmVkIHVzaW5nIExWaWV3Lm5vZGUpLlxuICAgKi9cbiAgcGFyZW50OiBURWxlbWVudE5vZGV8VEVsZW1lbnRDb250YWluZXJOb2RlfG51bGw7XG4gIHRWaWV3OiBudWxsO1xuICBwcm9qZWN0aW9uOiBudWxsO1xufVxuXG4vKiogU3RhdGljIGRhdGEgZm9yIGFuIExDb250YWluZXIgKi9cbmV4cG9ydCBpbnRlcmZhY2UgVENvbnRhaW5lck5vZGUgZXh0ZW5kcyBUTm9kZSB7XG4gIC8qKlxuICAgKiBJbmRleCBpbiB0aGUgZGF0YVtdIGFycmF5LlxuICAgKlxuICAgKiBJZiBpdCdzIC0xLCB0aGlzIGlzIGEgZHluYW1pY2FsbHkgY3JlYXRlZCBjb250YWluZXIgbm9kZSB0aGF0IGlzbid0IHN0b3JlZCBpblxuICAgKiBkYXRhW10gKGUuZy4gd2hlbiB5b3UgaW5qZWN0IFZpZXdDb250YWluZXJSZWYpIC5cbiAgICovXG4gIGluZGV4OiBudW1iZXI7XG4gIGNoaWxkOiBudWxsO1xuXG4gIC8qKlxuICAgKiBDb250YWluZXIgbm9kZXMgd2lsbCBoYXZlIHBhcmVudHMgdW5sZXNzOlxuICAgKlxuICAgKiAtIFRoZXkgYXJlIHRoZSBmaXJzdCBub2RlIG9mIGEgY29tcG9uZW50IG9yIGVtYmVkZGVkIHZpZXdcbiAgICogLSBUaGV5IGFyZSBkeW5hbWljYWxseSBjcmVhdGVkXG4gICAqL1xuICBwYXJlbnQ6IFRFbGVtZW50Tm9kZXxURWxlbWVudENvbnRhaW5lck5vZGV8bnVsbDtcbiAgdFZpZXc6IFRWaWV3fG51bGw7XG4gIHByb2plY3Rpb246IG51bGw7XG4gIHZhbHVlOiBudWxsO1xufVxuXG4vKiogU3RhdGljIGRhdGEgZm9yIGFuIDxuZy1jb250YWluZXI+ICovXG5leHBvcnQgaW50ZXJmYWNlIFRFbGVtZW50Q29udGFpbmVyTm9kZSBleHRlbmRzIFROb2RlIHtcbiAgLyoqIEluZGV4IGluIHRoZSBMVmlld1tdIGFycmF5LiAqL1xuICBpbmRleDogbnVtYmVyO1xuICBjaGlsZDogVEVsZW1lbnROb2RlfFRUZXh0Tm9kZXxUQ29udGFpbmVyTm9kZXxURWxlbWVudENvbnRhaW5lck5vZGV8VFByb2plY3Rpb25Ob2RlfG51bGw7XG4gIHBhcmVudDogVEVsZW1lbnROb2RlfFRFbGVtZW50Q29udGFpbmVyTm9kZXxudWxsO1xuICB0VmlldzogbnVsbDtcbiAgcHJvamVjdGlvbjogbnVsbDtcbn1cblxuLyoqIFN0YXRpYyBkYXRhIGZvciBhbiBJQ1UgZXhwcmVzc2lvbiAqL1xuZXhwb3J0IGludGVyZmFjZSBUSWN1Q29udGFpbmVyTm9kZSBleHRlbmRzIFROb2RlIHtcbiAgLyoqIEluZGV4IGluIHRoZSBMVmlld1tdIGFycmF5LiAqL1xuICBpbmRleDogbnVtYmVyO1xuICBjaGlsZDogbnVsbDtcbiAgcGFyZW50OiBURWxlbWVudE5vZGV8VEVsZW1lbnRDb250YWluZXJOb2RlfG51bGw7XG4gIHRWaWV3OiBudWxsO1xuICBwcm9qZWN0aW9uOiBudWxsO1xuICB2YWx1ZTogVEljdTtcbn1cblxuLyoqIFN0YXRpYyBkYXRhIGZvciBhbiBMUHJvamVjdGlvbk5vZGUgICovXG5leHBvcnQgaW50ZXJmYWNlIFRQcm9qZWN0aW9uTm9kZSBleHRlbmRzIFROb2RlIHtcbiAgLyoqIEluZGV4IGluIHRoZSBkYXRhW10gYXJyYXkgKi9cbiAgY2hpbGQ6IG51bGw7XG4gIC8qKlxuICAgKiBQcm9qZWN0aW9uIG5vZGVzIHdpbGwgaGF2ZSBwYXJlbnRzIHVubGVzcyB0aGV5IGFyZSB0aGUgZmlyc3Qgbm9kZSBvZiBhIGNvbXBvbmVudFxuICAgKiBvciBlbWJlZGRlZCB2aWV3ICh3aGljaCBtZWFucyB0aGVpciBwYXJlbnQgaXMgaW4gYSBkaWZmZXJlbnQgdmlldyBhbmQgbXVzdCBiZVxuICAgKiByZXRyaWV2ZWQgdXNpbmcgTFZpZXcubm9kZSkuXG4gICAqL1xuICBwYXJlbnQ6IFRFbGVtZW50Tm9kZXxURWxlbWVudENvbnRhaW5lck5vZGV8bnVsbDtcbiAgdFZpZXc6IG51bGw7XG5cbiAgLyoqIEluZGV4IG9mIHRoZSBwcm9qZWN0aW9uIG5vZGUuIChTZWUgVE5vZGUucHJvamVjdGlvbiBmb3IgbW9yZSBpbmZvLikgKi9cbiAgcHJvamVjdGlvbjogbnVtYmVyO1xuICB2YWx1ZTogbnVsbDtcbn1cblxuLyoqXG4gKiBBIHVuaW9uIHR5cGUgcmVwcmVzZW50aW5nIGFsbCBUTm9kZSB0eXBlcyB0aGF0IGNhbiBob3N0IGEgZGlyZWN0aXZlLlxuICovXG5leHBvcnQgdHlwZSBURGlyZWN0aXZlSG9zdE5vZGUgPSBURWxlbWVudE5vZGV8VENvbnRhaW5lck5vZGV8VEVsZW1lbnRDb250YWluZXJOb2RlO1xuXG4vKipcbiAqIFRoaXMgbWFwcGluZyBpcyBuZWNlc3Nhcnkgc28gd2UgY2FuIHNldCBpbnB1dCBwcm9wZXJ0aWVzIGFuZCBvdXRwdXQgbGlzdGVuZXJzXG4gKiBwcm9wZXJseSBhdCBydW50aW1lIHdoZW4gcHJvcGVydHkgbmFtZXMgYXJlIG1pbmlmaWVkIG9yIGFsaWFzZWQuXG4gKlxuICogS2V5OiB1bm1pbmlmaWVkIC8gcHVibGljIGlucHV0IG9yIG91dHB1dCBuYW1lXG4gKiBWYWx1ZTogYXJyYXkgY29udGFpbmluZyBtaW5pZmllZCAvIGludGVybmFsIG5hbWUgYW5kIHJlbGF0ZWQgZGlyZWN0aXZlIGluZGV4XG4gKlxuICogVGhlIHZhbHVlIG11c3QgYmUgYW4gYXJyYXkgdG8gc3VwcG9ydCBpbnB1dHMgYW5kIG91dHB1dHMgd2l0aCB0aGUgc2FtZSBuYW1lXG4gKiBvbiB0aGUgc2FtZSBub2RlLlxuICovXG5leHBvcnQgdHlwZSBQcm9wZXJ0eUFsaWFzZXMgPSB7XG4gIC8vIFRoaXMgdXNlcyBhbiBvYmplY3QgbWFwIGJlY2F1c2UgdXNpbmcgdGhlIE1hcCB0eXBlIHdvdWxkIGJlIHRvbyBzbG93XG4gIFtrZXk6IHN0cmluZ106IFByb3BlcnR5QWxpYXNWYWx1ZVxufTtcblxuLyoqXG4gKiBTdG9yZSB0aGUgcnVudGltZSBpbnB1dCBvciBvdXRwdXQgbmFtZXMgZm9yIGFsbCB0aGUgZGlyZWN0aXZlcy5cbiAqXG4gKiBpKzA6IGRpcmVjdGl2ZSBpbnN0YW5jZSBpbmRleFxuICogaSsxOiBwcml2YXRlTmFtZVxuICpcbiAqIGUuZy4gWzAsICdjaGFuZ2UtbWluaWZpZWQnXVxuICovXG5leHBvcnQgdHlwZSBQcm9wZXJ0eUFsaWFzVmFsdWUgPSAobnVtYmVyfHN0cmluZylbXTtcblxuLyoqXG4gKiBUaGlzIGFycmF5IGNvbnRhaW5zIGluZm9ybWF0aW9uIGFib3V0IGlucHV0IHByb3BlcnRpZXMgdGhhdFxuICogbmVlZCB0byBiZSBzZXQgb25jZSBmcm9tIGF0dHJpYnV0ZSBkYXRhLiBJdCdzIG9yZGVyZWQgYnlcbiAqIGRpcmVjdGl2ZSBpbmRleCAocmVsYXRpdmUgdG8gZWxlbWVudCkgc28gaXQncyBzaW1wbGUgdG9cbiAqIGxvb2sgdXAgYSBzcGVjaWZpYyBkaXJlY3RpdmUncyBpbml0aWFsIGlucHV0IGRhdGEuXG4gKlxuICogV2l0aGluIGVhY2ggc3ViLWFycmF5OlxuICpcbiAqIGkrMDogYXR0cmlidXRlIG5hbWVcbiAqIGkrMTogbWluaWZpZWQvaW50ZXJuYWwgaW5wdXQgbmFtZVxuICogaSsyOiBpbml0aWFsIHZhbHVlXG4gKlxuICogSWYgYSBkaXJlY3RpdmUgb24gYSBub2RlIGRvZXMgbm90IGhhdmUgYW55IGlucHV0IHByb3BlcnRpZXNcbiAqIHRoYXQgc2hvdWxkIGJlIHNldCBmcm9tIGF0dHJpYnV0ZXMsIGl0cyBpbmRleCBpcyBzZXQgdG8gbnVsbFxuICogdG8gYXZvaWQgYSBzcGFyc2UgYXJyYXkuXG4gKlxuICogZS5nLiBbbnVsbCwgWydyb2xlLW1pbicsICdtaW5pZmllZC1pbnB1dCcsICdidXR0b24nXV1cbiAqL1xuZXhwb3J0IHR5cGUgSW5pdGlhbElucHV0RGF0YSA9IChJbml0aWFsSW5wdXRzfG51bGwpW107XG5cbi8qKlxuICogVXNlZCBieSBJbml0aWFsSW5wdXREYXRhIHRvIHN0b3JlIGlucHV0IHByb3BlcnRpZXNcbiAqIHRoYXQgc2hvdWxkIGJlIHNldCBvbmNlIGZyb20gYXR0cmlidXRlcy5cbiAqXG4gKiBpKzA6IGF0dHJpYnV0ZSBuYW1lXG4gKiBpKzE6IG1pbmlmaWVkL2ludGVybmFsIGlucHV0IG5hbWVcbiAqIGkrMjogaW5pdGlhbCB2YWx1ZVxuICpcbiAqIGUuZy4gWydyb2xlLW1pbicsICdtaW5pZmllZC1pbnB1dCcsICdidXR0b24nXVxuICovXG5leHBvcnQgdHlwZSBJbml0aWFsSW5wdXRzID0gc3RyaW5nW107XG5cbi8vIE5vdGU6IFRoaXMgaGFjayBpcyBuZWNlc3Nhcnkgc28gd2UgZG9uJ3QgZXJyb25lb3VzbHkgZ2V0IGEgY2lyY3VsYXIgZGVwZW5kZW5jeVxuLy8gZmFpbHVyZSBiYXNlZCBvbiB0eXBlcy5cbmV4cG9ydCBjb25zdCB1bnVzZWRWYWx1ZUV4cG9ydFRvUGxhY2F0ZUFqZCA9IDE7XG5cbi8qKlxuICogVHlwZSByZXByZXNlbnRpbmcgYSBzZXQgb2YgVE5vZGVzIHRoYXQgY2FuIGhhdmUgbG9jYWwgcmVmcyAoYCNmb29gKSBwbGFjZWQgb24gdGhlbS5cbiAqL1xuZXhwb3J0IHR5cGUgVE5vZGVXaXRoTG9jYWxSZWZzID0gVENvbnRhaW5lck5vZGV8VEVsZW1lbnROb2RlfFRFbGVtZW50Q29udGFpbmVyTm9kZTtcblxuLyoqXG4gKiBUeXBlIGZvciBhIGZ1bmN0aW9uIHRoYXQgZXh0cmFjdHMgYSB2YWx1ZSBmb3IgYSBsb2NhbCByZWZzLlxuICogRXhhbXBsZTpcbiAqIC0gYDxkaXYgI25hdGl2ZURpdkVsPmAgLSBgbmF0aXZlRGl2RWxgIHNob3VsZCBwb2ludCB0byB0aGUgbmF0aXZlIGA8ZGl2PmAgZWxlbWVudDtcbiAqIC0gYDxuZy10ZW1wbGF0ZSAjdHBsUmVmPmAgLSBgdHBsUmVmYCBzaG91bGQgcG9pbnQgdG8gdGhlIGBUZW1wbGF0ZVJlZmAgaW5zdGFuY2U7XG4gKi9cbmV4cG9ydCB0eXBlIExvY2FsUmVmRXh0cmFjdG9yID0gKHROb2RlOiBUTm9kZVdpdGhMb2NhbFJlZnMsIGN1cnJlbnRWaWV3OiBMVmlldykgPT4gYW55O1xuXG4vKipcbiAqIFJldHVybnMgYHRydWVgIGlmIHRoZSBgVE5vZGVgIGhhcyBhIGRpcmVjdGl2ZSB3aGljaCBoYXMgYEBJbnB1dCgpYCBmb3IgYGNsYXNzYCBiaW5kaW5nLlxuICpcbiAqIGBgYFxuICogPGRpdiBteS1kaXIgW2NsYXNzXT1cImV4cFwiPjwvZGl2PlxuICogYGBgXG4gKiBhbmRcbiAqIGBgYFxuICogQERpcmVjdGl2ZSh7XG4gKiB9KVxuICogY2xhc3MgTXlEaXJlY3RpdmUge1xuICogICBASW5wdXQoKVxuICogICBjbGFzczogc3RyaW5nO1xuICogfVxuICogYGBgXG4gKlxuICogSW4gdGhlIGFib3ZlIGNhc2UgaXQgaXMgbmVjZXNzYXJ5IHRvIHdyaXRlIHRoZSByZWNvbmNpbGVkIHN0eWxpbmcgaW5mb3JtYXRpb24gaW50byB0aGVcbiAqIGRpcmVjdGl2ZSdzIGlucHV0LlxuICpcbiAqIEBwYXJhbSB0Tm9kZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzQ2xhc3NJbnB1dCh0Tm9kZTogVE5vZGUpIHtcbiAgcmV0dXJuICh0Tm9kZS5mbGFncyAmIFROb2RlRmxhZ3MuaGFzQ2xhc3NJbnB1dCkgIT09IDA7XG59XG5cbi8qKlxuICogUmV0dXJucyBgdHJ1ZWAgaWYgdGhlIGBUTm9kZWAgaGFzIGEgZGlyZWN0aXZlIHdoaWNoIGhhcyBgQElucHV0KClgIGZvciBgc3R5bGVgIGJpbmRpbmcuXG4gKlxuICogYGBgXG4gKiA8ZGl2IG15LWRpciBbc3R5bGVdPVwiZXhwXCI+PC9kaXY+XG4gKiBgYGBcbiAqIGFuZFxuICogYGBgXG4gKiBARGlyZWN0aXZlKHtcbiAqIH0pXG4gKiBjbGFzcyBNeURpcmVjdGl2ZSB7XG4gKiAgIEBJbnB1dCgpXG4gKiAgIGNsYXNzOiBzdHJpbmc7XG4gKiB9XG4gKiBgYGBcbiAqXG4gKiBJbiB0aGUgYWJvdmUgY2FzZSBpdCBpcyBuZWNlc3NhcnkgdG8gd3JpdGUgdGhlIHJlY29uY2lsZWQgc3R5bGluZyBpbmZvcm1hdGlvbiBpbnRvIHRoZVxuICogZGlyZWN0aXZlJ3MgaW5wdXQuXG4gKlxuICogQHBhcmFtIHROb2RlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYXNTdHlsZUlucHV0KHROb2RlOiBUTm9kZSkge1xuICByZXR1cm4gKHROb2RlLmZsYWdzICYgVE5vZGVGbGFncy5oYXNTdHlsZUlucHV0KSAhPT0gMDtcbn1cbiJdfQ==