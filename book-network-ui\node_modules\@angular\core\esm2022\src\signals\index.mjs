/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { defaultEquals, isSignal, SIGNAL } from './src/api';
export { computed } from './src/computed';
export { setThrowInvalidWriteToSignalError } from './src/errors';
export { consumerAfterComputation, consumerBeforeComputation, consumerDestroy, producerAccessed, producerNotifyConsumers, producerUpdatesAllowed, producerUpdateValueVersion, REACTIVE_NODE, setActiveConsumer } from './src/graph';
export { setPostSignalSetFn, signal } from './src/signal';
export { untracked } from './src/untracked';
export { watch } from './src/watch';
export { setAlternateWeakRefImpl } from './src/weak_ref';
//# sourceMappingURL=data:application/json;base64,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