package com.hospital.pharmacist;

import com.hospital.common.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pharmacist")
public class Pharmacist extends BaseEntity {

    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    private String phoneNumber;
    private String licenseNumber;
    private String specialization; // Clinical Pharmacy, Hospital Pharmacy, etc.
    private String department;
    private Integer yearsOfExperience;
    private String shift; // DAY, NIGHT, ROTATING
    
    @Enumerated(EnumType.STRING)
    private PharmacistStatus status;
    
    // Additional pharmacy-specific fields
    private String certifications; // Board certifications
    private String pharmacyLocation; // Main Pharmacy, ICU Pharmacy, etc.
    private Boolean canDispenseControlledSubstances;
    private String supervisorId; // Reference to supervising pharmacist

    public String getFullName() {
        return firstName + " " + lastName;
    }
}

enum PharmacistStatus {
    ACTIVE,
    INACTIVE,
    ON_LEAVE,
    SUSPENDED,
    RETIRED
}
