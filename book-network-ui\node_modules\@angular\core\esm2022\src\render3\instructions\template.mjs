/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { validateMatchingNode, validateNodeExists } from '../../hydration/error_handling';
import { TEMPLATES } from '../../hydration/interfaces';
import { locateNextRNode, siblingAfter } from '../../hydration/node_lookup_utils';
import { calcSerializedContainerSize, isDisconnectedNode, markRNodeAsClaimedByHydration, setSegmentHead } from '../../hydration/utils';
import { assertEqual } from '../../util/assert';
import { assertFirstCreatePass } from '../assert';
import { attachPatchData } from '../context_discovery';
import { registerPostOrderHooks } from '../hooks';
import { isDirectiveHost } from '../interfaces/type_checks';
import { HEADER_OFFSET, HYDRATION, RENDERER } from '../interfaces/view';
import { appendChild } from '../node_manipulation';
import { getLView, getTView, isInSkipHydrationBlock, lastNodeWasCreated, setCurrentTNode, wasLastNodeCreated } from '../state';
import { getConstant } from '../util/view_utils';
import { addToViewTree, createDirectivesInstances, createLContainer, createTView, getOrCreateTNode, resolveDirectives, saveResolvedLocalsInData } from './shared';
function templateFirstCreatePass(index, tView, lView, templateFn, decls, vars, tagName, attrsIndex, localRefsIndex) {
    ngDevMode && assertFirstCreatePass(tView);
    ngDevMode && ngDevMode.firstCreatePass++;
    const tViewConsts = tView.consts;
    // TODO(pk): refactor getOrCreateTNode to have the "create" only version
    const tNode = getOrCreateTNode(tView, index, 4 /* TNodeType.Container */, tagName || null, getConstant(tViewConsts, attrsIndex));
    resolveDirectives(tView, lView, tNode, getConstant(tViewConsts, localRefsIndex));
    registerPostOrderHooks(tView, tNode);
    const embeddedTView = tNode.tView = createTView(2 /* TViewType.Embedded */, tNode, templateFn, decls, vars, tView.directiveRegistry, tView.pipeRegistry, null, tView.schemas, tViewConsts, null /* ssrId */);
    if (tView.queries !== null) {
        tView.queries.template(tView, tNode);
        embeddedTView.queries = tView.queries.embeddedTView(tNode);
    }
    return tNode;
}
/**
 * Creates an LContainer for an ng-template (dynamically-inserted view), e.g.
 *
 * <ng-template #foo>
 *    <div></div>
 * </ng-template>
 *
 * @param index The index of the container in the data array
 * @param templateFn Inline template
 * @param decls The number of nodes, local refs, and pipes for this template
 * @param vars The number of bindings for this template
 * @param tagName The name of the container element, if applicable
 * @param attrsIndex Index of template attributes in the `consts` array.
 * @param localRefs Index of the local references in the `consts` array.
 * @param localRefExtractor A function which extracts local-refs values from the template.
 *        Defaults to the current element associated with the local-ref.
 *
 * @codeGenApi
 */
export function ɵɵtemplate(index, templateFn, decls, vars, tagName, attrsIndex, localRefsIndex, localRefExtractor) {
    const lView = getLView();
    const tView = getTView();
    const adjustedIndex = index + HEADER_OFFSET;
    const tNode = tView.firstCreatePass ? templateFirstCreatePass(adjustedIndex, tView, lView, templateFn, decls, vars, tagName, attrsIndex, localRefsIndex) :
        tView.data[adjustedIndex];
    setCurrentTNode(tNode, false);
    const comment = _locateOrCreateContainerAnchor(tView, lView, tNode, index);
    if (wasLastNodeCreated()) {
        appendChild(tView, lView, comment, tNode);
    }
    attachPatchData(comment, lView);
    addToViewTree(lView, lView[adjustedIndex] = createLContainer(comment, lView, comment, tNode));
    if (isDirectiveHost(tNode)) {
        createDirectivesInstances(tView, lView, tNode);
    }
    if (localRefsIndex != null) {
        saveResolvedLocalsInData(lView, tNode, localRefExtractor);
    }
}
let _locateOrCreateContainerAnchor = createContainerAnchorImpl;
/**
 * Regular creation mode for LContainers and their anchor (comment) nodes.
 */
function createContainerAnchorImpl(tView, lView, tNode, index) {
    lastNodeWasCreated(true);
    return lView[RENDERER].createComment(ngDevMode ? 'container' : '');
}
/**
 * Enables hydration code path (to lookup existing elements in DOM)
 * in addition to the regular creation mode for LContainers and their
 * anchor (comment) nodes.
 */
function locateOrCreateContainerAnchorImpl(tView, lView, tNode, index) {
    const hydrationInfo = lView[HYDRATION];
    const isNodeCreationMode = !hydrationInfo || isInSkipHydrationBlock() || isDisconnectedNode(hydrationInfo, index);
    lastNodeWasCreated(isNodeCreationMode);
    // Regular creation mode.
    if (isNodeCreationMode) {
        return createContainerAnchorImpl(tView, lView, tNode, index);
    }
    const ssrId = hydrationInfo.data[TEMPLATES]?.[index] ?? null;
    // Apply `ssrId` value to the underlying TView if it was not previously set.
    //
    // There might be situations when the same component is present in a template
    // multiple times and some instances are opted-out of using hydration via
    // `ngSkipHydration` attribute. In this scenario, at the time a TView is created,
    // the `ssrId` might be `null` (if the first component is opted-out of hydration).
    // The code below makes sure that the `ssrId` is applied to the TView if it's still
    // `null` and verifies we never try to override it with a different value.
    if (ssrId !== null && tNode.tView !== null) {
        if (tNode.tView.ssrId === null) {
            tNode.tView.ssrId = ssrId;
        }
        else {
            ngDevMode &&
                assertEqual(tNode.tView.ssrId, ssrId, 'Unexpected value of the `ssrId` for this TView');
        }
    }
    // Hydration mode, looking up existing elements in DOM.
    const currentRNode = locateNextRNode(hydrationInfo, tView, lView, tNode);
    ngDevMode && validateNodeExists(currentRNode, lView, tNode);
    setSegmentHead(hydrationInfo, index, currentRNode);
    const viewContainerSize = calcSerializedContainerSize(hydrationInfo, index);
    const comment = siblingAfter(viewContainerSize, currentRNode);
    if (ngDevMode) {
        validateMatchingNode(comment, Node.COMMENT_NODE, null, lView, tNode);
        markRNodeAsClaimedByHydration(comment);
    }
    return comment;
}
export function enableLocateOrCreateContainerAnchorImpl() {
    _locateOrCreateContainerAnchor = locateOrCreateContainerAnchorImpl;
}
//# sourceMappingURL=data:application/json;base64,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