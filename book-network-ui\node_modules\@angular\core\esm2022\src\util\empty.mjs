/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { initNgDevMode } from './ng_dev_mode';
/**
 * This file contains reuseable "empty" symbols that can be used as default return values
 * in different parts of the rendering code. Because the same symbols are returned, this
 * allows for identity checks against these values to be consistently used by the framework
 * code.
 */
export const EMPTY_OBJ = {};
export const EMPTY_ARRAY = [];
// freezing the values prevents any code from accidentally inserting new values in
if ((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode()) {
    // These property accesses can be ignored because ngDevMode will be set to false
    // when optimizing code and the whole if statement will be dropped.
    // tslint:disable-next-line:no-toplevel-property-access
    Object.freeze(EMPTY_OBJ);
    // tslint:disable-next-line:no-toplevel-property-access
    Object.freeze(EMPTY_ARRAY);
}
//# sourceMappingURL=data:application/json;base64,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