import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="container-fluid vh-100">
      <div class="row h-100">
        <div class="col-md-6 d-flex align-items-center justify-content-center bg-primary">
          <div class="text-center text-white">
            <h1 class="display-4 mb-4">
              <i class="fas fa-hospital-alt me-3"></i>
              Hospital Management System
            </h1>
            <p class="lead">Secure access for healthcare professionals</p>
          </div>
        </div>
        <div class="col-md-6 d-flex align-items-center justify-content-center">
          <div class="card shadow-lg" style="width: 400px;">
            <div class="card-body p-5">
              <div class="text-center mb-4">
                <h3 class="card-title">Welcome Back</h3>
                <p class="text-muted">Sign in to your account</p>
              </div>
              
              <div class="d-grid gap-2">
                <button 
                  class="btn btn-primary btn-lg"
                  (click)="login()"
                  [disabled]="isLoading">
                  <i class="fas fa-sign-in-alt me-2"></i>
                  <span *ngIf="!isLoading">Sign In</span>
                  <span *ngIf="isLoading">
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    Signing In...
                  </span>
                </button>
                
                <button 
                  class="btn btn-outline-secondary"
                  (click)="register()"
                  [disabled]="isLoading">
                  <i class="fas fa-user-plus me-2"></i>
                  Create Account
                </button>
              </div>
              
              <div class="mt-4 text-center">
                <small class="text-muted">
                  For healthcare professionals only
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .bg-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }
    
    .card {
      border: none;
      border-radius: 15px;
    }
    
    .btn {
      border-radius: 10px;
      padding: 12px 20px;
    }
    
    .fas {
      width: 20px;
    }
  `]
})
export class LoginComponent implements OnInit {
  isLoading = false;

  constructor(
    private keycloakService: KeycloakService,
    private router: Router
  ) {}

  async ngOnInit() {
    const isLoggedIn = await this.keycloakService.isLoggedIn();
    if (isLoggedIn) {
      this.router.navigate(['/dashboard']);
    }
  }

  async login() {
    this.isLoading = true;
    try {
      await this.keycloakService.login({
        redirectUri: window.location.origin + '/dashboard'
      });
    } catch (error) {
      console.error('Login failed:', error);
      this.isLoading = false;
    }
  }

  async register() {
    this.isLoading = true;
    try {
      await this.keycloakService.register({
        redirectUri: window.location.origin + '/dashboard'
      });
    } catch (error) {
      console.error('Registration failed:', error);
      this.isLoading = false;
    }
  }
}
