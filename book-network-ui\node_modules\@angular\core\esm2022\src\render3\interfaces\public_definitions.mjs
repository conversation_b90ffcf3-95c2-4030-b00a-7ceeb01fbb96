/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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