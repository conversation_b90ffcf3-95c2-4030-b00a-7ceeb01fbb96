/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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