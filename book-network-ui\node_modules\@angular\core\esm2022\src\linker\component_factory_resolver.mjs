/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { stringify } from '../util/stringify';
export function noComponentFactoryError(component) {
    const error = Error(`No component factory found for ${stringify(component)}.`);
    error[ERROR_COMPONENT] = component;
    return error;
}
const ERROR_COMPONENT = 'ngComponent';
export function getComponent(error) {
    return error[ERROR_COMPONENT];
}
class _NullComponentFactoryResolver {
    resolveComponentFactory(component) {
        throw noComponentFactoryError(component);
    }
}
/**
 * A simple registry that maps `Components` to generated `ComponentFactory` classes
 * that can be used to create instances of components.
 * Use to obtain the factory for a given component type,
 * then use the factory's `create()` method to create a component of that type.
 *
 * Note: since v13, dynamic component creation via
 * [`ViewContainerRef.createComponent`](api/core/ViewContainerRef#createComponent)
 * does **not** require resolving component factory: component class can be used directly.
 *
 * @publicApi
 *
 * @deprecated Angular no longer requires Component factories. Please use other APIs where
 *     Component class can be used directly.
 */
export class ComponentFactoryResolver {
    static { this.NULL = ( /* @__PURE__ */new _NullComponentFactoryResolver()); }
}
//# sourceMappingURL=data:application/json;base64,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