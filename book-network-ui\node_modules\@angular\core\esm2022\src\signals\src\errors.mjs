/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function defaultThrowError() {
    throw new Error();
}
let throwInvalidWriteToSignalErrorFn = defaultThrowError;
export function throwInvalidWriteToSignalError() {
    throwInvalidWriteToSignalErrorFn();
}
export function setThrowInvalidWriteToSignalError(fn) {
    throwInvalidWriteToSignalErrorFn = fn;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZXJyb3JzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29yZS9zcmMvc2lnbmFscy9zcmMvZXJyb3JzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILFNBQVMsaUJBQWlCO0lBQ3hCLE1BQU0sSUFBSSxLQUFLLEVBQUUsQ0FBQztBQUNwQixDQUFDO0FBRUQsSUFBSSxnQ0FBZ0MsR0FBRyxpQkFBaUIsQ0FBQztBQUV6RCxNQUFNLFVBQVUsOEJBQThCO0lBQzVDLGdDQUFnQyxFQUFFLENBQUM7QUFDckMsQ0FBQztBQUVELE1BQU0sVUFBVSxpQ0FBaUMsQ0FBQyxFQUFlO0lBQy9ELGdDQUFnQyxHQUFHLEVBQUUsQ0FBQztBQUN4QyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmZ1bmN0aW9uIGRlZmF1bHRUaHJvd0Vycm9yKCk6IG5ldmVyIHtcbiAgdGhyb3cgbmV3IEVycm9yKCk7XG59XG5cbmxldCB0aHJvd0ludmFsaWRXcml0ZVRvU2lnbmFsRXJyb3JGbiA9IGRlZmF1bHRUaHJvd0Vycm9yO1xuXG5leHBvcnQgZnVuY3Rpb24gdGhyb3dJbnZhbGlkV3JpdGVUb1NpZ25hbEVycm9yKCkge1xuICB0aHJvd0ludmFsaWRXcml0ZVRvU2lnbmFsRXJyb3JGbigpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gc2V0VGhyb3dJbnZhbGlkV3JpdGVUb1NpZ25hbEVycm9yKGZuOiAoKSA9PiBuZXZlcik6IHZvaWQge1xuICB0aHJvd0ludmFsaWRXcml0ZVRvU2lnbmFsRXJyb3JGbiA9IGZuO1xufVxuIl19