/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// Note: This hack is necessary so we don't erroneously get a circular dependency
// failure based on types.
export const unusedValueExportToPlacateAjd = 1;
//# sourceMappingURL=data:application/json;base64,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