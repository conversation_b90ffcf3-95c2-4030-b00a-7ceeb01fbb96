/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export class AsyncStackTaggingZoneSpec {
    constructor(namePrefix, consoleAsyncStackTaggingImpl = console) {
        this.name = 'asyncStackTagging for ' + namePrefix;
        this.createTask = consoleAsyncStackTaggingImpl?.createTask ?? (() => null);
    }
    onScheduleTask(delegate, _current, target, task) {
        task.consoleTask = this.createTask(`Zone - ${task.source || task.type}`);
        return delegate.scheduleTask(target, task);
    }
    onInvokeTask(delegate, _currentZone, targetZone, task, applyThis, applyArgs) {
        let ret;
        if (task.consoleTask) {
            ret = task.consoleTask.run(() => delegate.invokeTask(targetZone, task, applyThis, applyArgs));
        }
        else {
            ret = delegate.invokeTask(targetZone, task, applyThis, applyArgs);
        }
        return ret;
    }
}
//# sourceMappingURL=data:application/json;base64,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