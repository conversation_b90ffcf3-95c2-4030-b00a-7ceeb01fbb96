/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { invalidSkipHydrationHost, validateMatchingNode, validateNodeExists } from '../../hydration/error_handling';
import { locateNextRNode } from '../../hydration/node_lookup_utils';
import { hasSkipHydrationAttrOnRElement, hasSkipHydrationAttrOnTNode } from '../../hydration/skip_hydration';
import { getSerializedContainerViews, isDisconnectedNode, markRNodeAsClaimedByHydration, setSegmentHead } from '../../hydration/utils';
import { assertDefined, assertEqual, assertIndexInRange } from '../../util/assert';
import { assertFirstCreatePass, assertHasParent } from '../assert';
import { attachPatchData } from '../context_discovery';
import { registerPostOrderHooks } from '../hooks';
import { hasClassInput, hasStyleInput } from '../interfaces/node';
import { isComponentHost, isContentQueryHost, isDirectiveHost } from '../interfaces/type_checks';
import { HEADER_OFFSET, HYDRATION, RENDERER } from '../interfaces/view';
import { assertTNodeType } from '../node_assert';
import { appendChild, clearElementContents, createElementNode, setupStaticAttributes } from '../node_manipulation';
import { decreaseElementDepthCount, enterSkipHydrationBlock, getBindingIndex, getCurrentTNode, getElementDepthCount, getLView, getNamespace, getTView, increaseElementDepthCount, isCurrentTNodeParent, isInSkipHydrationBlock, isSkipHydrationRootTNode, lastNodeWasCreated, leaveSkipHydrationBlock, setCurrentTNode, setCurrentTNodeAsNotParent, wasLastNodeCreated } from '../state';
import { computeStaticStyling } from '../styling/static_styling';
import { getConstant } from '../util/view_utils';
import { validateElementIsKnown } from './element_validation';
import { setDirectiveInputsWhichShadowsStyling } from './property';
import { createDirectivesInstances, executeContentQueries, getOrCreateTNode, resolveDirectives, saveResolvedLocalsInData } from './shared';
function elementStartFirstCreatePass(index, tView, lView, name, attrsIndex, localRefsIndex) {
    ngDevMode && assertFirstCreatePass(tView);
    ngDevMode && ngDevMode.firstCreatePass++;
    const tViewConsts = tView.consts;
    const attrs = getConstant(tViewConsts, attrsIndex);
    const tNode = getOrCreateTNode(tView, index, 2 /* TNodeType.Element */, name, attrs);
    resolveDirectives(tView, lView, tNode, getConstant(tViewConsts, localRefsIndex));
    if (tNode.attrs !== null) {
        computeStaticStyling(tNode, tNode.attrs, false);
    }
    if (tNode.mergedAttrs !== null) {
        computeStaticStyling(tNode, tNode.mergedAttrs, true);
    }
    if (tView.queries !== null) {
        tView.queries.elementStart(tView, tNode);
    }
    return tNode;
}
/**
 * Create DOM element. The instruction must later be followed by `elementEnd()` call.
 *
 * @param index Index of the element in the LView array
 * @param name Name of the DOM Node
 * @param attrsIndex Index of the element's attributes in the `consts` array.
 * @param localRefsIndex Index of the element's local references in the `consts` array.
 * @returns This function returns itself so that it may be chained.
 *
 * Attributes and localRefs are passed as an array of strings where elements with an even index
 * hold an attribute name and elements with an odd index hold an attribute value, ex.:
 * ['id', 'warning5', 'class', 'alert']
 *
 * @codeGenApi
 */
export function ɵɵelementStart(index, name, attrsIndex, localRefsIndex) {
    const lView = getLView();
    const tView = getTView();
    const adjustedIndex = HEADER_OFFSET + index;
    ngDevMode &&
        assertEqual(getBindingIndex(), tView.bindingStartIndex, 'elements should be created before any bindings');
    ngDevMode && assertIndexInRange(lView, adjustedIndex);
    const renderer = lView[RENDERER];
    const tNode = tView.firstCreatePass ?
        elementStartFirstCreatePass(adjustedIndex, tView, lView, name, attrsIndex, localRefsIndex) :
        tView.data[adjustedIndex];
    const native = _locateOrCreateElementNode(tView, lView, tNode, renderer, name, index);
    lView[adjustedIndex] = native;
    const hasDirectives = isDirectiveHost(tNode);
    if (ngDevMode && tView.firstCreatePass) {
        validateElementIsKnown(native, lView, tNode.value, tView.schemas, hasDirectives);
    }
    setCurrentTNode(tNode, true);
    setupStaticAttributes(renderer, native, tNode);
    if ((tNode.flags & 32 /* TNodeFlags.isDetached */) !== 32 /* TNodeFlags.isDetached */ && wasLastNodeCreated()) {
        // In the i18n case, the translation may have removed this element, so only add it if it is not
        // detached. See `TNodeType.Placeholder` and `LFrame.inI18n` for more context.
        appendChild(tView, lView, native, tNode);
    }
    // any immediate children of a component or template container must be pre-emptively
    // monkey-patched with the component view data so that the element can be inspected
    // later on using any element discovery utility methods (see `element_discovery.ts`)
    if (getElementDepthCount() === 0) {
        attachPatchData(native, lView);
    }
    increaseElementDepthCount();
    if (hasDirectives) {
        createDirectivesInstances(tView, lView, tNode);
        executeContentQueries(tView, tNode, lView);
    }
    if (localRefsIndex !== null) {
        saveResolvedLocalsInData(lView, tNode);
    }
    return ɵɵelementStart;
}
/**
 * Mark the end of the element.
 * @returns This function returns itself so that it may be chained.
 *
 * @codeGenApi
 */
export function ɵɵelementEnd() {
    let currentTNode = getCurrentTNode();
    ngDevMode && assertDefined(currentTNode, 'No parent node to close.');
    if (isCurrentTNodeParent()) {
        setCurrentTNodeAsNotParent();
    }
    else {
        ngDevMode && assertHasParent(getCurrentTNode());
        currentTNode = currentTNode.parent;
        setCurrentTNode(currentTNode, false);
    }
    const tNode = currentTNode;
    ngDevMode && assertTNodeType(tNode, 3 /* TNodeType.AnyRNode */);
    if (isSkipHydrationRootTNode(tNode)) {
        leaveSkipHydrationBlock();
    }
    decreaseElementDepthCount();
    const tView = getTView();
    if (tView.firstCreatePass) {
        registerPostOrderHooks(tView, currentTNode);
        if (isContentQueryHost(currentTNode)) {
            tView.queries.elementEnd(currentTNode);
        }
    }
    if (tNode.classesWithoutHost != null && hasClassInput(tNode)) {
        setDirectiveInputsWhichShadowsStyling(tView, tNode, getLView(), tNode.classesWithoutHost, true);
    }
    if (tNode.stylesWithoutHost != null && hasStyleInput(tNode)) {
        setDirectiveInputsWhichShadowsStyling(tView, tNode, getLView(), tNode.stylesWithoutHost, false);
    }
    return ɵɵelementEnd;
}
/**
 * Creates an empty element using {@link elementStart} and {@link elementEnd}
 *
 * @param index Index of the element in the data array
 * @param name Name of the DOM Node
 * @param attrsIndex Index of the element's attributes in the `consts` array.
 * @param localRefsIndex Index of the element's local references in the `consts` array.
 * @returns This function returns itself so that it may be chained.
 *
 * @codeGenApi
 */
export function ɵɵelement(index, name, attrsIndex, localRefsIndex) {
    ɵɵelementStart(index, name, attrsIndex, localRefsIndex);
    ɵɵelementEnd();
    return ɵɵelement;
}
let _locateOrCreateElementNode = (tView, lView, tNode, renderer, name, index) => {
    lastNodeWasCreated(true);
    return createElementNode(renderer, name, getNamespace());
};
/**
 * Enables hydration code path (to lookup existing elements in DOM)
 * in addition to the regular creation mode of element nodes.
 */
function locateOrCreateElementNodeImpl(tView, lView, tNode, renderer, name, index) {
    const hydrationInfo = lView[HYDRATION];
    const isNodeCreationMode = !hydrationInfo || isInSkipHydrationBlock() || isDisconnectedNode(hydrationInfo, index);
    lastNodeWasCreated(isNodeCreationMode);
    // Regular creation mode.
    if (isNodeCreationMode) {
        return createElementNode(renderer, name, getNamespace());
    }
    // Hydration mode, looking up an existing element in DOM.
    const native = locateNextRNode(hydrationInfo, tView, lView, tNode);
    ngDevMode && validateMatchingNode(native, Node.ELEMENT_NODE, name, lView, tNode);
    ngDevMode && markRNodeAsClaimedByHydration(native);
    // This element might also be an anchor of a view container.
    if (getSerializedContainerViews(hydrationInfo, index)) {
        // Important note: this element acts as an anchor, but it's **not** a part
        // of the embedded view, so we start the segment **after** this element, taking
        // a reference to the next sibling. For example, the following template:
        // `<div #vcrTarget>` is represented in the DOM as `<div></div>...<!--container-->`,
        // so while processing a `<div>` instruction, point to the next sibling as a
        // start of a segment.
        ngDevMode && validateNodeExists(native.nextSibling, lView, tNode);
        setSegmentHead(hydrationInfo, index, native.nextSibling);
    }
    // Checks if the skip hydration attribute is present during hydration so we know to
    // skip attempting to hydrate this block. We check both TNode and RElement for an
    // attribute: the RElement case is needed for i18n cases, when we add it to host
    // elements during the annotation phase (after all internal data structures are setup).
    if (hydrationInfo &&
        (hasSkipHydrationAttrOnTNode(tNode) || hasSkipHydrationAttrOnRElement(native))) {
        if (isComponentHost(tNode)) {
            enterSkipHydrationBlock(tNode);
            // Since this isn't hydratable, we need to empty the node
            // so there's no duplicate content after render
            clearElementContents(native);
            ngDevMode && ngDevMode.componentsSkippedHydration++;
        }
        else if (ngDevMode) {
            // If this is not a component host, throw an error.
            // Hydration can be skipped on per-component basis only.
            throw invalidSkipHydrationHost(native);
        }
    }
    return native;
}
export function enableLocateOrCreateElementNodeImpl() {
    _locateOrCreateElementNode = locateOrCreateElementNodeImpl;
}
//# sourceMappingURL=data:application/json;base64,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