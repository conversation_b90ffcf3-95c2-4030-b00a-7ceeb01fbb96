/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isComponentHost } from '../render3/interfaces/type_checks';
import { DECLARATION_COMPONENT_VIEW } from '../render3/interfaces/view';
import { getCurrentTNode, getLView } from '../render3/state';
import { getComponentLViewByIndex } from '../render3/util/view_utils';
import { ViewRef } from '../render3/view_ref';
/**
 * Base class that provides change detection functionality.
 * A change-detection tree collects all views that are to be checked for changes.
 * Use the methods to add and remove views from the tree, initiate change-detection,
 * and explicitly mark views as _dirty_, meaning that they have changed and need to be re-rendered.
 *
 * @see [Using change detection hooks](guide/lifecycle-hooks#using-change-detection-hooks)
 * @see [Defining custom change detection](guide/lifecycle-hooks#defining-custom-change-detection)
 *
 * @usageNotes
 *
 * The following examples demonstrate how to modify default change-detection behavior
 * to perform explicit detection when needed.
 *
 * ### Use `markForCheck()` with `CheckOnce` strategy
 *
 * The following example sets the `OnPush` change-detection strategy for a component
 * (`CheckOnce`, rather than the default `CheckAlways`), then forces a second check
 * after an interval.
 *
 * <code-example path="core/ts/change_detect/change-detection.ts"
 * region="mark-for-check"></code-example>
 *
 * ### Detach change detector to limit how often check occurs
 *
 * The following example defines a component with a large list of read-only data
 * that is expected to change constantly, many times per second.
 * To improve performance, we want to check and update the list
 * less often than the changes actually occur. To do that, we detach
 * the component's change detector and perform an explicit local check every five seconds.
 *
 * <code-example path="core/ts/change_detect/change-detection.ts" region="detach"></code-example>
 *
 *
 * ### Reattaching a detached component
 *
 * The following example creates a component displaying live data.
 * The component detaches its change detector from the main change detector tree
 * when the `live` property is set to false, and reattaches it when the property
 * becomes true.
 *
 * <code-example path="core/ts/change_detect/change-detection.ts" region="reattach"></code-example>
 *
 * @publicApi
 */
export class ChangeDetectorRef {
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ELEMENT_ID__ = injectChangeDetectorRef; }
}
/** Returns a ChangeDetectorRef (a.k.a. a ViewRef) */
export function injectChangeDetectorRef(flags) {
    return createViewRef(getCurrentTNode(), getLView(), (flags & 16 /* InternalInjectFlags.ForPipe */) === 16 /* InternalInjectFlags.ForPipe */);
}
/**
 * Creates a ViewRef and stores it on the injector as ChangeDetectorRef (public alias).
 *
 * @param tNode The node that is requesting a ChangeDetectorRef
 * @param lView The view to which the node belongs
 * @param isPipe Whether the view is being injected into a pipe.
 * @returns The ChangeDetectorRef to use
 */
function createViewRef(tNode, lView, isPipe) {
    if (isComponentHost(tNode) && !isPipe) {
        // The LView represents the location where the component is declared.
        // Instead we want the LView for the component View and so we need to look it up.
        const componentView = getComponentLViewByIndex(tNode.index, lView); // look down
        return new ViewRef(componentView, componentView);
    }
    else if (tNode.type & (3 /* TNodeType.AnyRNode */ | 12 /* TNodeType.AnyContainer */ | 32 /* TNodeType.Icu */)) {
        // The LView represents the location where the injection is requested from.
        // We need to locate the containing LView (in case where the `lView` is an embedded view)
        const hostComponentView = lView[DECLARATION_COMPONENT_VIEW]; // look up
        return new ViewRef(hostComponentView, lView);
    }
    return null;
}
//# sourceMappingURL=data:application/json;base64,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